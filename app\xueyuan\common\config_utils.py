# coding:utf-8
"""
学习工具配置工具模块

该模块提供学习工具配置的便捷访问和管理功能，
包括配置验证、配置迁移、配置导入导出等实用工具。

主要功能：
- 配置值验证和转换
- 配置文件迁移
- 配置导入导出
- 配置重置和恢复
- 配置变更监听

类和函数说明：
- ConfigUtils: 配置工具类
- validate_config(): 验证配置有效性
- migrate_config(): 迁移配置文件
- export_config(): 导出配置
- import_config(): 导入配置
"""

import json
import os
import shutil
from pathlib import Path
from typing import Dict, Any, Optional, List
from datetime import datetime

from .config import study_cfg


class ConfigUtils:
    """学习工具配置工具类"""
    
    @staticmethod
    def validate_config(config_data: Dict[str, Any]) -> tuple[bool, List[str]]:
        """
        验证配置数据的有效性
        
        Args:
            config_data: 配置数据字典
            
        Returns:
            tuple: (是否有效, 错误信息列表)
        """
        errors = []
        
        # 验证必需的配置节
        required_sections = ["System", "Browser", "OCR", "API", "Website", "Logging"]
        for section in required_sections:
            if section not in config_data:
                errors.append(f"缺少必需的配置节: {section}")
        
        # 验证系统配置
        if "System" in config_data:
            system_config = config_data["System"]
            
            # 验证并发数量
            if "ConcurrentCount" in system_config:
                count = system_config["ConcurrentCount"]
                if not isinstance(count, int) or count < 1 or count > 10:
                    errors.append("并发数量必须是1-10之间的整数")
            
            # 验证课程数量
            for course_type in ["CompulsoryCourses", "ElectiveCourses"]:
                if course_type in system_config:
                    count = system_config[course_type]
                    if not isinstance(count, int) or count < 1 or count > 100:
                        errors.append(f"{course_type}必须是1-100之间的整数")
        
        # 验证浏览器配置
        if "Browser" in config_data:
            browser_config = config_data["Browser"]
            
            # 验证浏览器类型
            if "BrowserType" in browser_config:
                browser_type = browser_config["BrowserType"]
                valid_browsers = ["chrome", "firefox", "edge", "webkit"]
                if browser_type not in valid_browsers:
                    errors.append(f"浏览器类型必须是以下之一: {', '.join(valid_browsers)}")
            
            # 验证窗口尺寸
            if "WindowWidth" in browser_config:
                width = browser_config["WindowWidth"]
                if not isinstance(width, int) or width < 800 or width > 3840:
                    errors.append("窗口宽度必须是800-3840之间的整数")
            
            if "WindowHeight" in browser_config:
                height = browser_config["WindowHeight"]
                if not isinstance(height, int) or height < 600 or height > 2160:
                    errors.append("窗口高度必须是600-2160之间的整数")
        
        # 验证OCR配置
        if "OCR" in config_data:
            ocr_config = config_data["OCR"]
            
            # 验证OCR引擎
            if "PrimaryEngine" in ocr_config:
                engine = ocr_config["PrimaryEngine"]
                valid_engines = ["ddddocr", "baidu", "tesseract", "paddleocr"]
                if engine not in valid_engines:
                    errors.append(f"OCR引擎必须是以下之一: {', '.join(valid_engines)}")
        
        # 验证网站配置
        if "Website" in config_data:
            website_config = config_data["Website"]
            
            # 验证URL格式
            for url_key in ["BaseUrl", "LoginUrl", "StudyUrl", "CourseListUrl"]:
                if url_key in website_config:
                    url = website_config[url_key]
                    if not isinstance(url, str) or not url.startswith(("http://", "https://")):
                        errors.append(f"{url_key}必须是有效的HTTP/HTTPS URL")
        
        return len(errors) == 0, errors
    
    @staticmethod
    def migrate_config(old_config_path: str, new_config_path: str) -> bool:
        """
        迁移配置文件到新版本
        
        Args:
            old_config_path: 旧配置文件路径
            new_config_path: 新配置文件路径
            
        Returns:
            bool: 迁移是否成功
        """
        try:
            # 读取旧配置
            with open(old_config_path, 'r', encoding='utf-8') as f:
                old_config = json.load(f)
            
            # 读取新配置模板
            template_path = Path(__file__).parent.parent / "config" / "study_config.json"
            with open(template_path, 'r', encoding='utf-8') as f:
                new_config = json.load(f)
            
            # 合并配置（保留旧配置的值，添加新配置的默认值）
            def merge_config(old_dict, new_dict):
                for key, value in new_dict.items():
                    if key in old_dict:
                        if isinstance(value, dict) and isinstance(old_dict[key], dict):
                            merge_config(old_dict[key], value)
                        # 保留旧值
                    else:
                        # 添加新的默认值
                        old_dict[key] = value
            
            merge_config(old_config, new_config)
            
            # 保存迁移后的配置
            with open(new_config_path, 'w', encoding='utf-8') as f:
                json.dump(old_config, f, indent=4, ensure_ascii=False)
            
            print(f"[配置] 配置文件迁移成功: {old_config_path} -> {new_config_path}")
            return True
            
        except Exception as e:
            print(f"[配置] 配置文件迁移失败: {e}")
            return False
    
    @staticmethod
    def export_config(export_path: str, include_sensitive: bool = False) -> bool:
        """
        导出配置到文件
        
        Args:
            export_path: 导出文件路径
            include_sensitive: 是否包含敏感信息（如API密钥）
            
        Returns:
            bool: 导出是否成功
        """
        try:
            # 获取当前配置
            config_data = {}
            
            # 遍历所有配置项
            for attr_name in dir(study_cfg):
                if not attr_name.startswith('_'):
                    attr = getattr(study_cfg, attr_name)
                    if hasattr(attr, 'value'):
                        # 获取配置节和键
                        section = attr.group
                        key = attr.name
                        
                        if section not in config_data:
                            config_data[section] = {}
                        
                        # 检查是否为敏感信息
                        if not include_sensitive and key.lower() in ['apikey', 'secretkey', 'password']:
                            config_data[section][key] = ""
                        else:
                            config_data[section][key] = attr.value
            
            # 保存到文件
            with open(export_path, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=4, ensure_ascii=False)
            
            print(f"[配置] 配置导出成功: {export_path}")
            return True
            
        except Exception as e:
            print(f"[配置] 配置导出失败: {e}")
            return False
    
    @staticmethod
    def import_config(import_path: str, validate: bool = True) -> bool:
        """
        从文件导入配置
        
        Args:
            import_path: 导入文件路径
            validate: 是否验证配置有效性
            
        Returns:
            bool: 导入是否成功
        """
        try:
            # 读取配置文件
            with open(import_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            # 验证配置
            if validate:
                is_valid, errors = ConfigUtils.validate_config(config_data)
                if not is_valid:
                    print(f"[配置] 配置验证失败: {'; '.join(errors)}")
                    return False
            
            # 应用配置
            for section, section_data in config_data.items():
                for key, value in section_data.items():
                    # 查找对应的配置项
                    for attr_name in dir(study_cfg):
                        if not attr_name.startswith('_'):
                            attr = getattr(study_cfg, attr_name)
                            if hasattr(attr, 'group') and hasattr(attr, 'name'):
                                if attr.group == section and attr.name == key:
                                    attr.value = value
                                    break
            
            print(f"[配置] 配置导入成功: {import_path}")
            return True
            
        except Exception as e:
            print(f"[配置] 配置导入失败: {e}")
            return False
    
    @staticmethod
    def reset_config(section: Optional[str] = None) -> bool:
        """
        重置配置到默认值
        
        Args:
            section: 要重置的配置节，None表示重置所有
            
        Returns:
            bool: 重置是否成功
        """
        try:
            # 读取默认配置
            template_path = Path(__file__).parent.parent / "config" / "study_config.json"
            with open(template_path, 'r', encoding='utf-8') as f:
                default_config = json.load(f)
            
            # 重置指定节或所有节
            sections_to_reset = [section] if section else default_config.keys()
            
            for section_name in sections_to_reset:
                if section_name in default_config:
                    section_data = default_config[section_name]
                    for key, value in section_data.items():
                        # 查找对应的配置项
                        for attr_name in dir(study_cfg):
                            if not attr_name.startswith('_'):
                                attr = getattr(study_cfg, attr_name)
                                if hasattr(attr, 'group') and hasattr(attr, 'name'):
                                    if attr.group == section_name and attr.name == key:
                                        attr.value = value
                                        break
            
            print(f"[配置] 配置重置成功: {section or '全部'}")
            return True
            
        except Exception as e:
            print(f"[配置] 配置重置失败: {e}")
            return False
    
    @staticmethod
    def backup_config(backup_dir: str = "backups") -> Optional[str]:
        """
        备份当前配置
        
        Args:
            backup_dir: 备份目录
            
        Returns:
            str: 备份文件路径，失败返回None
        """
        try:
            # 创建备份目录
            backup_path = Path(backup_dir)
            backup_path.mkdir(parents=True, exist_ok=True)
            
            # 生成备份文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_file = backup_path / f"study_config_backup_{timestamp}.json"
            
            # 导出配置到备份文件
            if ConfigUtils.export_config(str(backup_file), include_sensitive=True):
                return str(backup_file)
            else:
                return None
                
        except Exception as e:
            print(f"[配置] 配置备份失败: {e}")
            return None


def get_config_summary() -> Dict[str, Any]:
    """获取配置摘要信息"""
    return {
        "system": {
            "async_login": study_cfg.asyncLogin.value,
            "concurrent_count": study_cfg.concurrentCount.value,
            "auto_start": study_cfg.autoStart.value
        },
        "browser": {
            "type": study_cfg.browserType.value,
            "headless": study_cfg.headless.value,
            "disable_images": study_cfg.disableImages.value
        },
        "ocr": {
            "primary_engine": study_cfg.primaryEngine.value,
            "fallback_engine": study_cfg.fallbackEngine.value
        },
        "study_strategy": {
            "mode": study_cfg.studyMode.value,
            "auto_next": study_cfg.autoNextCourse.value,
            "skip_completed": study_cfg.skipCompletedCourses.value
        }
    }


def is_config_valid() -> bool:
    """检查当前配置是否有效"""
    try:
        # 基本配置检查
        if not study_cfg.baseUrl.value:
            return False
        if not study_cfg.loginUrl.value:
            return False
        if study_cfg.concurrentCount.value < 1:
            return False

        return True
    except Exception:
        return False
