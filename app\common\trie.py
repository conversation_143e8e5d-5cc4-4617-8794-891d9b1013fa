# coding: utf-8
"""
字典树（Trie）数据结构模块

该模块实现了字典树数据结构，用于高效的字符串搜索和前缀匹配。
主要用于实现搜索建议、自动完成等功能。

主要功能：
- 支持字符串的插入和查找
- 提供前缀匹配搜索
- 支持获取所有匹配前缀的项目
- 仅支持英文字母（a-z）

类说明：
- Trie: 字典树类，提供字符串存储和搜索功能
"""

from queue import Queue


class Trie:
    """
    字典树（前缀树）数据结构

    用于高效存储和搜索字符串的树形数据结构，
    支持前缀匹配和快速查找功能。
    """

    def __init__(self):
        """
        初始化字典树节点
        """
        self.key = ''
        self.value = None
        self.children = [None] * 26  # 支持26个英文字母
        self.isEnd = False

    def insert(self, key: str, value):
        """
        插入键值对到字典树中

        Args:
            key (str): 要插入的键（字符串）
            value: 与键关联的值
        """
        key = key.lower()

        node = self
        for c in key:
            i = ord(c) - 97  # 转换为数组索引（a=0, b=1, ...）
            if not 0 <= i < 26:
                return  # 只支持英文字母

            if not node.children[i]:
                node.children[i] = Trie()

            node = node.children[i]

        node.isEnd = True
        node.key = key
        node.value = value

    def get(self, key, default=None):
        """
        根据键获取对应的值

        Args:
            key (str): 要查找的键
            default: 未找到时返回的默认值

        Returns:
            与键关联的值，如果未找到则返回默认值
        """
        node = self.searchPrefix(key)
        if not (node and node.isEnd):
            return default

        return node.value

    def searchPrefix(self, prefix):
        """
        搜索匹配指定前缀的节点

        Args:
            prefix (str): 要搜索的前缀

        Returns:
            匹配前缀的节点，如果未找到则返回None
        """
        prefix = prefix.lower()
        node = self
        for c in prefix:
            i = ord(c) - 97
            if not (0 <= i < 26 and node.children[i]):
                return None

            node = node.children[i]

        return node

    def items(self, prefix):
        """
        获取所有匹配指定前缀的键值对

        Args:
            prefix (str): 要搜索的前缀

        Returns:
            list: 包含所有匹配项的(key, value)元组列表
        """
        node = self.searchPrefix(prefix)
        if not node:
            return []

        q = Queue()
        result = []
        q.put(node)

        while not q.empty():
            node = q.get()
            if node.isEnd:
                result.append((node.key, node.value))

            for c in node.children:
                if c:
                    q.put(c)

        return result
