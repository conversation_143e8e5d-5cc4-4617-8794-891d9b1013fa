# coding:utf-8
"""
状态栏组件

该模块提供主窗口的状态栏组件，显示系统状态、
任务进度、连接状态等信息。

主要功能：
- 系统状态显示
- 任务进度显示
- 连接状态指示
- 实时信息更新

类说明：
- StatusBar: 状态栏类
"""

from PySide6.QtWidgets import QStatusBar, QLabel, QProgressBar, QHBoxLayout, QWidget
from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QPixmap, QIcon
from qfluentwidgets import (BodyLabel, CaptionLabel, ProgressBar, 
                           FluentIcon as FIF, IconWidget, ToolTip)
from typing import Optional, Dict, Any
from datetime import datetime

from ..logging import log_manager
from ..common import task_scheduler
from ..database.manager import db_manager


class StatusBar(QStatusBar):
    """
    状态栏类
    
    提供系统状态显示和实时信息更新
    """
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 状态信息
        self.system_status = "就绪"
        self.task_count = 0
        self.active_users = 0
        self.db_status = "未连接"
        
        self._init_ui()
        self._init_timer()
    
    def _init_ui(self):
        """初始化界面"""
        # 设置状态栏样式
        self.setStyleSheet("""
            QStatusBar {
                background-color: transparent;
                border-top: 1px solid rgba(0, 0, 0, 0.1);
                padding: 4px;
            }
            QStatusBar::item {
                border: none;
            }
        """)
        
        # 左侧状态信息
        self._create_left_widgets()
        
        # 右侧系统信息
        self._create_right_widgets()
        
        # 中间进度条
        self._create_center_widgets()
    
    def _create_left_widgets(self):
        """创建左侧状态组件"""
        # 系统状态
        self.status_icon = IconWidget(FIF.ACCEPT_MEDIUM)
        self.status_icon.setFixedSize(16, 16)
        self.status_label = CaptionLabel(self.system_status)
        
        # 状态容器
        status_widget = QWidget()
        status_layout = QHBoxLayout(status_widget)
        status_layout.setContentsMargins(0, 0, 0, 0)
        status_layout.setSpacing(5)
        status_layout.addWidget(self.status_icon)
        status_layout.addWidget(self.status_label)
        
        self.addWidget(status_widget)
        
        # 分隔符
        separator1 = QLabel("|")
        separator1.setStyleSheet("color: rgba(0, 0, 0, 0.3);")
        self.addWidget(separator1)
        
        # 任务状态
        self.task_icon = IconWidget(FIF.PLAY)
        self.task_icon.setFixedSize(16, 16)
        self.task_label = CaptionLabel(f"任务: {self.task_count}")
        
        task_widget = QWidget()
        task_layout = QHBoxLayout(task_widget)
        task_layout.setContentsMargins(0, 0, 0, 0)
        task_layout.setSpacing(5)
        task_layout.addWidget(self.task_icon)
        task_layout.addWidget(self.task_label)
        
        self.addWidget(task_widget)
        
        # 分隔符
        separator2 = QLabel("|")
        separator2.setStyleSheet("color: rgba(0, 0, 0, 0.3);")
        self.addWidget(separator2)
        
        # 用户状态
        self.user_icon = IconWidget(FIF.PEOPLE)
        self.user_icon.setFixedSize(16, 16)
        self.user_label = CaptionLabel(f"活跃用户: {self.active_users}")
        
        user_widget = QWidget()
        user_layout = QHBoxLayout(user_widget)
        user_layout.setContentsMargins(0, 0, 0, 0)
        user_layout.setSpacing(5)
        user_layout.addWidget(self.user_icon)
        user_layout.addWidget(self.user_label)
        
        self.addWidget(user_widget)
    
    def _create_center_widgets(self):
        """创建中间进度组件"""
        # 添加弹性空间
        self.addWidget(QWidget(), 1)
        
        # 进度条（默认隐藏）
        self.progress_bar = ProgressBar()
        self.progress_bar.setFixedWidth(200)
        self.progress_bar.setVisible(False)
        self.addWidget(self.progress_bar)
        
        # 进度标签
        self.progress_label = CaptionLabel("")
        self.progress_label.setVisible(False)
        self.addWidget(self.progress_label)
        
        # 添加弹性空间
        self.addWidget(QWidget(), 1)
    
    def _create_right_widgets(self):
        """创建右侧系统信息组件"""
        # 分隔符
        separator3 = QLabel("|")
        separator3.setStyleSheet("color: rgba(0, 0, 0, 0.3);")
        self.addWidget(separator3)
        
        # 数据库状态
        self.db_icon = IconWidget(FIF.DATABASE)
        self.db_icon.setFixedSize(16, 16)
        self.db_label = CaptionLabel(f"数据库: {self.db_status}")
        
        db_widget = QWidget()
        db_layout = QHBoxLayout(db_widget)
        db_layout.setContentsMargins(0, 0, 0, 0)
        db_layout.setSpacing(5)
        db_layout.addWidget(self.db_icon)
        db_layout.addWidget(self.db_label)
        
        self.addWidget(db_widget)
        
        # 分隔符
        separator4 = QLabel("|")
        separator4.setStyleSheet("color: rgba(0, 0, 0, 0.3);")
        self.addWidget(separator4)
        
        # 时间显示
        self.time_label = CaptionLabel(datetime.now().strftime("%H:%M:%S"))
        self.addWidget(self.time_label)
    
    def _init_timer(self):
        """初始化定时器"""
        # 状态更新定时器
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_status)
        self.update_timer.start(5000)  # 5秒更新一次
        
        # 时间更新定时器
        self.time_timer = QTimer()
        self.time_timer.timeout.connect(self._update_time)
        self.time_timer.start(1000)  # 1秒更新一次
    
    def update_status(self):
        """更新状态信息"""
        try:
            # 更新系统状态
            self._update_system_status()
            
            # 更新任务状态
            self._update_task_status()
            
            # 更新用户状态
            self._update_user_status()
            
            # 更新数据库状态
            self._update_database_status()
            
        except Exception as e:
            log_manager.error(f"更新状态栏失败: {e}", module="StatusBar")
    
    def _update_system_status(self):
        """更新系统状态"""
        try:
            # 检查系统状态
            if task_scheduler.is_running:
                self.system_status = "运行中"
                self.status_icon.setIcon(FIF.ACCEPT_MEDIUM)
                self.status_icon.setStyleSheet("color: green;")
            else:
                self.system_status = "已停止"
                self.status_icon.setIcon(FIF.PAUSE)
                self.status_icon.setStyleSheet("color: orange;")
            
            self.status_label.setText(self.system_status)
            
        except Exception as e:
            self.system_status = "错误"
            self.status_icon.setIcon(FIF.CLOSE)
            self.status_icon.setStyleSheet("color: red;")
            self.status_label.setText(self.system_status)
    
    def _update_task_status(self):
        """更新任务状态"""
        try:
            # 获取任务统计
            stats = task_scheduler.get_stats()
            scheduler_stats = stats.get('scheduler', {})
            
            self.task_count = scheduler_stats.get('pending_tasks', 0) + scheduler_stats.get('scheduled_tasks', 0)
            self.task_label.setText(f"任务: {self.task_count}")
            
            # 更新任务图标
            if self.task_count > 0:
                self.task_icon.setIcon(FIF.PLAY)
                self.task_icon.setStyleSheet("color: blue;")
            else:
                self.task_icon.setIcon(FIF.PAUSE)
                self.task_icon.setStyleSheet("color: gray;")
                
        except Exception as e:
            self.task_count = 0
            self.task_label.setText(f"任务: {self.task_count}")
    
    def _update_user_status(self):
        """更新用户状态"""
        try:
            # 获取活跃用户数
            from ..common.concurrency import concurrency_controller
            active_users = concurrency_controller.get_active_users()
            self.active_users = len(active_users)
            
            self.user_label.setText(f"活跃用户: {self.active_users}")
            
            # 更新用户图标
            if self.active_users > 0:
                self.user_icon.setStyleSheet("color: green;")
            else:
                self.user_icon.setStyleSheet("color: gray;")
                
        except Exception as e:
            self.active_users = 0
            self.user_label.setText(f"活跃用户: {self.active_users}")
    
    def _update_database_status(self):
        """更新数据库状态"""
        try:
            # 检查数据库连接
            if db_manager.is_connected():
                self.db_status = "已连接"
                self.db_icon.setStyleSheet("color: green;")
            else:
                self.db_status = "未连接"
                self.db_icon.setStyleSheet("color: red;")
            
            self.db_label.setText(f"数据库: {self.db_status}")
            
        except Exception as e:
            self.db_status = "错误"
            self.db_icon.setStyleSheet("color: red;")
            self.db_label.setText(f"数据库: {self.db_status}")
    
    def _update_time(self):
        """更新时间显示"""
        current_time = datetime.now().strftime("%H:%M:%S")
        self.time_label.setText(current_time)
    
    def show_progress(self, text: str, maximum: int = 100):
        """显示进度条"""
        self.progress_label.setText(text)
        self.progress_bar.setMaximum(maximum)
        self.progress_bar.setValue(0)
        self.progress_bar.setVisible(True)
        self.progress_label.setVisible(True)
    
    def update_progress(self, value: int, text: Optional[str] = None):
        """更新进度"""
        self.progress_bar.setValue(value)
        if text:
            self.progress_label.setText(text)
    
    def hide_progress(self):
        """隐藏进度条"""
        self.progress_bar.setVisible(False)
        self.progress_label.setVisible(False)
    
    def set_status_message(self, message: str, timeout: int = 0):
        """设置状态消息"""
        self.showMessage(message, timeout)
    
    def clear_status_message(self):
        """清除状态消息"""
        self.clearMessage()
    
    def set_system_status(self, status: str, icon: Optional[FIF] = None, color: Optional[str] = None):
        """设置系统状态"""
        self.system_status = status
        self.status_label.setText(status)
        
        if icon:
            self.status_icon.setIcon(icon)
        
        if color:
            self.status_icon.setStyleSheet(f"color: {color};")
    
    def get_status_info(self) -> Dict[str, Any]:
        """获取状态信息"""
        return {
            'system_status': self.system_status,
            'task_count': self.task_count,
            'active_users': self.active_users,
            'db_status': self.db_status,
            'current_time': datetime.now().isoformat()
        }
    
    def set_tooltip_info(self):
        """设置工具提示信息"""
        try:
            # 系统状态工具提示
            system_tooltip = f"系统状态: {self.system_status}\n点击查看详细信息"
            self.status_label.setToolTip(system_tooltip)
            
            # 任务状态工具提示
            task_tooltip = f"当前任务数: {self.task_count}\n点击查看任务详情"
            self.task_label.setToolTip(task_tooltip)
            
            # 用户状态工具提示
            user_tooltip = f"活跃用户数: {self.active_users}\n点击查看用户列表"
            self.user_label.setToolTip(user_tooltip)
            
            # 数据库状态工具提示
            db_tooltip = f"数据库状态: {self.db_status}\n点击查看连接详情"
            self.db_label.setToolTip(db_tooltip)
            
        except Exception as e:
            log_manager.error(f"设置工具提示失败: {e}", module="StatusBar")
