# coding:utf-8
"""
首页界面模块

该模块定义了应用程序的首页界面，包含横幅组件和链接卡片视图。
首页展示应用程序的主要功能入口和相关链接。

主要组件：
- BannerWidget: 横幅组件，显示应用标题和背景图片
- HomeInterface: 首页界面，包含横幅和功能链接卡片

功能特性：
- 响应式横幅设计，支持明暗主题
- 渐变背景效果
- 功能链接卡片展示
- 滚动区域支持
"""

from PySide6.QtCore import Qt, QRectF
from PySide6.QtGui import QPixmap, QPainter, QColor, QBrush, QPainterPath, QLinearGradient
from PySide6.QtWidgets import QWidget, QVBoxLayout, QLabel

from qfluentwidgets import ScrollArea, isDarkTheme, FluentIcon

from ..components.link_card import FlowLinkCardView
from ..common.style_sheet import StyleSheet


class BannerWidget(QWidget):
    """
    横幅组件

    显示应用程序标题和背景图片的横幅组件，支持渐变背景效果，
    并能根据明暗主题自动调整颜色。
    """

    def __init__(self, parent=None):
        """
        初始化横幅组件

        Args:
            parent: 父组件
        """
        super().__init__(parent=parent)
        self.setFixedHeight(336)

        self.vBoxLayout = QVBoxLayout(self)
        self.galleryLabel = QLabel('小帅工具箱', self)
        self.banner = QPixmap(':/gallery/images/header1.png')

        self.galleryLabel.setObjectName('galleryLabel')

        self.vBoxLayout.setSpacing(0)
        self.vBoxLayout.setContentsMargins(0, 20, 0, 0)
        self.vBoxLayout.addWidget(self.galleryLabel)
        self.vBoxLayout.setAlignment(Qt.AlignLeft | Qt.AlignTop)

    def paintEvent(self, e):
        """
        绘制事件处理

        自定义绘制横幅背景，包括渐变效果和背景图片。
        根据当前主题（明暗）调整渐变颜色。
        """
        super().paintEvent(e)
        painter = QPainter(self)
        painter.setRenderHints(
            QPainter.SmoothPixmapTransform | QPainter.Antialiasing)
        painter.setPen(Qt.NoPen)

        path = QPainterPath()
        path.setFillRule(Qt.WindingFill)
        w, h = self.width(), self.height()
        path.addRoundedRect(QRectF(0, 0, w, h), 10, 10)
        path.addRect(QRectF(0, h-50, 50, 50))
        path.addRect(QRectF(w-50, 0, 50, 50))
        path.addRect(QRectF(w-50, h-50, 50, 50))
        path = path.simplified()

        # 初始化线性渐变效果
        gradient = QLinearGradient(0, 0, 0, h)

        # 绘制背景颜色
        if not isDarkTheme():
            gradient.setColorAt(0, QColor(207, 216, 228, 255))
            gradient.setColorAt(1, QColor(207, 216, 228, 0))
        else:
            gradient.setColorAt(0, QColor(0, 0, 0, 255))
            gradient.setColorAt(1, QColor(0, 0, 0, 0))

        painter.fillPath(path, QBrush(gradient))

        # 绘制横幅图片
        pixmap = self.banner.scaled(
            self.size(), Qt.IgnoreAspectRatio, Qt.SmoothTransformation)
        painter.fillPath(path, QBrush(pixmap))


class HomeInterface(ScrollArea):
    """
    首页界面

    应用程序的主页面，包含横幅和功能链接卡片。
    提供应用程序的主要功能入口和相关资源链接。
    """

    def __init__(self, parent=None):
        """
        初始化首页界面

        Args:
            parent: 父组件
        """
        super().__init__(parent=parent)
        self.banner = BannerWidget(self)
        self.view = QWidget(self)
        self.vBoxLayout = QVBoxLayout(self.view)

        self.__initWidget()
        self.loadSamples()

    def __initWidget(self):
        """
        初始化界面组件

        设置界面样式、滚动条策略和布局参数。
        """
        self.view.setObjectName('view')
        self.setObjectName('homeInterface')
        StyleSheet.HOME_INTERFACE.apply(self)

        self.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.setWidget(self.view)
        self.setWidgetResizable(True)

        self.vBoxLayout.setContentsMargins(0, 0, 0, 36)
        self.vBoxLayout.setSpacing(40)
        self.vBoxLayout.addWidget(self.banner)
        self.vBoxLayout.setAlignment(Qt.AlignTop)

    def loadSamples(self):
        """
        加载示例链接卡片

        创建并添加页面导航链接卡片，用于快速访问应用程序的各个功能页面。
        """
        # 为主要内容创建流式布局链接卡片视图
        self.linkCardView = FlowLinkCardView(self.tr("功能页面"), parent=self.view)

        # 添加空白页面的导航链接卡片
        self.linkCardView.addNavigationCard(
            FluentIcon.DOCUMENT,
            self.tr('空白页面'),
            self.tr('这是一个空白页面，您可以在这里添加自己的内容。'),
            'blankInterface'
        )



        self.vBoxLayout.addWidget(self.linkCardView)


