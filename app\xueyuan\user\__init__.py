# coding:utf-8
"""
用户管理模块

该模块提供用户管理功能，包括用户注册、登录验证、
信息管理、状态跟踪等。

主要组件：
- manager: 用户管理器
- validator: 用户验证器
- service: 用户服务

使用示例：
    from app.xueyuan.user import UserManager
    
    manager = UserManager()
    user = await manager.create_user(phone, name, password)
"""

from .manager import UserManager, user_manager
from .validator import UserValidator
from .service import UserService, user_service

__all__ = ['UserManager', 'user_manager', 'UserValidator', 'UserService', 'user_service']
