# coding:utf-8
"""
OCR工具函数

该模块提供OCR相关的工具函数，包括图片处理、结果处理等。

主要功能：
- 图片预处理
- 结果后处理
- 格式转换
- 验证功能

函数说明：
- preprocess_image: 图片预处理
- postprocess_text: 文本后处理
- validate_image: 图片验证
- convert_result: 结果格式转换
"""

import io
import base64
from typing import Union, Optional, Tuple, List
from pathlib import Path
import re

try:
    from PIL import Image, ImageEnhance, ImageFilter
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False
    print("[OCR] PIL库未安装，图片处理功能受限")


def validate_image(image_data: Union[bytes, str, Path]) -> Tuple[bool, str]:
    """
    验证图片数据
    
    Args:
        image_data: 图片数据
        
    Returns:
        Tuple[bool, str]: (是否有效, 错误信息)
    """
    try:
        if isinstance(image_data, (str, Path)):
            file_path = Path(image_data)
            if not file_path.exists():
                return False, f"图片文件不存在: {file_path}"
            
            if not file_path.is_file():
                return False, f"路径不是文件: {file_path}"
            
            # 检查文件大小
            file_size = file_path.stat().st_size
            if file_size == 0:
                return False, "图片文件为空"
            
            if file_size > 10 * 1024 * 1024:  # 10MB
                return False, "图片文件过大 (>10MB)"
            
            # 检查文件扩展名
            valid_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tiff', '.webp'}
            if file_path.suffix.lower() not in valid_extensions:
                return False, f"不支持的图片格式: {file_path.suffix}"
            
            image_data = file_path.read_bytes()
        
        elif isinstance(image_data, bytes):
            if len(image_data) == 0:
                return False, "图片数据为空"
            
            if len(image_data) > 10 * 1024 * 1024:  # 10MB
                return False, "图片数据过大 (>10MB)"
        
        else:
            return False, f"不支持的图片数据类型: {type(image_data)}"
        
        # 如果PIL可用，进一步验证图片格式
        if PIL_AVAILABLE:
            try:
                with Image.open(io.BytesIO(image_data)) as img:
                    img.verify()  # 验证图片完整性
                return True, ""
            except Exception as e:
                return False, f"图片格式无效: {e}"
        
        # 简单的格式检查
        if image_data.startswith(b'\x89PNG'):
            return True, ""
        elif image_data.startswith(b'\xff\xd8\xff'):
            return True, ""
        elif image_data.startswith(b'BM'):
            return True, ""
        elif image_data.startswith(b'GIF'):
            return True, ""
        else:
            return False, "无法识别的图片格式"
            
    except Exception as e:
        return False, f"图片验证失败: {e}"


def preprocess_image(image_data: Union[bytes, str, Path], 
                    enhance_contrast: bool = True,
                    enhance_sharpness: bool = True,
                    convert_to_grayscale: bool = False,
                    resize_max_size: Optional[int] = None) -> bytes:
    """
    图片预处理
    
    Args:
        image_data: 图片数据
        enhance_contrast: 是否增强对比度
        enhance_sharpness: 是否增强锐度
        convert_to_grayscale: 是否转换为灰度图
        resize_max_size: 最大尺寸限制
        
    Returns:
        bytes: 处理后的图片数据
    """
    if not PIL_AVAILABLE:
        # 如果PIL不可用，直接返回原始数据
        if isinstance(image_data, bytes):
            return image_data
        elif isinstance(image_data, (str, Path)):
            return Path(image_data).read_bytes()
        else:
            raise ValueError(f"不支持的图片数据类型: {type(image_data)}")
    
    try:
        # 加载图片
        if isinstance(image_data, bytes):
            img = Image.open(io.BytesIO(image_data))
        elif isinstance(image_data, (str, Path)):
            img = Image.open(image_data)
        else:
            raise ValueError(f"不支持的图片数据类型: {type(image_data)}")
        
        # 转换为RGB模式（如果需要）
        if img.mode not in ('RGB', 'L'):
            img = img.convert('RGB')
        
        # 调整大小
        if resize_max_size and max(img.size) > resize_max_size:
            ratio = resize_max_size / max(img.size)
            new_size = (int(img.width * ratio), int(img.height * ratio))
            img = img.resize(new_size, Image.Resampling.LANCZOS)
        
        # 转换为灰度图
        if convert_to_grayscale and img.mode != 'L':
            img = img.convert('L')
        
        # 增强对比度
        if enhance_contrast:
            enhancer = ImageEnhance.Contrast(img)
            img = enhancer.enhance(1.2)  # 增强20%
        
        # 增强锐度
        if enhance_sharpness:
            enhancer = ImageEnhance.Sharpness(img)
            img = enhancer.enhance(1.1)  # 增强10%
        
        # 保存为bytes
        output = io.BytesIO()
        img.save(output, format='PNG')
        return output.getvalue()
        
    except Exception as e:
        print(f"[OCR] 图片预处理失败: {e}")
        # 预处理失败时返回原始数据
        if isinstance(image_data, bytes):
            return image_data
        elif isinstance(image_data, (str, Path)):
            return Path(image_data).read_bytes()
        else:
            raise


def postprocess_text(text: str, 
                    remove_extra_spaces: bool = True,
                    remove_special_chars: bool = False,
                    normalize_line_breaks: bool = True) -> str:
    """
    文本后处理
    
    Args:
        text: 原始文本
        remove_extra_spaces: 是否移除多余空格
        remove_special_chars: 是否移除特殊字符
        normalize_line_breaks: 是否规范化换行符
        
    Returns:
        str: 处理后的文本
    """
    if not text:
        return ""
    
    processed_text = text
    
    # 规范化换行符
    if normalize_line_breaks:
        processed_text = re.sub(r'\r\n|\r', '\n', processed_text)
    
    # 移除多余空格
    if remove_extra_spaces:
        # 移除行首行尾空格
        lines = processed_text.split('\n')
        lines = [line.strip() for line in lines]
        processed_text = '\n'.join(lines)
        
        # 移除多余的空格
        processed_text = re.sub(r' +', ' ', processed_text)
        
        # 移除多余的换行
        processed_text = re.sub(r'\n+', '\n', processed_text)
    
    # 移除特殊字符
    if remove_special_chars:
        # 保留中文、英文、数字、常用标点符号
        processed_text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9\s\.,;:!?()（）【】""''、。，；：！？]', '', processed_text)
    
    return processed_text.strip()


def convert_to_base64(image_data: Union[bytes, str, Path]) -> str:
    """
    将图片数据转换为base64编码
    
    Args:
        image_data: 图片数据
        
    Returns:
        str: base64编码的图片数据
    """
    if isinstance(image_data, bytes):
        return base64.b64encode(image_data).decode()
    elif isinstance(image_data, (str, Path)):
        image_bytes = Path(image_data).read_bytes()
        return base64.b64encode(image_bytes).decode()
    else:
        raise ValueError(f"不支持的图片数据类型: {type(image_data)}")


def extract_numbers(text: str) -> List[str]:
    """
    从文本中提取数字
    
    Args:
        text: 输入文本
        
    Returns:
        List[str]: 提取的数字列表
    """
    if not text:
        return []
    
    # 匹配整数和小数
    numbers = re.findall(r'\d+\.?\d*', text)
    return numbers


def extract_chinese_text(text: str) -> str:
    """
    从文本中提取中文字符
    
    Args:
        text: 输入文本
        
    Returns:
        str: 提取的中文文本
    """
    if not text:
        return ""
    
    # 匹配中文字符
    chinese_chars = re.findall(r'[\u4e00-\u9fa5]+', text)
    return ''.join(chinese_chars)


def extract_english_text(text: str) -> str:
    """
    从文本中提取英文字符
    
    Args:
        text: 输入文本
        
    Returns:
        str: 提取的英文文本
    """
    if not text:
        return ""
    
    # 匹配英文字符
    english_chars = re.findall(r'[a-zA-Z]+', text)
    return ' '.join(english_chars)


def calculate_text_similarity(text1: str, text2: str) -> float:
    """
    计算两个文本的相似度
    
    Args:
        text1: 文本1
        text2: 文本2
        
    Returns:
        float: 相似度 (0-1)
    """
    if not text1 or not text2:
        return 0.0
    
    # 简单的字符级相似度计算
    set1 = set(text1)
    set2 = set(text2)
    
    intersection = len(set1 & set2)
    union = len(set1 | set2)
    
    if union == 0:
        return 0.0
    
    return intersection / union


def format_ocr_result(result, format_type: str = "text") -> str:
    """
    格式化OCR结果
    
    Args:
        result: OCR结果对象
        format_type: 格式类型 ("text", "json", "xml")
        
    Returns:
        str: 格式化后的结果
    """
    if format_type == "text":
        return result.text
    elif format_type == "json":
        import json
        return json.dumps({
            "success": result.success,
            "text": result.text,
            "confidence": result.confidence,
            "engine": result.engine_name,
            "processing_time": result.processing_time,
            "error": result.error_message
        }, ensure_ascii=False, indent=2)
    elif format_type == "xml":
        return f"""<?xml version="1.0" encoding="UTF-8"?>
<ocr_result>
    <success>{result.success}</success>
    <text><![CDATA[{result.text}]]></text>
    <confidence>{result.confidence}</confidence>
    <engine>{result.engine_name}</engine>
    <processing_time>{result.processing_time}</processing_time>
    <error><![CDATA[{result.error_message}]]></error>
</ocr_result>"""
    else:
        raise ValueError(f"不支持的格式类型: {format_type}")


def is_valid_text_result(text: str, min_length: int = 1, max_length: int = 10000) -> bool:
    """
    验证文本结果是否有效
    
    Args:
        text: 文本内容
        min_length: 最小长度
        max_length: 最大长度
        
    Returns:
        bool: 是否有效
    """
    if not text:
        return False
    
    text_length = len(text.strip())
    return min_length <= text_length <= max_length
