# coding: utf-8
"""
图标模块

该模块定义了应用程序的自定义图标枚举，提供各种界面图标的
资源路径管理和主题适配功能。

主要功能：
- 定义自定义图标枚举
- 支持明暗主题图标自动切换
- 提供图标资源路径解析

类说明：
- Icon: 自定义图标枚举类，继承自FluentIconBase
"""

from enum import Enum

from qfluentwidgets import FluentIconBase, getIconColor, Theme


class Icon(FluentIconBase, Enum):
    """
    自定义图标枚举类

    定义应用程序中使用的自定义图标，
    支持根据主题自动选择对应颜色的图标文件。
    """

    GRID = "Grid"
    MENU = "Menu"
    TEXT = "Text"
    PRICE = "Price"
    EMOJI_TAB_SYMBOLS = "EmojiTabSymbols"

    def path(self, theme=Theme.AUTO):
        """
        获取图标文件路径

        Args:
            theme: 主题类型，默认为自动检测

        Returns:
            str: 图标文件的资源路径
        """
        return f":/gallery/images/icons/{self.value}_{getIconColor(theme)}.svg"
