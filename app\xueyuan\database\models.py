# coding:utf-8
"""
数据库模型定义

该模块定义了学习工具的数据库表结构和模型类，
基于设计文档中的数据结构设计实现。

主要模型：
- User: 用户数据模型
- Course: 课程数据模型
- Log: 日志数据模型
- APIData: API数据模型

表结构说明：
- users: 用户信息表
- courses: 课程信息表
- logs: 日志记录表
- api_data: API数据表
"""

from dataclasses import dataclass
from datetime import datetime
from typing import Optional


@dataclass
class User:
    """用户数据模型"""
    id: Optional[int] = None
    phone: str = ""
    password: str = ""
    name: Optional[str] = None
    status: str = "未开始"
    complete_status: str = "1"
    online_total_credit: float = 0.0
    compulsory_credit: float = 0.0
    electives_credit: float = 0.0
    last_login_time: Optional[datetime] = None
    error_message: Optional[str] = None
    progress: float = 0.0
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None


@dataclass
class Course:
    """课程数据模型"""
    id: Optional[int] = None
    user_phone: str = ""
    course_type: str = ""
    name: str = ""
    course_id: str = ""
    completed: str = "0"
    credit: int = 0
    percentage: str = "0"
    courseware_id: Optional[str] = None
    video_url: Optional[str] = None
    completed_date: Optional[datetime] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None


@dataclass
class Log:
    """日志数据模型"""
    id: Optional[int] = None
    level: str = ""
    message: str = ""
    module: Optional[str] = None
    user_phone: Optional[str] = None
    created_at: Optional[datetime] = None


@dataclass
class APIData:
    """API数据模型"""
    id: Optional[int] = None
    user_phone: str = ""
    api_url: str = ""
    request_method: str = "GET"
    response_status: Optional[int] = None
    response_data: Optional[str] = None
    capture_time: Optional[datetime] = None
    is_processed: bool = False
    error_message: Optional[str] = None


# 数据库表创建SQL语句
CREATE_TABLES_SQL = {
    "users": """
    CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        phone TEXT UNIQUE NOT NULL,
        password TEXT NOT NULL,
        name TEXT,
        status TEXT DEFAULT '未开始',
        complete_status TEXT DEFAULT '1',
        online_total_credit REAL DEFAULT 0,
        compulsory_credit REAL DEFAULT 0,
        electives_credit REAL DEFAULT 0,
        last_login_time DATETIME,
        error_message TEXT,
        progress REAL DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
    """,
    
    "courses": """
    CREATE TABLE IF NOT EXISTS courses (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_phone TEXT NOT NULL,
        course_type TEXT NOT NULL,
        name TEXT NOT NULL,
        course_id TEXT NOT NULL,
        completed TEXT DEFAULT '0',
        credit INTEGER DEFAULT 0,
        percentage TEXT DEFAULT '0',
        courseware_id TEXT,
        video_url TEXT,
        completed_date DATE,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_phone) REFERENCES users(phone)
    )
    """,
    
    "logs": """
    CREATE TABLE IF NOT EXISTS logs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        level TEXT NOT NULL,
        message TEXT NOT NULL,
        module TEXT,
        user_phone TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
    """,
    
    "api_data": """
    CREATE TABLE IF NOT EXISTS api_data (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_phone TEXT NOT NULL,
        api_url TEXT NOT NULL,
        request_method TEXT DEFAULT 'GET',
        response_status INTEGER,
        response_data TEXT,
        capture_time DATETIME DEFAULT CURRENT_TIMESTAMP,
        is_processed BOOLEAN DEFAULT FALSE,
        error_message TEXT,
        FOREIGN KEY (user_phone) REFERENCES users(phone)
    )
    """
}

# 索引创建SQL语句
CREATE_INDEXES_SQL = [
    "CREATE INDEX IF NOT EXISTS idx_users_phone ON users(phone)",
    "CREATE INDEX IF NOT EXISTS idx_users_status ON users(status)",
    "CREATE INDEX IF NOT EXISTS idx_courses_user_phone ON courses(user_phone)",
    "CREATE INDEX IF NOT EXISTS idx_courses_type ON courses(course_type)",
    "CREATE INDEX IF NOT EXISTS idx_logs_level ON logs(level)",
    "CREATE INDEX IF NOT EXISTS idx_logs_created_at ON logs(created_at)",
    "CREATE INDEX IF NOT EXISTS idx_api_data_user_phone ON api_data(user_phone)",
    "CREATE INDEX IF NOT EXISTS idx_api_data_api_url ON api_data(api_url)",
    "CREATE INDEX IF NOT EXISTS idx_api_data_is_processed ON api_data(is_processed)"
]
