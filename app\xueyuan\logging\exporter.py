# coding:utf-8
"""
日志导出器

该模块提供日志导出功能，支持多种格式导出，
包括Excel、CSV、JSON、TXT等。

主要功能：
- 多格式导出
- 自定义导出字段
- 批量导出
- 压缩导出

类说明：
- LogExporter: 日志导出器类
"""

import os
import json
import csv
import zipfile
from typing import Optional, Dict, Any, List
from datetime import datetime
from pathlib import Path

try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False

from .manager import log_manager


class LogExporter:
    """
    日志导出器类
    
    提供多种格式的日志导出功能
    """
    
    def __init__(self):
        """初始化日志导出器"""
        self.export_dir = Path("exports")
        self.export_dir.mkdir(parents=True, exist_ok=True)
        
        # 支持的格式
        self.supported_formats = ['excel', 'csv', 'json', 'txt']
        if PANDAS_AVAILABLE:
            self.supported_formats.extend(['xlsx', 'parquet'])
        
        print("[日志导出] 日志导出器初始化完成")
    
    def export_logs(self, logs: Optional[List[Dict[str, Any]]] = None,
                   format: str = 'excel', filename: Optional[str] = None,
                   filters: Optional[Dict[str, Any]] = None,
                   fields: Optional[List[str]] = None,
                   compress: bool = False) -> bool:
        """
        导出日志
        
        Args:
            logs: 日志数据，None表示从管理器获取
            format: 导出格式
            filename: 文件名
            filters: 过滤条件（当logs为None时使用）
            fields: 导出字段
            compress: 是否压缩
            
        Returns:
            bool: 是否成功
        """
        try:
            # 获取日志数据
            if logs is None:
                logs = self._get_logs_from_manager(filters or {})
            
            if not logs:
                print("[日志导出] 没有可导出的日志数据")
                return False
            
            # 生成文件名
            if not filename:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f"logs_{timestamp}.{format}"
            
            # 确保文件扩展名正确
            if not filename.endswith(f'.{format}'):
                filename = f"{filename}.{format}"
            
            file_path = self.export_dir / filename
            
            # 处理导出字段
            if fields:
                logs = self._filter_fields(logs, fields)
            
            # 根据格式导出
            success = False
            if format == 'excel' or format == 'xlsx':
                success = self._export_excel(logs, file_path)
            elif format == 'csv':
                success = self._export_csv(logs, file_path)
            elif format == 'json':
                success = self._export_json(logs, file_path)
            elif format == 'txt':
                success = self._export_txt(logs, file_path)
            elif format == 'parquet' and PANDAS_AVAILABLE:
                success = self._export_parquet(logs, file_path)
            else:
                print(f"[日志导出] 不支持的格式: {format}")
                return False
            
            if success:
                # 压缩文件
                if compress:
                    zip_path = file_path.with_suffix('.zip')
                    self._compress_file(file_path, zip_path)
                    file_path.unlink()  # 删除原文件
                    file_path = zip_path
                
                print(f"[日志导出] 导出成功: {file_path}")
                return True
            else:
                print("[日志导出] 导出失败")
                return False
                
        except Exception as e:
            print(f"[日志导出] 导出日志失败: {e}")
            return False
    
    def _get_logs_from_manager(self, filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """从日志管理器获取日志"""
        return log_manager.get_logs(
            level=filters.get('level'),
            user_phone=filters.get('user_phone'),
            module=filters.get('module'),
            start_time=filters.get('start_time'),
            end_time=filters.get('end_time'),
            limit=filters.get('limit', 10000),
            source=filters.get('source', 'database')
        )
    
    def _filter_fields(self, logs: List[Dict[str, Any]], fields: List[str]) -> List[Dict[str, Any]]:
        """过滤字段"""
        filtered_logs = []
        for log in logs:
            filtered_log = {field: log.get(field) for field in fields if field in log}
            filtered_logs.append(filtered_log)
        return filtered_logs
    
    def _export_excel(self, logs: List[Dict[str, Any]], file_path: Path) -> bool:
        """导出Excel格式"""
        try:
            if not PANDAS_AVAILABLE:
                print("[日志导出] 需要安装pandas库才能导出Excel格式")
                return False
            
            # 转换为DataFrame
            df = pd.DataFrame(logs)
            
            # 处理时间字段
            if 'timestamp' in df.columns:
                df['timestamp'] = pd.to_datetime(df['timestamp'])
            
            # 导出Excel
            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='日志数据', index=False)
                
                # 获取工作表
                worksheet = writer.sheets['日志数据']
                
                # 自动调整列宽
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    
                    adjusted_width = min(max_length + 2, 50)
                    worksheet.column_dimensions[column_letter].width = adjusted_width
            
            return True
            
        except Exception as e:
            print(f"[日志导出] 导出Excel失败: {e}")
            return False
    
    def _export_csv(self, logs: List[Dict[str, Any]], file_path: Path) -> bool:
        """导出CSV格式"""
        try:
            if not logs:
                return False
            
            # 获取所有字段
            fieldnames = set()
            for log in logs:
                fieldnames.update(log.keys())
            fieldnames = sorted(list(fieldnames))
            
            # 写入CSV
            with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                
                for log in logs:
                    # 处理时间字段
                    row = log.copy()
                    if 'timestamp' in row and isinstance(row['timestamp'], datetime):
                        row['timestamp'] = row['timestamp'].strftime('%Y-%m-%d %H:%M:%S')
                    
                    writer.writerow(row)
            
            return True
            
        except Exception as e:
            print(f"[日志导出] 导出CSV失败: {e}")
            return False
    
    def _export_json(self, logs: List[Dict[str, Any]], file_path: Path) -> bool:
        """导出JSON格式"""
        try:
            # 处理时间字段
            processed_logs = []
            for log in logs:
                processed_log = log.copy()
                if 'timestamp' in processed_log and isinstance(processed_log['timestamp'], datetime):
                    processed_log['timestamp'] = processed_log['timestamp'].isoformat()
                processed_logs.append(processed_log)
            
            # 写入JSON
            with open(file_path, 'w', encoding='utf-8') as jsonfile:
                json.dump(processed_logs, jsonfile, ensure_ascii=False, indent=2)
            
            return True
            
        except Exception as e:
            print(f"[日志导出] 导出JSON失败: {e}")
            return False
    
    def _export_txt(self, logs: List[Dict[str, Any]], file_path: Path) -> bool:
        """导出TXT格式"""
        try:
            with open(file_path, 'w', encoding='utf-8') as txtfile:
                for log in logs:
                    # 格式化日志行
                    timestamp = log.get('timestamp', 'N/A')
                    if isinstance(timestamp, datetime):
                        timestamp = timestamp.strftime('%Y-%m-%d %H:%M:%S')
                    
                    level = log.get('level', 'N/A')
                    user_phone = log.get('user_phone', 'N/A')
                    module = log.get('module', 'N/A')
                    message = log.get('message', 'N/A')
                    
                    line = f"[{timestamp}] [{level}] [{user_phone}] [{module}] {message}\n"
                    txtfile.write(line)
            
            return True
            
        except Exception as e:
            print(f"[日志导出] 导出TXT失败: {e}")
            return False
    
    def _export_parquet(self, logs: List[Dict[str, Any]], file_path: Path) -> bool:
        """导出Parquet格式"""
        try:
            if not PANDAS_AVAILABLE:
                print("[日志导出] 需要安装pandas库才能导出Parquet格式")
                return False
            
            # 转换为DataFrame
            df = pd.DataFrame(logs)
            
            # 处理时间字段
            if 'timestamp' in df.columns:
                df['timestamp'] = pd.to_datetime(df['timestamp'])
            
            # 导出Parquet
            df.to_parquet(file_path, index=False)
            
            return True
            
        except Exception as e:
            print(f"[日志导出] 导出Parquet失败: {e}")
            return False
    
    def _compress_file(self, file_path: Path, zip_path: Path):
        """压缩文件"""
        try:
            with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                zipf.write(file_path, file_path.name)
            
            print(f"[日志导出] 文件已压缩: {zip_path}")
            
        except Exception as e:
            print(f"[日志导出] 压缩文件失败: {e}")
    
    def export_log_files(self, compress: bool = True) -> Optional[str]:
        """
        导出日志文件
        
        Args:
            compress: 是否压缩
            
        Returns:
            Optional[str]: 导出文件路径
        """
        try:
            # 获取日志文件列表
            log_files = log_manager.get_log_files()
            
            if not log_files:
                print("[日志导出] 没有可导出的日志文件")
                return None
            
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            
            if compress:
                # 压缩导出
                zip_path = self.export_dir / f"log_files_{timestamp}.zip"
                
                with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                    for log_file in log_files:
                        file_path = Path(log_file['path'])
                        if file_path.exists():
                            zipf.write(file_path, file_path.name)
                
                print(f"[日志导出] 日志文件导出成功: {zip_path}")
                return str(zip_path)
            else:
                # 复制文件
                export_subdir = self.export_dir / f"log_files_{timestamp}"
                export_subdir.mkdir(exist_ok=True)
                
                for log_file in log_files:
                    src_path = Path(log_file['path'])
                    if src_path.exists():
                        dst_path = export_subdir / src_path.name
                        dst_path.write_bytes(src_path.read_bytes())
                
                print(f"[日志导出] 日志文件导出成功: {export_subdir}")
                return str(export_subdir)
                
        except Exception as e:
            print(f"[日志导出] 导出日志文件失败: {e}")
            return None
    
    def get_export_stats(self) -> Dict[str, Any]:
        """
        获取导出统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            export_files = []
            total_size = 0
            
            for file_path in self.export_dir.glob("*"):
                if file_path.is_file():
                    stat = file_path.stat()
                    export_files.append({
                        'name': file_path.name,
                        'size': stat.st_size,
                        'modified_time': datetime.fromtimestamp(stat.st_mtime)
                    })
                    total_size += stat.st_size
            
            # 按修改时间排序
            export_files.sort(key=lambda x: x['modified_time'], reverse=True)
            
            return {
                'export_dir': str(self.export_dir),
                'total_files': len(export_files),
                'total_size': total_size,
                'files': export_files,
                'supported_formats': self.supported_formats
            }
            
        except Exception as e:
            print(f"[日志导出] 获取导出统计失败: {e}")
            return {}
    
    def cleanup_old_exports(self, days: int = 30):
        """
        清理旧的导出文件
        
        Args:
            days: 保留天数
        """
        try:
            cutoff_time = datetime.now() - timedelta(days=days)
            deleted_count = 0
            
            for file_path in self.export_dir.glob("*"):
                if file_path.is_file():
                    stat = file_path.stat()
                    modified_time = datetime.fromtimestamp(stat.st_mtime)
                    
                    if modified_time < cutoff_time:
                        file_path.unlink()
                        deleted_count += 1
            
            print(f"[日志导出] 清理完成，删除了 {deleted_count} 个旧文件")
            
        except Exception as e:
            print(f"[日志导出] 清理旧文件失败: {e}")
    
    def get_supported_formats(self) -> List[str]:
        """
        获取支持的导出格式
        
        Returns:
            List[str]: 支持的格式列表
        """
        return self.supported_formats.copy()
