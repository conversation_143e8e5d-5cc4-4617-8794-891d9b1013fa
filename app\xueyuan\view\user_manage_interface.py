# coding:utf-8
"""
用户管理界面

该模块定义了用户管理界面类 UserManageInterface，
提供用户的增删改查、批量导入、状态管理等功能。

主要功能：
- 用户列表显示和管理
- 添加、编辑、删除用户
- 批量导入用户
- 用户状态管理

类说明：
- UserManageInterface: 用户管理界面类
"""

from PySide6.QtCore import Qt, QTimer, Signal
from PySide6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QFileDialog, QTableWidgetItem
from qfluentwidgets import (CardWidget, HeaderCardWidget, BodyLabel, LineEdit,
                            PrimaryPushButton, PushButton, TableWidget, ComboBox,
                            FluentIcon as FIF, InfoBar, MessageBox, Dialog)

from ..database.dao import DAOFactory
from ..database.models import User
from ..common.constants import UserStatus


class UserManageInterface(QWidget):
    """用户管理界面类"""
    
    # 定义信号
    userAddedSignal = Signal(User)
    userUpdatedSignal = Signal(User)
    userDeletedSignal = Signal(int)
    
    def __init__(self, parent=None):
        """初始化用户管理界面"""
        super().__init__(parent)
        self.setObjectName("UserManageInterface")
        
        # 初始化界面
        self.initWidget()
        self.initLayout()
        self.connectSignalToSlot()
        
        # 启动定时器更新用户列表
        self.updateTimer = QTimer()
        self.updateTimer.timeout.connect(self.refreshUserList)
        self.updateTimer.start(5000)  # 每5秒更新一次
        
        # 初始加载用户列表
        self.refreshUserList()

    def initWidget(self):
        """初始化界面组件"""
        # 创建主布局
        self.vBoxLayout = QVBoxLayout(self)
        
        # 创建操作面板卡片
        self.operationCard = HeaderCardWidget(self)
        self.operationCard.setTitle("用户操作")
        
        # 操作面板内容
        self.operationWidget = QWidget()
        self.operationLayout = QHBoxLayout(self.operationWidget)
        
        # 添加用户按钮
        self.addUserBtn = PrimaryPushButton("添加用户", self)
        self.addUserBtn.setIcon(FIF.ADD)
        self.addUserBtn.setFixedSize(120, 35)
        
        # 批量导入按钮
        self.importBtn = PushButton("批量导入", self)
        self.importBtn.setIcon(FIF.FOLDER)
        self.importBtn.setFixedSize(120, 35)
        
        # 导出用户按钮
        self.exportBtn = PushButton("导出用户", self)
        self.exportBtn.setIcon(FIF.SAVE)
        self.exportBtn.setFixedSize(120, 35)
        
        # 刷新按钮
        self.refreshBtn = PushButton("刷新", self)
        self.refreshBtn.setIcon(FIF.SYNC)
        self.refreshBtn.setFixedSize(80, 35)
        
        # 状态筛选
        self.statusFilterCombo = ComboBox()
        self.statusFilterCombo.addItems(["全部状态", "未开始", "登录中", "学习中", "已完成", "错误"])
        self.statusFilterCombo.setFixedSize(120, 35)
        
        # 搜索框
        self.searchEdit = LineEdit()
        self.searchEdit.setPlaceholderText("搜索手机号或姓名...")
        self.searchEdit.setFixedSize(200, 35)
        
        # 添加到操作布局
        self.operationLayout.addWidget(self.addUserBtn)
        self.operationLayout.addWidget(self.importBtn)
        self.operationLayout.addWidget(self.exportBtn)
        self.operationLayout.addWidget(self.refreshBtn)
        self.operationLayout.addStretch()
        self.operationLayout.addWidget(BodyLabel("状态筛选:"))
        self.operationLayout.addWidget(self.statusFilterCombo)
        self.operationLayout.addWidget(self.searchEdit)

        # 设置操作卡片内容
        self.operationCard.viewLayout.addWidget(self.operationWidget)
        
        # 创建用户列表卡片
        self.userListCard = HeaderCardWidget(self)
        self.userListCard.setTitle("用户列表")
        
        # 用户表格
        self.userTable = TableWidget()
        self.userTable.setColumnCount(9)
        self.userTable.setHorizontalHeaderLabels([
            "ID", "手机号", "密码", "姓名", "状态", "总学分", "必修学分", "选修学分", "操作"
        ])
        
        # 设置列宽
        self.userTable.setColumnWidth(0, 50)   # ID
        self.userTable.setColumnWidth(1, 120)  # 手机号
        self.userTable.setColumnWidth(2, 100)  # 密码
        self.userTable.setColumnWidth(3, 100)  # 姓名
        self.userTable.setColumnWidth(4, 80)   # 状态
        self.userTable.setColumnWidth(5, 80)   # 总学分
        self.userTable.setColumnWidth(6, 80)   # 必修学分
        self.userTable.setColumnWidth(7, 80)   # 选修学分
        self.userTable.setColumnWidth(8, 120)  # 操作

        # 设置用户列表卡片内容
        self.userListCard.viewLayout.addWidget(self.userTable)

    def initLayout(self):
        """初始化布局"""
        self.vBoxLayout.setContentsMargins(0, 0, 0, 0)
        self.vBoxLayout.setSpacing(20)
        
        self.vBoxLayout.addWidget(self.operationCard)
        self.vBoxLayout.addWidget(self.userListCard)

    def connectSignalToSlot(self):
        """连接信号到槽函数"""
        self.addUserBtn.clicked.connect(self.onAddUser)
        self.importBtn.clicked.connect(self.onImportUsers)
        self.exportBtn.clicked.connect(self.onExportUsers)
        self.refreshBtn.clicked.connect(self.refreshUserList)
        self.statusFilterCombo.currentTextChanged.connect(self.onFilterChanged)
        self.searchEdit.textChanged.connect(self.onSearchChanged)

    def onAddUser(self):
        """添加用户"""
        dialog = UserEditDialog(self)
        if dialog.exec():
            user_data = dialog.getUserData()
            user = User(
                phone=user_data["phone"],
                password=user_data["password"],
                name=user_data["name"],
                status=UserStatus.NOT_STARTED
            )
            
            user_dao = DAOFactory.get_user_dao()
            if user_dao.create(user):
                self.showInfoBar("用户添加成功", "success")
                self.refreshUserList()
                self.userAddedSignal.emit(user)
            else:
                self.showInfoBar("用户添加失败", "error")

    def onImportUsers(self):
        """批量导入用户"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择用户文件", "", "CSV文件 (*.csv);;Excel文件 (*.xlsx)"
        )
        
        if file_path:
            try:
                # 这里应该实现文件解析逻辑
                # 暂时显示提示信息
                self.showInfoBar("批量导入功能开发中...", "info")
            except Exception as e:
                self.showInfoBar(f"导入失败: {str(e)}", "error")

    def onExportUsers(self):
        """导出用户"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存用户文件", "users.csv", "CSV文件 (*.csv)"
        )
        
        if file_path:
            try:
                # 这里应该实现导出逻辑
                # 暂时显示提示信息
                self.showInfoBar("导出功能开发中...", "info")
            except Exception as e:
                self.showInfoBar(f"导出失败: {str(e)}", "error")

    def onFilterChanged(self):
        """状态筛选改变"""
        self.refreshUserList()

    def onSearchChanged(self):
        """搜索内容改变"""
        self.refreshUserList()

    def refreshUserList(self):
        """刷新用户列表"""
        try:
            user_dao = DAOFactory.get_user_dao()
            
            # 获取筛选条件
            status_filter = self.statusFilterCombo.currentText()
            search_text = self.searchEdit.text().strip()
            
            # 获取用户列表
            if status_filter == "全部状态":
                users = user_dao.get_all()
            else:
                # 状态映射
                status_map = {
                    "未开始": UserStatus.NOT_STARTED,
                    "登录中": UserStatus.LOGGING_IN,
                    "学习中": UserStatus.STUDYING,
                    "已完成": UserStatus.COMPLETED,
                    "错误": UserStatus.ERROR
                }
                status = status_map.get(status_filter, UserStatus.NOT_STARTED)
                users = user_dao.get_by_status(status)
            
            # 应用搜索筛选
            if search_text:
                users = [u for u in users if 
                        search_text in u.phone or 
                        (u.name and search_text in u.name)]
            
            # 更新表格
            self.updateUserTable(users)
            
        except Exception as e:
            print(f"[界面] 刷新用户列表失败: {e}")
            self.showInfoBar("刷新用户列表失败", "error")

    def updateUserTable(self, users):
        """更新用户表格"""
        try:
            self.userTable.setRowCount(len(users))
            
            for i, user in enumerate(users):
                self.userTable.setItem(i, 0, QTableWidgetItem(str(user.id)))
                self.userTable.setItem(i, 1, QTableWidgetItem(user.phone))
                self.userTable.setItem(i, 2, QTableWidgetItem(user.password))
                self.userTable.setItem(i, 3, QTableWidgetItem(user.name or ""))
                self.userTable.setItem(i, 4, QTableWidgetItem(user.status))
                self.userTable.setItem(i, 5, QTableWidgetItem(f"{user.online_total_credit:.1f}"))
                self.userTable.setItem(i, 6, QTableWidgetItem(f"{user.compulsory_credit:.1f}"))
                self.userTable.setItem(i, 7, QTableWidgetItem(f"{user.electives_credit:.1f}"))
                
                # 操作按钮
                operation_widget = QWidget()
                operation_layout = QHBoxLayout(operation_widget)
                operation_layout.setContentsMargins(5, 0, 5, 0)
                
                edit_btn = PushButton("编辑")
                edit_btn.setFixedSize(40, 25)
                edit_btn.clicked.connect(lambda checked, u=user: self.onEditUser(u))
                
                delete_btn = PushButton("删除")
                delete_btn.setFixedSize(40, 25)
                delete_btn.clicked.connect(lambda checked, u=user: self.onDeleteUser(u))
                
                operation_layout.addWidget(edit_btn)
                operation_layout.addWidget(delete_btn)
                
                self.userTable.setCellWidget(i, 8, operation_widget)
                
        except Exception as e:
            print(f"[界面] 更新用户表格失败: {e}")

    def onEditUser(self, user):
        """编辑用户"""
        dialog = UserEditDialog(self, user)
        if dialog.exec():
            user_data = dialog.getUserData()
            user.phone = user_data["phone"]
            user.password = user_data["password"]
            user.name = user_data["name"]
            
            user_dao = DAOFactory.get_user_dao()
            if user_dao.update(user):
                self.showInfoBar("用户更新成功", "success")
                self.refreshUserList()
                self.userUpdatedSignal.emit(user)
            else:
                self.showInfoBar("用户更新失败", "error")

    def onDeleteUser(self, user):
        """删除用户"""
        dialog = MessageBox("确认删除", f"确定要删除用户 {user.phone} 吗？", self)
        if dialog.exec():
            user_dao = DAOFactory.get_user_dao()
            if user_dao.delete(user.id):
                self.showInfoBar("用户删除成功", "success")
                self.refreshUserList()
                self.userDeletedSignal.emit(user.id)
            else:
                self.showInfoBar("用户删除失败", "error")

    def showInfoBar(self, message: str, type: str = "info"):
        """显示信息栏"""
        if type == "success":
            InfoBar.success("", message, duration=3000, parent=self)
        elif type == "warning":
            InfoBar.warning("", message, duration=5000, parent=self)
        elif type == "error":
            InfoBar.error("", message, duration=8000, parent=self)
        else:
            InfoBar.info("", message, duration=3000, parent=self)


class UserEditDialog(Dialog):
    """用户编辑对话框"""
    
    def __init__(self, parent=None, user=None):
        super().__init__("编辑用户" if user else "添加用户", "", parent)
        self.user = user
        self.initWidget()
        
    def initWidget(self):
        """初始化组件"""
        # 手机号输入
        self.phoneEdit = LineEdit()
        self.phoneEdit.setPlaceholderText("请输入手机号")
        
        # 密码输入
        self.passwordEdit = LineEdit()
        self.passwordEdit.setPlaceholderText("请输入密码")
        
        # 姓名输入
        self.nameEdit = LineEdit()
        self.nameEdit.setPlaceholderText("请输入姓名")
        
        # 如果是编辑模式，填充现有数据
        if self.user:
            self.phoneEdit.setText(self.user.phone)
            self.passwordEdit.setText(self.user.password)
            self.nameEdit.setText(self.user.name or "")
        
        # 添加到布局
        self.textLayout.addWidget(BodyLabel("手机号:"))
        self.textLayout.addWidget(self.phoneEdit)
        self.textLayout.addWidget(BodyLabel("密码:"))
        self.textLayout.addWidget(self.passwordEdit)
        self.textLayout.addWidget(BodyLabel("姓名:"))
        self.textLayout.addWidget(self.nameEdit)
        
    def getUserData(self):
        """获取用户数据"""
        return {
            "phone": self.phoneEdit.text().strip(),
            "password": self.passwordEdit.text().strip(),
            "name": self.nameEdit.text().strip()
        }
