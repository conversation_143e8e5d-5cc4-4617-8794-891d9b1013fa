# coding:utf-8
"""
测试模块

该模块包含学习工具的所有测试用例，包括单元测试、
集成测试和性能测试。

测试结构：
- unit/: 单元测试
- integration/: 集成测试
- performance/: 性能测试
- fixtures/: 测试数据和夹具

使用方法：
    # 运行所有测试
    python -m pytest tests/
    
    # 运行单元测试
    python -m pytest tests/unit/
    
    # 运行集成测试
    python -m pytest tests/integration/
    
    # 运行性能测试
    python -m pytest tests/performance/
"""

import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

__version__ = "1.0.0"
