# coding:utf-8
"""
学习监控器

该模块提供学习过程监控功能，包括视频播放监控、
学习时间统计、异常检测等。

主要功能：
- 视频播放状态监控
- 学习时间统计
- 学习行为分析
- 异常情况检测

类说明：
- LearningMonitor: 学习监控器类
"""

import asyncio
import time
from typing import Optional, Dict, Any, List, Callable
from datetime import datetime, timedelta
from playwright.async_api import Page

from ..common.config_loader import get_study_config
from ..database.dao import DAOFactory


class LearningMonitor:
    """
    学习监控器类
    
    提供学习过程的实时监控功能
    """
    
    def __init__(self):
        """初始化学习监控器"""
        self.page: Optional[Page] = None
        self.is_monitoring = False
        self.current_session: Optional[Dict[str, Any]] = None
        
        # 配置
        cfg = get_study_config()
        self.monitor_interval = cfg.get(cfg.monitorInterval, 5)  # 监控间隔（秒）
        self.idle_timeout = cfg.get(cfg.idleTimeout, 300)  # 空闲超时（秒）
        
        # 数据库DAO
        self.log_dao = DAOFactory.get_log_dao()
        self.progress_dao = DAOFactory.get_progress_dao()
        
        # 监控数据
        self.session_data = {
            'start_time': None,
            'total_time': 0,
            'active_time': 0,
            'idle_time': 0,
            'video_events': [],
            'interaction_events': [],
            'last_activity': None
        }
        
        # 视频监控状态
        self.video_state = {
            'current_video': None,
            'is_playing': False,
            'current_time': 0,
            'duration': 0,
            'play_start_time': None,
            'total_play_time': 0,
            'pause_count': 0,
            'seek_count': 0
        }
        
        # 回调函数
        self.callbacks: Dict[str, List[Callable]] = {
            'video_start': [],
            'video_pause': [],
            'video_resume': [],
            'video_complete': [],
            'session_idle': [],
            'session_active': []
        }
        
        print("[学习监控] 学习监控器初始化完成")
    
    def set_page(self, page: Page):
        """
        设置Playwright页面对象
        
        Args:
            page: Playwright页面对象
        """
        self.page = page
        print("[学习监控] 页面对象已设置")
    
    async def start_monitoring(self, user_phone: str, course_id: str) -> bool:
        """
        开始学习监控
        
        Args:
            user_phone: 用户手机号
            course_id: 课程ID
            
        Returns:
            bool: 是否成功启动
        """
        if self.is_monitoring:
            print("[学习监控] 监控已在运行中")
            return True
        
        if not self.page:
            print("[学习监控] 页面对象未设置")
            return False
        
        try:
            print(f"[学习监控] 开始学习监控: {user_phone} - {course_id}")
            
            # 初始化会话数据
            self.current_session = {
                'user_phone': user_phone,
                'course_id': course_id,
                'session_id': f"{user_phone}_{course_id}_{int(time.time())}",
                'start_time': datetime.now()
            }
            
            # 重置监控数据
            self.session_data['start_time'] = time.time()
            self.session_data['last_activity'] = time.time()
            
            # 设置页面事件监听
            await self._setup_page_listeners()
            
            self.is_monitoring = True
            
            # 启动监控循环
            asyncio.create_task(self._monitoring_loop())
            
            print("[学习监控] 学习监控已启动")
            return True
            
        except Exception as e:
            print(f"[学习监控] 启动学习监控失败: {e}")
            return False
    
    async def stop_monitoring(self):
        """停止学习监控"""
        if not self.is_monitoring:
            print("[学习监控] 监控未在运行中")
            return
        
        try:
            self.is_monitoring = False
            
            # 保存会话数据
            if self.current_session:
                await self._save_session_data()
            
            # 清理状态
            self.current_session = None
            self._reset_session_data()
            
            print("[学习监控] 学习监控已停止")
            
        except Exception as e:
            print(f"[学习监控] 停止学习监控失败: {e}")
    
    async def _setup_page_listeners(self):
        """设置页面事件监听器"""
        try:
            # 注入监控脚本
            await self.page.add_init_script("""
                // 视频事件监听
                window.videoMonitor = {
                    events: [],
                    addEvent: function(type, data) {
                        this.events.push({
                            type: type,
                            data: data,
                            timestamp: Date.now()
                        });
                    }
                };
                
                // 监听视频事件
                document.addEventListener('DOMContentLoaded', function() {
                    const videos = document.querySelectorAll('video');
                    videos.forEach(video => {
                        video.addEventListener('play', function() {
                            window.videoMonitor.addEvent('play', {
                                currentTime: this.currentTime,
                                duration: this.duration,
                                src: this.src
                            });
                        });
                        
                        video.addEventListener('pause', function() {
                            window.videoMonitor.addEvent('pause', {
                                currentTime: this.currentTime,
                                duration: this.duration,
                                src: this.src
                            });
                        });
                        
                        video.addEventListener('ended', function() {
                            window.videoMonitor.addEvent('ended', {
                                currentTime: this.currentTime,
                                duration: this.duration,
                                src: this.src
                            });
                        });
                        
                        video.addEventListener('seeked', function() {
                            window.videoMonitor.addEvent('seeked', {
                                currentTime: this.currentTime,
                                duration: this.duration,
                                src: this.src
                            });
                        });
                        
                        video.addEventListener('timeupdate', function() {
                            window.videoMonitor.addEvent('timeupdate', {
                                currentTime: this.currentTime,
                                duration: this.duration,
                                src: this.src
                            });
                        });
                    });
                });
                
                // 用户交互监听
                ['click', 'keydown', 'mousemove', 'scroll'].forEach(eventType => {
                    document.addEventListener(eventType, function() {
                        window.lastActivity = Date.now();
                    });
                });
            """)
            
            print("[学习监控] 页面监听器已设置")
            
        except Exception as e:
            print(f"[学习监控] 设置页面监听器失败: {e}")
    
    async def _monitoring_loop(self):
        """监控循环"""
        try:
            while self.is_monitoring:
                await self._collect_monitoring_data()
                await self._analyze_learning_behavior()
                await self._check_idle_status()
                await asyncio.sleep(self.monitor_interval)
                
        except Exception as e:
            print(f"[学习监控] 监控循环异常: {e}")
            self.is_monitoring = False
    
    async def _collect_monitoring_data(self):
        """收集监控数据"""
        try:
            current_time = time.time()
            
            # 更新总时间
            if self.session_data['start_time']:
                self.session_data['total_time'] = current_time - self.session_data['start_time']
            
            # 获取视频事件
            video_events = await self.page.evaluate("window.videoMonitor ? window.videoMonitor.events.splice(0) : []")
            
            if video_events:
                await self._process_video_events(video_events)
            
            # 获取用户活动状态
            last_activity = await self.page.evaluate("window.lastActivity || 0")
            if last_activity > 0:
                self.session_data['last_activity'] = last_activity / 1000  # 转换为秒
            
            # 获取当前视频状态
            await self._update_video_state()
            
        except Exception as e:
            print(f"[学习监控] 收集监控数据失败: {e}")
    
    async def _process_video_events(self, events: List[Dict[str, Any]]):
        """
        处理视频事件
        
        Args:
            events: 视频事件列表
        """
        for event in events:
            event_type = event.get('type')
            event_data = event.get('data', {})
            timestamp = event.get('timestamp', 0) / 1000
            
            # 记录事件
            self.session_data['video_events'].append({
                'type': event_type,
                'data': event_data,
                'timestamp': timestamp
            })
            
            # 处理不同类型的事件
            if event_type == 'play':
                await self._handle_video_play(event_data, timestamp)
            elif event_type == 'pause':
                await self._handle_video_pause(event_data, timestamp)
            elif event_type == 'ended':
                await self._handle_video_complete(event_data, timestamp)
            elif event_type == 'seeked':
                await self._handle_video_seek(event_data, timestamp)
    
    async def _handle_video_play(self, data: Dict[str, Any], timestamp: float):
        """
        处理视频播放事件
        
        Args:
            data: 事件数据
            timestamp: 时间戳
        """
        self.video_state['is_playing'] = True
        self.video_state['play_start_time'] = timestamp
        self.video_state['current_time'] = data.get('currentTime', 0)
        self.video_state['duration'] = data.get('duration', 0)
        
        # 触发回调
        await self._trigger_callbacks('video_start', {
            'video_src': data.get('src', ''),
            'current_time': data.get('currentTime', 0),
            'timestamp': timestamp
        })
        
        print(f"[学习监控] 视频开始播放: {data.get('currentTime', 0):.1f}s")
    
    async def _handle_video_pause(self, data: Dict[str, Any], timestamp: float):
        """
        处理视频暂停事件
        
        Args:
            data: 事件数据
            timestamp: 时间戳
        """
        if self.video_state['is_playing'] and self.video_state['play_start_time']:
            # 计算播放时间
            play_duration = timestamp - self.video_state['play_start_time']
            self.video_state['total_play_time'] += play_duration
        
        self.video_state['is_playing'] = False
        self.video_state['pause_count'] += 1
        self.video_state['current_time'] = data.get('currentTime', 0)
        
        # 触发回调
        await self._trigger_callbacks('video_pause', {
            'video_src': data.get('src', ''),
            'current_time': data.get('currentTime', 0),
            'timestamp': timestamp
        })
        
        print(f"[学习监控] 视频暂停: {data.get('currentTime', 0):.1f}s")
    
    async def _handle_video_complete(self, data: Dict[str, Any], timestamp: float):
        """
        处理视频完成事件
        
        Args:
            data: 事件数据
            timestamp: 时间戳
        """
        if self.video_state['is_playing'] and self.video_state['play_start_time']:
            # 计算最后一段播放时间
            play_duration = timestamp - self.video_state['play_start_time']
            self.video_state['total_play_time'] += play_duration
        
        self.video_state['is_playing'] = False
        
        # 触发回调
        await self._trigger_callbacks('video_complete', {
            'video_src': data.get('src', ''),
            'duration': data.get('duration', 0),
            'total_play_time': self.video_state['total_play_time'],
            'timestamp': timestamp
        })
        
        print(f"[学习监控] 视频播放完成: {data.get('duration', 0):.1f}s")
    
    async def _handle_video_seek(self, data: Dict[str, Any], timestamp: float):
        """
        处理视频跳转事件
        
        Args:
            data: 事件数据
            timestamp: 时间戳
        """
        self.video_state['seek_count'] += 1
        self.video_state['current_time'] = data.get('currentTime', 0)
        
        print(f"[学习监控] 视频跳转: {data.get('currentTime', 0):.1f}s")
    
    async def _update_video_state(self):
        """更新视频状态"""
        try:
            # 获取当前视频状态
            video_info = await self.page.evaluate("""
                () => {
                    const video = document.querySelector('video');
                    if (video) {
                        return {
                            currentTime: video.currentTime,
                            duration: video.duration,
                            paused: video.paused,
                            ended: video.ended,
                            src: video.src
                        };
                    }
                    return null;
                }
            """)
            
            if video_info:
                self.video_state['current_video'] = video_info.get('src', '')
                self.video_state['current_time'] = video_info.get('currentTime', 0)
                self.video_state['duration'] = video_info.get('duration', 0)
                
                # 更新播放状态
                is_playing = not video_info.get('paused', True) and not video_info.get('ended', False)
                if is_playing != self.video_state['is_playing']:
                    if is_playing:
                        self.video_state['play_start_time'] = time.time()
                    elif self.video_state['play_start_time']:
                        play_duration = time.time() - self.video_state['play_start_time']
                        self.video_state['total_play_time'] += play_duration
                    
                    self.video_state['is_playing'] = is_playing
            
        except Exception as e:
            print(f"[学习监控] 更新视频状态失败: {e}")
    
    async def _analyze_learning_behavior(self):
        """分析学习行为"""
        try:
            current_time = time.time()
            
            # 计算活跃时间和空闲时间
            if self.session_data['last_activity']:
                time_since_activity = current_time - self.session_data['last_activity']
                
                if time_since_activity < 30:  # 30秒内有活动认为是活跃
                    self.session_data['active_time'] += self.monitor_interval
                else:
                    self.session_data['idle_time'] += self.monitor_interval
            
            # 分析视频观看行为
            if len(self.session_data['video_events']) > 0:
                await self._analyze_video_behavior()
            
        except Exception as e:
            print(f"[学习监控] 分析学习行为失败: {e}")
    
    async def _analyze_video_behavior(self):
        """分析视频观看行为"""
        try:
            # 计算观看效率
            if self.video_state['duration'] > 0 and self.video_state['total_play_time'] > 0:
                watch_efficiency = min(self.video_state['total_play_time'] / self.video_state['duration'], 1.0)
                
                # 检测异常行为
                if self.video_state['seek_count'] > 10:  # 频繁跳转
                    await self._log_behavior_event("频繁跳转视频", "WARNING")
                
                if self.video_state['pause_count'] > 20:  # 频繁暂停
                    await self._log_behavior_event("频繁暂停视频", "WARNING")
                
                if watch_efficiency < 0.3:  # 观看效率低
                    await self._log_behavior_event("视频观看效率低", "INFO")
            
        except Exception as e:
            print(f"[学习监控] 分析视频行为失败: {e}")
    
    async def _check_idle_status(self):
        """检查空闲状态"""
        try:
            current_time = time.time()
            
            if self.session_data['last_activity']:
                idle_duration = current_time - self.session_data['last_activity']
                
                if idle_duration > self.idle_timeout:
                    # 触发空闲回调
                    await self._trigger_callbacks('session_idle', {
                        'idle_duration': idle_duration,
                        'timestamp': current_time
                    })
                    
                    await self._log_behavior_event(f"用户空闲 {idle_duration:.0f} 秒", "INFO")
            
        except Exception as e:
            print(f"[学习监控] 检查空闲状态失败: {e}")
    
    async def _trigger_callbacks(self, event_type: str, data: Dict[str, Any]):
        """
        触发回调函数
        
        Args:
            event_type: 事件类型
            data: 事件数据
        """
        if event_type in self.callbacks:
            for callback in self.callbacks[event_type]:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback(data)
                    else:
                        callback(data)
                except Exception as e:
                    print(f"[学习监控] 回调函数执行失败: {e}")
    
    async def _save_session_data(self):
        """保存会话数据"""
        try:
            if not self.current_session:
                return
            
            session_summary = {
                'session_id': self.current_session['session_id'],
                'user_phone': self.current_session['user_phone'],
                'course_id': self.current_session['course_id'],
                'start_time': self.current_session['start_time'],
                'end_time': datetime.now(),
                'total_time': self.session_data['total_time'],
                'active_time': self.session_data['active_time'],
                'idle_time': self.session_data['idle_time'],
                'video_play_time': self.video_state['total_play_time'],
                'video_pause_count': self.video_state['pause_count'],
                'video_seek_count': self.video_state['seek_count'],
                'event_count': len(self.session_data['video_events'])
            }
            
            # 记录会话日志
            await self._log_behavior_event(f"学习会话结束: 总时长{session_summary['total_time']:.0f}秒", "INFO")
            
            print(f"[学习监控] 会话数据已保存: {session_summary['session_id']}")
            
        except Exception as e:
            print(f"[学习监控] 保存会话数据失败: {e}")
    
    async def _log_behavior_event(self, message: str, level: str = "INFO"):
        """
        记录行为事件日志
        
        Args:
            message: 日志消息
            level: 日志级别
        """
        try:
            if self.current_session:
                log_data = {
                    'user_phone': self.current_session['user_phone'],
                    'level': level,
                    'message': f"[学习监控] {message}",
                    'module': 'LearningMonitor',
                    'timestamp': datetime.now()
                }
                
                self.log_dao.create(log_data)
            
        except Exception as e:
            print(f"[学习监控] 记录行为日志失败: {e}")
    
    def _reset_session_data(self):
        """重置会话数据"""
        self.session_data = {
            'start_time': None,
            'total_time': 0,
            'active_time': 0,
            'idle_time': 0,
            'video_events': [],
            'interaction_events': [],
            'last_activity': None
        }
        
        self.video_state = {
            'current_video': None,
            'is_playing': False,
            'current_time': 0,
            'duration': 0,
            'play_start_time': None,
            'total_play_time': 0,
            'pause_count': 0,
            'seek_count': 0
        }
    
    def add_callback(self, event_type: str, callback: Callable):
        """
        添加事件回调函数
        
        Args:
            event_type: 事件类型
            callback: 回调函数
        """
        if event_type in self.callbacks:
            self.callbacks[event_type].append(callback)
            print(f"[学习监控] 添加事件回调: {event_type}")
        else:
            print(f"[学习监控] 未知事件类型: {event_type}")
    
    def get_session_stats(self) -> Dict[str, Any]:
        """
        获取会话统计信息
        
        Returns:
            Dict[str, Any]: 会话统计
        """
        return {
            'is_monitoring': self.is_monitoring,
            'session_data': self.session_data.copy(),
            'video_state': self.video_state.copy(),
            'current_session': self.current_session
        }
