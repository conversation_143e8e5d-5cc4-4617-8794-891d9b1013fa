# coding:utf-8
"""
学习引擎核心类

该模块提供自动化学习引擎的核心功能，整合浏览器管理、
登录处理、学习控制、状态监控和API捕获等功能。

主要功能：
- 统一的学习引擎接口
- 组件协调和管理
- 学习流程控制
- 错误处理和恢复

类说明：
- StudyEngine: 学习引擎核心类
"""

import asyncio
from typing import Optional, Dict, Any, Callable
from datetime import datetime

from .browser import BrowserManager
from .login import LoginHandler
from .study import StudyHandler
from .monitor import StatusMonitor
from ..api.manager import api_capture_manager
from ..common.config_loader import get_study_config
from ..database.dao import DAOFactory


class StudyEngine:
    """
    学习引擎核心类
    
    整合所有自动化组件，提供统一的学习引擎接口
    """
    
    def __init__(self):
        """初始化学习引擎"""
        self.browser_manager = BrowserManager()
        self.login_handler = LoginHandler()
        self.study_handler = StudyHandler()
        self.status_monitor = StatusMonitor()
        
        self.is_running = False
        self.current_user = {}
        self.engine_status = "stopped"
        
        # 配置
        cfg = get_study_config()
        self.auto_login = True  # 默认自动登录
        self.auto_study = True  # 默认自动学习
        self.api_capture_enabled = cfg.get(cfg.apiCaptureEnabled)
        
        # 数据库DAO
        self.user_dao = DAOFactory.get_user_dao()
        self.log_dao = DAOFactory.get_log_dao()
        
        # 事件回调
        self.callbacks = {
            'on_start': [],
            'on_stop': [],
            'on_login': [],
            'on_study_start': [],
            'on_study_complete': [],
            'on_error': []
        }
        
        print("[引擎] 学习引擎初始化完成")
    
    async def start(self) -> bool:
        """
        启动学习引擎
        
        Returns:
            bool: 启动是否成功
        """
        if self.is_running:
            print("[引擎] 学习引擎已在运行中")
            return True
        
        try:
            print("[引擎] 正在启动学习引擎...")
            self.engine_status = "starting"
            
            # 启动浏览器
            if not await self.browser_manager.start():
                raise Exception("浏览器启动失败")
            
            # 获取页面对象
            page = self.browser_manager.get_page()
            if not page:
                raise Exception("无法获取页面对象")
            
            # 设置页面对象到各个组件
            self.login_handler.set_page(page)
            self.study_handler.set_page(page)
            self.status_monitor.set_page(page)
            api_capture_manager.set_page(page)
            
            # 启动状态监控
            await self.status_monitor.start_monitoring()
            
            # 启动API捕获（如果启用）
            if self.api_capture_enabled:
                await api_capture_manager.start_capture()
            
            self.is_running = True
            self.engine_status = "running"
            
            # 触发启动回调
            await self._trigger_callbacks('on_start', {'timestamp': datetime.now()})
            
            print("[引擎] 学习引擎启动成功")
            return True
            
        except Exception as e:
            print(f"[引擎] 学习引擎启动失败: {e}")
            self.engine_status = "error"
            await self._trigger_callbacks('on_error', {'error': str(e), 'context': 'start'})
            await self.cleanup()
            return False
    
    async def stop(self):
        """停止学习引擎"""
        if not self.is_running:
            print("[引擎] 学习引擎未在运行中")
            return
        
        try:
            print("[引擎] 正在停止学习引擎...")
            self.engine_status = "stopping"
            
            # 停止学习
            await self.study_handler.stop_study()
            
            # 停止API捕获
            if self.api_capture_enabled:
                await api_capture_manager.stop_capture()
            
            # 停止状态监控
            await self.status_monitor.stop_monitoring()
            
            # 停止浏览器
            await self.browser_manager.stop()
            
            self.is_running = False
            self.engine_status = "stopped"
            
            # 触发停止回调
            await self._trigger_callbacks('on_stop', {'timestamp': datetime.now()})
            
            print("[引擎] 学习引擎已停止")
            
        except Exception as e:
            print(f"[引擎] 停止学习引擎失败: {e}")
            await self._trigger_callbacks('on_error', {'error': str(e), 'context': 'stop'})
        finally:
            await self.cleanup()
    
    async def cleanup(self):
        """清理资源"""
        try:
            await self.browser_manager.cleanup()
            self.current_user = {}
            self.engine_status = "stopped"
        except Exception as e:
            print(f"[引擎] 清理资源失败: {e}")
    
    async def login(self, phone: str, password: str, auto_captcha: bool = True) -> bool:
        """
        用户登录
        
        Args:
            phone: 手机号
            password: 密码
            auto_captcha: 是否自动识别验证码
            
        Returns:
            bool: 登录是否成功
        """
        if not self.is_running:
            print("[引擎] 学习引擎未启动")
            return False
        
        try:
            print(f"[引擎] 开始用户登录: {phone}")
            
            # 执行登录
            success = await self.login_handler.login(phone, password, auto_captcha)
            
            if success:
                self.current_user = {'phone': phone}
                
                # 设置用户信息到API捕获管理器
                api_capture_manager.set_user_phone(phone)
                
                # 记录登录日志
                await self._log_action(phone, f"用户登录成功: {phone}")
                
                # 触发登录回调
                await self._trigger_callbacks('on_login', {
                    'user_phone': phone,
                    'timestamp': datetime.now(),
                    'success': True
                })
                
                print(f"[引擎] 用户登录成功: {phone}")
            else:
                await self._log_action(phone, f"用户登录失败: {phone}", "ERROR")
                
                # 触发登录回调
                await self._trigger_callbacks('on_login', {
                    'user_phone': phone,
                    'timestamp': datetime.now(),
                    'success': False
                })
                
                print(f"[引擎] 用户登录失败: {phone}")
            
            return success
            
        except Exception as e:
            print(f"[引擎] 登录过程异常: {e}")
            await self._trigger_callbacks('on_error', {'error': str(e), 'context': 'login'})
            return False
    
    async def start_study(self, user_phone: Optional[str] = None) -> bool:
        """
        开始自动学习
        
        Args:
            user_phone: 用户手机号（可选，使用当前登录用户）
            
        Returns:
            bool: 启动是否成功
        """
        if not self.is_running:
            print("[引擎] 学习引擎未启动")
            return False
        
        # 使用当前用户或指定用户
        phone = user_phone or self.current_user.get('phone', '')
        if not phone:
            print("[引擎] 未指定用户或当前无登录用户")
            return False
        
        try:
            print(f"[引擎] 开始自动学习: {phone}")
            
            # 触发学习开始回调
            await self._trigger_callbacks('on_study_start', {
                'user_phone': phone,
                'timestamp': datetime.now()
            })
            
            # 开始学习
            success = await self.study_handler.start_study(phone)
            
            if success:
                await self._log_action(phone, "自动学习完成")
                
                # 触发学习完成回调
                await self._trigger_callbacks('on_study_complete', {
                    'user_phone': phone,
                    'timestamp': datetime.now(),
                    'success': True
                })
                
                print(f"[引擎] 自动学习完成: {phone}")
            else:
                await self._log_action(phone, "自动学习失败", "ERROR")
                
                # 触发学习完成回调
                await self._trigger_callbacks('on_study_complete', {
                    'user_phone': phone,
                    'timestamp': datetime.now(),
                    'success': False
                })
                
                print(f"[引擎] 自动学习失败: {phone}")
            
            return success
            
        except Exception as e:
            print(f"[引擎] 学习过程异常: {e}")
            await self._trigger_callbacks('on_error', {'error': str(e), 'context': 'study'})
            return False
    
    async def stop_study(self):
        """停止自动学习"""
        await self.study_handler.stop_study()
        print("[引擎] 自动学习已停止")
    
    async def auto_run(self, phone: str, password: str) -> bool:
        """
        自动运行（登录+学习）
        
        Args:
            phone: 手机号
            password: 密码
            
        Returns:
            bool: 运行是否成功
        """
        try:
            print(f"[引擎] 开始自动运行: {phone}")
            
            # 自动登录
            if self.auto_login:
                login_success = await self.login(phone, password)
                if not login_success:
                    print("[引擎] 自动登录失败，停止运行")
                    return False
            
            # 自动学习
            if self.auto_study:
                study_success = await self.start_study(phone)
                if not study_success:
                    print("[引擎] 自动学习失败")
                    return False
            
            print(f"[引擎] 自动运行完成: {phone}")
            return True
            
        except Exception as e:
            print(f"[引擎] 自动运行异常: {e}")
            await self._trigger_callbacks('on_error', {'error': str(e), 'context': 'auto_run'})
            return False
    
    def add_callback(self, event: str, callback: Callable):
        """
        添加事件回调函数
        
        Args:
            event: 事件名称
            callback: 回调函数
        """
        if event in self.callbacks:
            self.callbacks[event].append(callback)
            print(f"[引擎] 添加事件回调: {event}")
        else:
            print(f"[引擎] 未知事件类型: {event}")
    
    async def _trigger_callbacks(self, event: str, data: Dict[str, Any]):
        """
        触发事件回调
        
        Args:
            event: 事件名称
            data: 事件数据
        """
        if event in self.callbacks:
            for callback in self.callbacks[event]:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback(data)
                    else:
                        callback(data)
                except Exception as e:
                    print(f"[引擎] 回调函数执行失败: {e}")
    
    async def _log_action(self, user_phone: str, message: str, level: str = "INFO"):
        """
        记录操作日志
        
        Args:
            user_phone: 用户手机号
            message: 日志消息
            level: 日志级别
        """
        try:
            log_data = {
                'user_phone': user_phone,
                'level': level,
                'message': f"[学习引擎] {message}",
                'module': 'StudyEngine',
                'timestamp': datetime.now()
            }
            
            self.log_dao.create(log_data)
            
        except Exception as e:
            print(f"[引擎] 记录日志失败: {e}")
    
    def get_status(self) -> Dict[str, Any]:
        """
        获取引擎状态
        
        Returns:
            Dict[str, Any]: 状态信息
        """
        return {
            "is_running": self.is_running,
            "engine_status": self.engine_status,
            "current_user": self.current_user,
            "browser_status": self.browser_manager.get_status(),
            "login_status": self.login_handler.get_status(),
            "study_status": self.study_handler.get_status(),
            "monitor_status": self.status_monitor.get_metrics(),
            "api_capture_status": api_capture_manager.get_status(),
            "auto_login": self.auto_login,
            "auto_study": self.auto_study,
            "api_capture_enabled": self.api_capture_enabled
        }
    
    def get_components(self) -> Dict[str, Any]:
        """
        获取组件实例
        
        Returns:
            Dict[str, Any]: 组件实例
        """
        return {
            "browser_manager": self.browser_manager,
            "login_handler": self.login_handler,
            "study_handler": self.study_handler,
            "status_monitor": self.status_monitor,
            "api_capture_manager": api_capture_manager
        }


# 全局学习引擎实例
study_engine = StudyEngine()
