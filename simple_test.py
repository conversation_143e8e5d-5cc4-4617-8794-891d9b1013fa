#!/usr/bin/env python
# coding:utf-8
"""
简单测试脚本，用于验证基本功能
"""

def test_basic_imports():
    """测试基本导入"""
    print("测试基本导入...")
    
    try:
        import sys
        print(f"✅ Python版本: {sys.version}")
        
        import sqlite3
        print("✅ sqlite3")
        
        import threading
        print("✅ threading")
        
        import json
        print("✅ json")
        
        import datetime
        print("✅ datetime")
        
        import pathlib
        print("✅ pathlib")
        
        print("✅ 基本导入测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 基本导入测试失败: {e}")
        return False


def test_ddddocr():
    """测试ddddocr"""
    print("\n测试ddddocr...")
    
    try:
        import ddddocr
        print("✅ ddddocr导入成功")
        
        ocr = ddddocr.DdddOcr()
        print("✅ ddddocr实例创建成功")
        
        return True
        
    except Exception as e:
        print(f"❌ ddddocr测试失败: {e}")
        return False


def test_database():
    """测试数据库功能"""
    print("\n测试数据库功能...")
    
    try:
        import sqlite3
        import tempfile
        import os
        
        # 创建临时数据库
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as f:
            db_path = f.name
        
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 创建测试表
        cursor.execute('''
            CREATE TABLE test_table (
                id INTEGER PRIMARY KEY,
                name TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 插入测试数据
        cursor.execute("INSERT INTO test_table (name) VALUES (?)", ("测试用户",))
        conn.commit()
        
        # 查询数据
        cursor.execute("SELECT * FROM test_table")
        result = cursor.fetchone()
        
        if result and result[1] == "测试用户":
            print("✅ 数据库基本操作测试通过")
            success = True
        else:
            print("❌ 数据库查询结果不正确")
            success = False
        
        # 清理
        conn.close()
        os.unlink(db_path)
        
        return success
        
    except Exception as e:
        print(f"❌ 数据库测试失败: {e}")
        return False


def test_config():
    """测试配置系统"""
    print("\n测试配置系统...")
    
    try:
        # 不导入qfluentwidgets相关的配置，只测试基本配置
        import json
        import tempfile
        import os
        
        # 创建测试配置
        test_config = {
            "browser": {
                "type": "chrome",
                "headless": False
            },
            "ocr": {
                "primary_engine": "ddddocr",
                "timeout": 10
            }
        }
        
        # 保存配置到临时文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(test_config, f, ensure_ascii=False, indent=2)
            config_path = f.name
        
        # 读取配置
        with open(config_path, 'r', encoding='utf-8') as f:
            loaded_config = json.load(f)
        
        if loaded_config == test_config:
            print("✅ 配置文件读写测试通过")
            success = True
        else:
            print("❌ 配置文件内容不匹配")
            success = False
        
        # 清理
        os.unlink(config_path)
        
        return success
        
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False


def main():
    """主函数"""
    print("=" * 60)
    print("学习工具基础功能测试")
    print("=" * 60)
    
    tests = [
        test_basic_imports,
        test_ddddocr,
        test_database,
        test_config
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有基础功能测试通过！")
        return True
    else:
        print("💥 部分测试失败，请检查环境配置")
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
