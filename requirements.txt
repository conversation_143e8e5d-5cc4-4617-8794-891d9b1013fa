# 学习工具依赖包列表
# 使用命令安装: pip install -r requirements.txt

# 核心GUI框架
PySide6>=6.4.0
qfluentwidgets>=1.1.0

# 浏览器自动化
playwright>=1.40.0

# OCR识别引擎
ddddocr>=1.6.0
onnxruntime>=1.22.0
opencv-python-headless>=4.12.0
requests>=2.28.0
Pillow>=9.0.0

# 数据库和数据处理
sqlite3  # Python内置，无需安装

# 系统监控和性能
psutil>=5.9.0

# 日志和配置
configparser  # Python内置，无需安装

# 并发和异步
asyncio  # Python内置，无需安装
threading  # Python内置，无需安装
concurrent.futures  # Python内置，无需安装

# 数据导出
openpyxl>=3.0.0
pandas>=1.5.0

# 网络请求
urllib3>=1.26.0
certifi>=2022.0.0

# 加密和安全
hashlib  # Python内置，无需安装
secrets  # Python内置，无需安装

# 时间和日期处理
datetime  # Python内置，无需安装

# 文件和路径处理
pathlib  # Python内置，无需安装
shutil  # Python内置，无需安装

# JSON处理
json  # Python内置，无需安装

# 正则表达式
re  # Python内置，无需安装

# 开发和测试依赖（可选）
pytest>=7.0.0
pytest-cov>=4.0.0
pytest-xdist>=3.0.0

# 构建和打包（可选）
pyinstaller>=5.0.0
setuptools>=65.0.0
wheel>=0.38.0

# 代码质量（可选）
flake8>=5.0.0
black>=22.0.0
isort>=5.10.0

# 文档生成（可选）
sphinx>=5.0.0
sphinx-rtd-theme>=1.0.0
