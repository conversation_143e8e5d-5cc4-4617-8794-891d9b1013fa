# coding:utf-8
"""
课程管理器

该模块提供课程管理功能，包括课程数据获取、
课程信息管理、学习状态跟踪等。

主要功能：
- 课程数据获取和解析
- 课程信息存储和管理
- 学习状态跟踪
- 课程进度统计

类说明：
- CourseManager: 课程管理器类
"""

import asyncio
from typing import Optional, Dict, Any, List
from datetime import datetime
from playwright.async_api import Page

from ..common.config_loader import get_study_config
from ..database.dao import DAOFactory
from ..api.manager import api_capture_manager


class CourseManager:
    """
    课程管理器类
    
    提供课程数据管理和学习流程控制功能
    """
    
    def __init__(self):
        """初始化课程管理器"""
        self.page: Optional[Page] = None
        
        # 配置
        cfg = get_study_config()
        self.base_url = cfg.get(cfg.baseUrl)
        self.course_list_url = f"{self.base_url}/course/list"
        self.course_detail_url = f"{self.base_url}/course/detail"
        
        # 数据库DAO
        self.course_dao = DAOFactory.get_course_dao()
        self.log_dao = DAOFactory.get_log_dao()
        
        # 课程数据缓存
        self.course_cache: Dict[str, Dict[str, Any]] = {}
        self.cache_timeout = 300  # 5分钟缓存
        
        print("[课程管理] 课程管理器初始化完成")
    
    def set_page(self, page: Page):
        """
        设置Playwright页面对象
        
        Args:
            page: Playwright页面对象
        """
        self.page = page
        print("[课程管理] 页面对象已设置")
    
    async def get_user_courses(self, user_phone: str, force_refresh: bool = False) -> List[Dict[str, Any]]:
        """
        获取用户课程列表
        
        Args:
            user_phone: 用户手机号
            force_refresh: 是否强制刷新
            
        Returns:
            List[Dict[str, Any]]: 课程列表
        """
        try:
            print(f"[课程管理] 获取用户课程列表: {user_phone}")
            
            # 检查缓存
            cache_key = f"courses_{user_phone}"
            if not force_refresh and cache_key in self.course_cache:
                cache_data = self.course_cache[cache_key]
                if (datetime.now().timestamp() - cache_data['timestamp']) < self.cache_timeout:
                    print("[课程管理] 使用缓存数据")
                    return cache_data['courses']
            
            # 从页面获取课程数据
            courses = await self._fetch_courses_from_page()
            
            if not courses:
                # 从数据库获取历史数据
                courses = self._get_courses_from_db(user_phone)
                print("[课程管理] 使用数据库历史数据")
            else:
                # 保存到数据库
                await self._save_courses_to_db(user_phone, courses)
                
                # 更新缓存
                self.course_cache[cache_key] = {
                    'courses': courses,
                    'timestamp': datetime.now().timestamp()
                }
                
                print(f"[课程管理] 获取到 {len(courses)} 门课程")
            
            return courses
            
        except Exception as e:
            print(f"[课程管理] 获取用户课程失败: {e}")
            return []
    
    async def _fetch_courses_from_page(self) -> List[Dict[str, Any]]:
        """
        从页面获取课程数据
        
        Returns:
            List[Dict[str, Any]]: 课程列表
        """
        if not self.page:
            print("[课程管理] 页面对象未设置")
            return []
        
        try:
            # 导航到课程列表页面
            await self.page.goto(self.course_list_url)
            await self.page.wait_for_load_state("networkidle")
            
            # 等待课程列表加载
            await self.page.wait_for_selector('.course-list', timeout=10000)
            
            # 提取课程数据
            courses_data = await self.page.evaluate("""
                () => {
                    const courses = [];
                    const courseElements = document.querySelectorAll('.course-item');
                    
                    courseElements.forEach(element => {
                        try {
                            const course = {
                                id: element.getAttribute('data-course-id') || '',
                                title: element.querySelector('.course-title')?.textContent?.trim() || '',
                                description: element.querySelector('.course-desc')?.textContent?.trim() || '',
                                duration: element.querySelector('.course-duration')?.textContent?.trim() || '',
                                progress: element.querySelector('.course-progress')?.textContent?.trim() || '0%',
                                status: element.querySelector('.course-status')?.textContent?.trim() || '',
                                thumbnail: element.querySelector('.course-thumbnail img')?.src || '',
                                url: element.querySelector('.course-link')?.href || ''
                            };
                            
                            if (course.id && course.title) {
                                courses.push(course);
                            }
                        } catch (e) {
                            console.error('解析课程数据失败:', e);
                        }
                    });
                    
                    return courses;
                }
            """)
            
            return courses_data or []
            
        except Exception as e:
            print(f"[课程管理] 从页面获取课程数据失败: {e}")
            return []
    
    def _get_courses_from_db(self, user_phone: str) -> List[Dict[str, Any]]:
        """
        从数据库获取课程数据
        
        Args:
            user_phone: 用户手机号
            
        Returns:
            List[Dict[str, Any]]: 课程列表
        """
        try:
            courses = self.course_dao.get_by_user(user_phone)
            return courses or []
            
        except Exception as e:
            print(f"[课程管理] 从数据库获取课程失败: {e}")
            return []
    
    async def _save_courses_to_db(self, user_phone: str, courses: List[Dict[str, Any]]):
        """
        保存课程数据到数据库
        
        Args:
            user_phone: 用户手机号
            courses: 课程列表
        """
        try:
            for course in courses:
                course_data = {
                    'user_phone': user_phone,
                    'course_id': course.get('id', ''),
                    'title': course.get('title', ''),
                    'description': course.get('description', ''),
                    'duration': course.get('duration', ''),
                    'thumbnail': course.get('thumbnail', ''),
                    'url': course.get('url', ''),
                    'status': course.get('status', ''),
                    'updated_at': datetime.now()
                }
                
                # 检查是否已存在
                existing = self.course_dao.get_by_id(course.get('id', ''))
                if existing:
                    self.course_dao.update(course.get('id', ''), course_data)
                else:
                    course_data['created_at'] = datetime.now()
                    self.course_dao.create(course_data)
            
            print(f"[课程管理] 保存 {len(courses)} 门课程到数据库")
            
        except Exception as e:
            print(f"[课程管理] 保存课程到数据库失败: {e}")
    
    async def get_course_detail(self, course_id: str, user_phone: str) -> Optional[Dict[str, Any]]:
        """
        获取课程详细信息
        
        Args:
            course_id: 课程ID
            user_phone: 用户手机号
            
        Returns:
            Optional[Dict[str, Any]]: 课程详细信息
        """
        try:
            print(f"[课程管理] 获取课程详情: {course_id}")
            
            # 先从数据库获取基本信息
            course = self.course_dao.get_by_id(course_id)
            if not course:
                print("[课程管理] 课程不存在")
                return None
            
            # 从页面获取详细信息
            if self.page:
                detail_info = await self._fetch_course_detail_from_page(course_id)
                if detail_info:
                    course.update(detail_info)
            
            # 获取学习进度
            progress = self.progress_dao.get_by_course(user_phone, course_id)
            if progress:
                course['learning_progress'] = progress
            
            return course
            
        except Exception as e:
            print(f"[课程管理] 获取课程详情失败: {e}")
            return None
    
    async def _fetch_course_detail_from_page(self, course_id: str) -> Optional[Dict[str, Any]]:
        """
        从页面获取课程详细信息
        
        Args:
            course_id: 课程ID
            
        Returns:
            Optional[Dict[str, Any]]: 课程详细信息
        """
        try:
            # 导航到课程详情页面
            detail_url = f"{self.course_detail_url}?id={course_id}"
            await self.page.goto(detail_url)
            await self.page.wait_for_load_state("networkidle")
            
            # 等待详情页面加载
            await self.page.wait_for_selector('.course-detail', timeout=10000)
            
            # 提取详细信息
            detail_data = await self.page.evaluate("""
                () => {
                    try {
                        const detail = {
                            chapters: [],
                            total_videos: 0,
                            total_duration: '',
                            instructor: '',
                            category: '',
                            difficulty: '',
                            tags: []
                        };
                        
                        // 获取章节信息
                        const chapterElements = document.querySelectorAll('.chapter-item');
                        chapterElements.forEach(element => {
                            const chapter = {
                                id: element.getAttribute('data-chapter-id') || '',
                                title: element.querySelector('.chapter-title')?.textContent?.trim() || '',
                                videos: [],
                                duration: element.querySelector('.chapter-duration')?.textContent?.trim() || ''
                            };
                            
                            // 获取视频信息
                            const videoElements = element.querySelectorAll('.video-item');
                            videoElements.forEach(videoEl => {
                                const video = {
                                    id: videoEl.getAttribute('data-video-id') || '',
                                    title: videoEl.querySelector('.video-title')?.textContent?.trim() || '',
                                    duration: videoEl.querySelector('.video-duration')?.textContent?.trim() || '',
                                    url: videoEl.querySelector('.video-link')?.href || '',
                                    watched: videoEl.classList.contains('watched')
                                };
                                
                                if (video.id && video.title) {
                                    chapter.videos.push(video);
                                }
                            });
                            
                            if (chapter.id && chapter.title) {
                                detail.chapters.push(chapter);
                            }
                        });
                        
                        // 获取其他信息
                        detail.total_videos = document.querySelector('.total-videos')?.textContent?.trim() || '0';
                        detail.total_duration = document.querySelector('.total-duration')?.textContent?.trim() || '';
                        detail.instructor = document.querySelector('.instructor-name')?.textContent?.trim() || '';
                        detail.category = document.querySelector('.course-category')?.textContent?.trim() || '';
                        detail.difficulty = document.querySelector('.course-difficulty')?.textContent?.trim() || '';
                        
                        // 获取标签
                        const tagElements = document.querySelectorAll('.course-tag');
                        tagElements.forEach(tag => {
                            const tagText = tag.textContent?.trim();
                            if (tagText) {
                                detail.tags.push(tagText);
                            }
                        });
                        
                        return detail;
                        
                    } catch (e) {
                        console.error('解析课程详情失败:', e);
                        return null;
                    }
                }
            """)
            
            return detail_data
            
        except Exception as e:
            print(f"[课程管理] 从页面获取课程详情失败: {e}")
            return None
    
    async def start_course_learning(self, course_id: str, user_phone: str) -> bool:
        """
        开始课程学习

        Args:
            course_id: 课程ID
            user_phone: 用户手机号

        Returns:
            bool: 是否成功开始
        """
        try:
            print(f"[课程管理] 开始学习课程: {course_id}")

            # 获取课程详情
            course_info = await self._get_course_detail_from_page(course_id)
            if not course_info:
                print(f"[课程管理] 获取课程详情失败: {course_id}")
                return False

            # 记录学习开始
            course_title = course_info.get('title', course_id)
            await self._log_course_action(user_phone, f"开始学习课程: {course_title}")

            # 导航到课程页面
            if not await self._navigate_to_course(course_id):
                print(f"[课程管理] 导航到课程页面失败: {course_id}")
                return False

            # 处理课程内容
            success = await self._process_course_content(user_phone, course_id, course_info)

            if success:
                await self.update_learning_progress(user_phone, course_id, completed=True)
                print(f"[课程管理] 课程学习完成: {course_title}")
            else:
                print(f"[课程管理] 课程学习失败: {course_id}")

            return success

        except Exception as e:
            print(f"[课程管理] 课程学习异常: {e}")
            return False
    
    async def update_learning_progress(self, user_phone: str, course_id: str,
                                     completed: bool = False) -> bool:
        """
        更新学习进度 - 简化版本

        Args:
            user_phone: 用户手机号
            course_id: 课程ID
            completed: 是否完成

        Returns:
            bool: 是否更新成功
        """
        try:
            # 简单记录进度更新
            status = "完成" if completed else "进行中"
            await self._log_course_action(user_phone, f"学习进度更新: {course_id} - {status}")

            print(f"[课程管理] 学习进度已更新: {course_id} - {status}")
            return True

        except Exception as e:
            print(f"[课程管理] 更新学习进度失败: {e}")
            return False
    
    def get_learning_progress(self, user_phone: str, course_id: str) -> Optional[Dict[str, Any]]:
        """
        获取学习进度
        
        Args:
            user_phone: 用户手机号
            course_id: 课程ID
            
        Returns:
            Optional[Dict[str, Any]]: 学习进度
        """
        try:
            return self.progress_dao.get_by_course(user_phone, course_id)
            
        except Exception as e:
            print(f"[课程管理] 获取学习进度失败: {e}")
            return None
    
    def get_user_learning_stats(self, user_phone: str) -> Dict[str, Any]:
        """
        获取用户学习统计
        
        Args:
            user_phone: 用户手机号
            
        Returns:
            Dict[str, Any]: 学习统计
        """
        try:
            # 获取所有课程进度
            all_progress = self.progress_dao.get_by_user(user_phone)
            
            stats = {
                'total_courses': len(all_progress),
                'completed_courses': 0,
                'learning_courses': 0,
                'total_videos_watched': 0,
                'total_learning_time': 0,
                'average_progress': 0.0
            }
            
            total_progress = 0.0
            
            for progress in all_progress:
                status = progress.get('status', '')
                if status == 'completed':
                    stats['completed_courses'] += 1
                elif status == 'learning':
                    stats['learning_courses'] += 1
                
                stats['total_videos_watched'] += progress.get('completed_videos', 0)
                total_progress += progress.get('progress_percentage', 0.0)
            
            # 计算平均进度
            if stats['total_courses'] > 0:
                stats['average_progress'] = total_progress / stats['total_courses']
            
            return stats
            
        except Exception as e:
            print(f"[课程管理] 获取学习统计失败: {e}")
            return {}
    
    async def _log_course_action(self, user_phone: str, message: str, level: str = "INFO"):
        """
        记录课程操作日志
        
        Args:
            user_phone: 用户手机号
            message: 日志消息
            level: 日志级别
        """
        try:
            log_data = {
                'user_phone': user_phone,
                'level': level,
                'message': f"[课程管理] {message}",
                'module': 'CourseManager',
                'timestamp': datetime.now()
            }
            
            self.log_dao.create(log_data)
            
        except Exception as e:
            print(f"[课程管理] 记录课程日志失败: {e}")
    
    async def _navigate_to_course(self, course_id: str) -> bool:
        """
        导航到课程页面

        Args:
            course_id: 课程ID

        Returns:
            bool: 是否成功
        """
        try:
            if not self.page:
                print("[课程管理] 页面对象未设置")
                return False

            course_url = f"{self.course_detail_url}/{course_id}"
            await self.page.goto(course_url)
            await self.page.wait_for_load_state("networkidle")

            print(f"[课程管理] 成功导航到课程页面: {course_id}")
            return True

        except Exception as e:
            print(f"[课程管理] 导航到课程页面失败: {e}")
            return False

    async def _process_course_content(self, user_phone: str, course_id: str, course_info: dict) -> bool:
        """
        处理课程内容

        Args:
            user_phone: 用户手机号
            course_id: 课程ID
            course_info: 课程信息

        Returns:
            bool: 是否处理成功
        """
        try:
            print(f"[课程管理] 开始处理课程内容: {course_info.get('title', course_id)}")

            # 查找视频元素
            video_element = await self.page.query_selector('video')
            if video_element:
                print("[课程管理] 找到视频内容，开始播放")

                # 尝试播放视频
                await video_element.evaluate("video => video.play()")

                # 监控视频播放
                await self._monitor_video_playback(video_element)

            else:
                # 如果没有视频，可能是文档或其他类型的课程
                print("[课程管理] 未找到视频内容，处理其他类型课程")
                await asyncio.sleep(5)  # 简单等待

            print(f"[课程管理] 课程内容处理完成: {course_id}")
            return True

        except Exception as e:
            print(f"[课程管理] 处理课程内容失败: {e}")
            return False

    async def _monitor_video_playback(self, video_element):
        """
        监控视频播放

        Args:
            video_element: 视频元素
        """
        try:
            print("[课程管理] 开始监控视频播放")

            while True:
                # 获取视频状态
                is_paused = await video_element.evaluate("video => video.paused")
                current_time = await video_element.evaluate("video => video.currentTime")
                duration = await video_element.evaluate("video => video.duration")

                if duration and current_time:
                    progress = (current_time / duration) * 100
                    print(f"[课程管理] 视频播放进度: {progress:.1f}%")

                    # 如果播放完成
                    if progress >= 95:
                        print("[课程管理] 视频播放完成")
                        break

                # 如果暂停，尝试继续播放
                if is_paused:
                    await video_element.evaluate("video => video.play()")

                await asyncio.sleep(5)

        except Exception as e:
            print(f"[课程管理] 监控视频播放失败: {e}")

    def clear_cache(self, user_phone: Optional[str] = None):
        """
        清空缓存

        Args:
            user_phone: 用户手机号（可选，清空指定用户缓存）
        """
        if user_phone:
            cache_key = f"courses_{user_phone}"
            if cache_key in self.course_cache:
                del self.course_cache[cache_key]
                print(f"[课程管理] 清空用户缓存: {user_phone}")
        else:
            self.course_cache.clear()
            print("[课程管理] 清空所有缓存")


# 全局课程管理器实例
course_manager = CourseManager()
