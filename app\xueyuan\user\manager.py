# coding:utf-8
"""
用户管理器

该模块提供用户管理功能，整合用户验证、服务和数据访问，
提供统一的用户管理接口。

主要功能：
- 用户注册和创建
- 用户信息查询和更新
- 用户状态管理
- 批量用户操作

类说明：
- UserManager: 用户管理器类
"""

from typing import Optional, Dict, Any, List
from datetime import datetime

from .validator import UserValidator
from .service import user_service
from ..common.constants import UserStatus
from ..database.dao import DAOFactory


class UserManager:
    """
    用户管理器类
    
    提供统一的用户管理接口
    """
    
    def __init__(self):
        """初始化用户管理器"""
        self.validator = UserValidator()
        self.service = user_service
        self.user_dao = DAOFactory.get_user_dao()
        self.log_dao = DAOFactory.get_log_dao()
        
        print("[用户管理] 用户管理器初始化完成")
    
    async def create_user(self, phone: str, name: str, password: str, 
                         status: str = UserStatus.ACTIVE.value) -> Dict[str, Any]:
        """
        创建用户
        
        Args:
            phone: 手机号
            name: 姓名
            password: 密码
            status: 用户状态
            
        Returns:
            Dict[str, Any]: 创建结果
        """
        result = {
            'success': False,
            'user': None,
            'message': '',
            'errors': []
        }
        
        try:
            # 清理输入数据
            phone = self.validator.sanitize_phone(phone)
            name = self.validator.sanitize_name(name)
            
            # 验证用户数据
            user_data = {
                'phone': phone,
                'name': name,
                'password': password,
                'status': status
            }
            
            validation_result = self.validator.validate_user_data(user_data)
            if not validation_result['valid']:
                result['errors'] = validation_result['errors']
                result['message'] = '用户数据验证失败'
                return result
            
            # 检查用户是否已存在
            existing_user = self.user_dao.get_by_phone(phone)
            if existing_user:
                result['message'] = '用户已存在'
                result['errors'] = ['手机号已被注册']
                return result
            
            # 加密密码
            password_data = self.service.hash_password(password)
            
            # 创建用户数据
            create_data = {
                'phone': phone,
                'name': name,
                'password_hash': password_data['hash'],
                'salt': password_data['salt'],
                'status': status,
                'created_at': datetime.now(),
                'login_count': 0
            }
            
            # 保存用户
            user = self.user_dao.create(create_data)
            if user:
                # 记录创建日志
                await self._log_user_action(phone, f"用户创建成功: {name}")
                
                result.update({
                    'success': True,
                    'user': self.service._sanitize_user_data(user),
                    'message': '用户创建成功'
                })
                
                print(f"[用户管理] 用户创建成功: {phone}")
            else:
                result['message'] = '用户创建失败'
            
            return result
            
        except Exception as e:
            print(f"[用户管理] 创建用户失败: {e}")
            result['message'] = '创建过程发生错误'
            result['errors'] = [str(e)]
            return result
    
    def get_user(self, phone: str) -> Optional[Dict[str, Any]]:
        """
        获取用户信息
        
        Args:
            phone: 手机号
            
        Returns:
            Optional[Dict[str, Any]]: 用户信息
        """
        try:
            phone = self.validator.sanitize_phone(phone)
            user = self.user_dao.get_by_phone(phone)
            
            if user:
                return self.service._sanitize_user_data(user)
            return None
            
        except Exception as e:
            print(f"[用户管理] 获取用户失败: {e}")
            return None
    
    async def update_user(self, phone: str, update_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        更新用户信息
        
        Args:
            phone: 手机号
            update_data: 更新数据
            
        Returns:
            Dict[str, Any]: 更新结果
        """
        result = {
            'success': False,
            'user': None,
            'message': '',
            'errors': []
        }
        
        try:
            phone = self.validator.sanitize_phone(phone)
            
            # 检查用户是否存在
            existing_user = self.user_dao.get_by_phone(phone)
            if not existing_user:
                result['message'] = '用户不存在'
                return result
            
            # 验证更新数据
            validation_result = self.validator.validate_update_data(update_data)
            if not validation_result['valid']:
                result['errors'] = validation_result['errors']
                result['message'] = '更新数据验证失败'
                return result
            
            # 处理密码更新
            processed_data = update_data.copy()
            if 'password' in processed_data:
                password_data = self.service.hash_password(processed_data['password'])
                processed_data['password_hash'] = password_data['hash']
                processed_data['salt'] = password_data['salt']
                del processed_data['password']
            
            # 清理姓名
            if 'name' in processed_data:
                processed_data['name'] = self.validator.sanitize_name(processed_data['name'])
            
            # 添加更新时间
            processed_data['updated_at'] = datetime.now()
            
            # 执行更新
            updated_user = self.user_dao.update(phone, processed_data)
            if updated_user:
                # 记录更新日志
                update_fields = list(update_data.keys())
                await self._log_user_action(phone, f"用户信息更新: {', '.join(update_fields)}")
                
                result.update({
                    'success': True,
                    'user': self.service._sanitize_user_data(updated_user),
                    'message': '用户信息更新成功'
                })
                
                print(f"[用户管理] 用户更新成功: {phone}")
            else:
                result['message'] = '用户更新失败'
            
            return result
            
        except Exception as e:
            print(f"[用户管理] 更新用户失败: {e}")
            result['message'] = '更新过程发生错误'
            result['errors'] = [str(e)]
            return result
    
    async def delete_user(self, phone: str) -> Dict[str, Any]:
        """
        删除用户
        
        Args:
            phone: 手机号
            
        Returns:
            Dict[str, Any]: 删除结果
        """
        result = {
            'success': False,
            'message': ''
        }
        
        try:
            phone = self.validator.sanitize_phone(phone)
            
            # 检查用户是否存在
            existing_user = self.user_dao.get_by_phone(phone)
            if not existing_user:
                result['message'] = '用户不存在'
                return result
            
            # 执行删除
            success = self.user_dao.delete(phone)
            if success:
                # 记录删除日志
                await self._log_user_action(phone, "用户删除成功")
                
                result.update({
                    'success': True,
                    'message': '用户删除成功'
                })
                
                print(f"[用户管理] 用户删除成功: {phone}")
            else:
                result['message'] = '用户删除失败'
            
            return result
            
        except Exception as e:
            print(f"[用户管理] 删除用户失败: {e}")
            result['message'] = '删除过程发生错误'
            return result
    
    def list_users(self, status: Optional[str] = None, limit: int = 100, 
                   offset: int = 0) -> List[Dict[str, Any]]:
        """
        获取用户列表
        
        Args:
            status: 用户状态过滤
            limit: 返回数量限制
            offset: 偏移量
            
        Returns:
            List[Dict[str, Any]]: 用户列表
        """
        try:
            users = self.user_dao.list_all(limit=limit, offset=offset)
            
            # 过滤状态
            if status:
                users = [user for user in users if user.get('status') == status]
            
            # 清理敏感数据
            return [self.service._sanitize_user_data(user) for user in users]
            
        except Exception as e:
            print(f"[用户管理] 获取用户列表失败: {e}")
            return []
    
    def search_users(self, keyword: str, limit: int = 50) -> List[Dict[str, Any]]:
        """
        搜索用户
        
        Args:
            keyword: 搜索关键词
            limit: 返回数量限制
            
        Returns:
            List[Dict[str, Any]]: 搜索结果
        """
        try:
            users = self.user_dao.list_all(limit=limit)
            
            # 简单的关键词搜索
            keyword = keyword.lower()
            results = []
            
            for user in users:
                if (keyword in user.get('phone', '').lower() or 
                    keyword in user.get('name', '').lower()):
                    results.append(self.service._sanitize_user_data(user))
            
            return results
            
        except Exception as e:
            print(f"[用户管理] 搜索用户失败: {e}")
            return []
    
    async def change_user_status(self, phone: str, new_status: str) -> Dict[str, Any]:
        """
        更改用户状态
        
        Args:
            phone: 手机号
            new_status: 新状态
            
        Returns:
            Dict[str, Any]: 更改结果
        """
        # 验证状态值
        if new_status not in [s.value for s in UserStatus]:
            return {
                'success': False,
                'message': '无效的状态值'
            }
        
        return await self.update_user(phone, {'status': new_status})
    
    async def authenticate(self, phone: str, password: str) -> Dict[str, Any]:
        """
        用户认证
        
        Args:
            phone: 手机号
            password: 密码
            
        Returns:
            Dict[str, Any]: 认证结果
        """
        # 验证登录数据
        login_data = {'phone': phone, 'password': password}
        validation_result = self.validator.validate_login_data(login_data)
        
        if not validation_result['valid']:
            return {
                'success': False,
                'message': '登录数据验证失败',
                'errors': validation_result['errors']
            }
        
        # 执行认证
        return await self.service.authenticate_user(phone, password)
    
    def get_user_stats(self) -> Dict[str, Any]:
        """
        获取用户统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            users = self.user_dao.list_all()
            
            stats = {
                'total_users': len(users),
                'active_users': 0,
                'disabled_users': 0,
                'recent_registrations': 0,
                'recent_logins': 0
            }
            
            current_time = datetime.now()
            
            for user in users:
                status = user.get('status')
                if status == UserStatus.ACTIVE.value:
                    stats['active_users'] += 1
                elif status == UserStatus.DISABLED.value:
                    stats['disabled_users'] += 1
                
                # 最近7天注册
                created_at = user.get('created_at')
                if created_at and isinstance(created_at, datetime):
                    if (current_time - created_at).days <= 7:
                        stats['recent_registrations'] += 1
                
                # 最近7天登录
                last_login = user.get('last_login_at')
                if last_login and isinstance(last_login, datetime):
                    if (current_time - last_login).days <= 7:
                        stats['recent_logins'] += 1
            
            return stats
            
        except Exception as e:
            print(f"[用户管理] 获取用户统计失败: {e}")
            return {}
    
    async def _log_user_action(self, user_phone: str, message: str, level: str = "INFO"):
        """
        记录用户操作日志
        
        Args:
            user_phone: 用户手机号
            message: 日志消息
            level: 日志级别
        """
        try:
            log_data = {
                'user_phone': user_phone,
                'level': level,
                'message': f"[用户管理] {message}",
                'module': 'UserManager',
                'timestamp': datetime.now()
            }
            
            self.log_dao.create(log_data)
            
        except Exception as e:
            print(f"[用户管理] 记录用户日志失败: {e}")


# 全局用户管理器实例
user_manager = UserManager()
