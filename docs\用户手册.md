# 学习工具用户手册

## 目录

1. [软件介绍](#软件介绍)
2. [系统要求](#系统要求)
3. [安装指南](#安装指南)
4. [快速开始](#快速开始)
5. [功能详解](#功能详解)
6. [设置配置](#设置配置)
7. [常见问题](#常见问题)
8. [技术支持](#技术支持)

## 软件介绍

学习工具是一款基于PySide6开发的自动化学习辅助软件，主要功能包括：

### 核心功能
- **自动化学习**：支持自动登录、课程学习、进度跟踪
- **智能识别**：集成双OCR引擎，自动识别验证码
- **API捕获**：实时捕获学习平台API数据
- **用户管理**：支持多用户批量管理
- **进度监控**：实时监控学习进度和状态
- **日志记录**：详细的操作日志和错误记录

### 技术特点
- 现代化界面设计，支持亮色/暗色主题
- 高性能并发处理，支持多任务同时执行
- 智能异常处理和自动重试机制
- 完整的数据备份和恢复功能

## 系统要求

### 最低配置
- **操作系统**：Windows 10 (64位) 或更高版本
- **处理器**：Intel Core i3 或 AMD 同等级别
- **内存**：4GB RAM
- **存储空间**：500MB 可用空间
- **网络**：稳定的互联网连接

### 推荐配置
- **操作系统**：Windows 11 (64位)
- **处理器**：Intel Core i5 或 AMD 同等级别
- **内存**：8GB RAM 或更多
- **存储空间**：2GB 可用空间
- **网络**：高速宽带连接

### 软件依赖
- Python 3.8 或更高版本
- Chrome 或 Edge 浏览器
- .NET Framework 4.7.2 或更高版本

## 安装指南

### 方式一：直接运行（推荐）

1. **下载软件包**
   - 从官方网站下载最新版本的软件包
   - 解压到任意目录（建议：`C:\学习工具\`）

2. **安装依赖**
   ```bash
   # 打开命令提示符，进入软件目录
   cd C:\学习工具
   
   # 安装Python依赖
   pip install -r requirements.txt
   
   # 安装浏览器驱动
   playwright install chromium
   ```

3. **运行软件**
   ```bash
   python main.py
   ```

### 方式二：开发环境安装

1. **克隆代码库**
   ```bash
   git clone https://github.com/your-repo/learning-tool.git
   cd learning-tool
   ```

2. **创建虚拟环境**
   ```bash
   python -m venv venv
   venv\Scripts\activate
   ```

3. **安装依赖**
   ```bash
   pip install -r requirements.txt
   playwright install chromium
   ```

4. **运行软件**
   ```bash
   python main.py
   ```

## 快速开始

### 首次启动

1. **启动软件**
   - 双击 `main.py` 或在命令行运行 `python main.py`
   - 软件将自动创建必要的配置文件和数据库

2. **基础设置**
   - 点击右上角的设置按钮 ⚙️
   - 配置浏览器类型（推荐Chrome）
   - 设置OCR引擎参数
   - 配置日志级别

3. **添加用户**
   - 切换到"用户管理"标签页
   - 点击"添加用户"按钮
   - 填写用户信息（手机号、密码、姓名）
   - 点击"保存"

### 开始学习

1. **选择用户**
   - 在用户列表中选择要学习的用户
   - 检查用户状态是否为"活跃"

2. **启动自动化**
   - 点击工具栏中的"开始学习"按钮 ▶️
   - 软件将自动打开浏览器并开始学习流程

3. **监控进度**
   - 在状态栏查看实时进度
   - 切换到"日志"标签页查看详细信息

## 功能详解

### 用户管理

#### 添加用户
1. 点击"用户管理" → "添加用户"
2. 填写必要信息：
   - **手机号**：登录账号（必填）
   - **密码**：登录密码（必填）
   - **姓名**：用户姓名（必填）
   - **状态**：用户状态（活跃/暂停）

#### 批量导入
1. 准备Excel文件，包含以下列：
   - 手机号、密码、姓名、状态
2. 点击"批量导入" → 选择文件
3. 确认导入信息并执行

#### 用户操作
- **编辑**：双击用户行或点击编辑按钮
- **删除**：选择用户后点击删除按钮
- **激活/暂停**：右键菜单选择状态操作

### 自动化学习

#### 学习流程
1. **自动登录**：使用OCR识别验证码自动登录
2. **课程检测**：自动检测可学习的课程
3. **视频学习**：自动播放视频并监控进度
4. **进度更新**：实时更新学习进度到数据库

#### 并发控制
- 支持多用户同时学习
- 智能任务调度和资源分配
- 自动错误恢复和重试

#### 异常处理
- 网络异常自动重连
- 验证码识别失败自动重试
- 浏览器崩溃自动恢复

### API捕获

#### 功能说明
- 实时捕获学习平台的API请求
- 自动解析和存储API响应数据
- 支持数据导出和分析

#### 使用方法
1. 在设置中启用API捕获
2. 配置目标API地址
3. 开始学习时自动捕获数据

### 日志系统

#### 日志级别
- **调试**：详细的调试信息
- **信息**：一般操作信息
- **警告**：需要注意的情况
- **错误**：错误信息
- **严重**：严重错误

#### 日志查看
1. 切换到"日志"标签页
2. 使用过滤器筛选日志：
   - 按时间范围过滤
   - 按日志级别过滤
   - 按用户过滤
   - 按模块过滤

#### 日志导出
1. 在日志界面点击"导出"按钮
2. 选择导出格式：Excel、CSV、JSON、TXT
3. 选择导出路径并确认

## 设置配置

### 系统设置

#### 基本设置
- **异步登录**：是否启用异步登录模式
- **必修课程数量**：需要完成的必修课程数量
- **选修课程数量**：需要完成的选修课程数量
- **延迟时间**：操作间隔时间（秒）
- **重试次数**：失败后的重试次数

#### 浏览器设置
- **浏览器类型**：Chrome、Firefox、Edge
- **无头模式**：是否在后台运行浏览器
- **禁用图片**：是否禁用图片加载以提高速度
- **用户代理**：自定义浏览器用户代理

### OCR设置

#### 主引擎设置
- **主引擎**：Ddddocr（推荐）或百度OCR
- **超时时间**：OCR识别超时时间（秒）

#### 百度OCR设置
- **API Key**：百度OCR API密钥
- **Secret Key**：百度OCR密钥

### 日志设置

#### 日志级别
- **控制台日志级别**：控制台输出的最低日志级别
- **文件日志级别**：文件记录的最低日志级别
- **数据库日志级别**：数据库存储的最低日志级别

#### 日志选项
- **启用控制台日志**：是否在控制台输出日志
- **启用文件日志**：是否保存日志到文件
- **启用数据库日志**：是否保存日志到数据库

### 并发控制

#### 线程池设置
- **最大工作线程**：同时运行的最大线程数
- **任务队列大小**：任务队列的最大容量

#### 并发限制
- **最大并发用户**：同时学习的最大用户数
- **API请求限制**：每秒最大API请求数

## 常见问题

### 安装问题

**Q: 提示缺少Python依赖包？**
A: 运行 `pip install -r requirements.txt` 安装所有依赖包。

**Q: 浏览器驱动安装失败？**
A: 运行 `playwright install chromium` 手动安装浏览器驱动。

**Q: 软件无法启动？**
A: 检查Python版本是否为3.8或更高，确保所有依赖已正确安装。

### 使用问题

**Q: 验证码识别失败？**
A: 
1. 检查OCR引擎设置
2. 尝试切换到百度OCR
3. 调整OCR超时时间

**Q: 自动登录失败？**
A: 
1. 检查用户名密码是否正确
2. 确认网络连接正常
3. 查看日志了解具体错误

**Q: 学习进度不更新？**
A: 
1. 检查数据库连接
2. 确认用户状态为活跃
3. 重启软件重新尝试

**Q: 软件运行缓慢？**
A: 
1. 减少并发用户数量
2. 启用无头模式
3. 禁用图片加载

### 错误处理

**Q: 出现数据库错误？**
A: 
1. 检查数据库文件权限
2. 重新初始化数据库
3. 恢复数据库备份

**Q: 浏览器崩溃？**
A: 
1. 更新浏览器到最新版本
2. 清理浏览器缓存
3. 重启软件

## 技术支持

### 联系方式
- **邮箱**：<EMAIL>
- **QQ群**：123456789
- **微信群**：扫描二维码加入

### 反馈问题
提交问题时请包含以下信息：
1. 软件版本号
2. 操作系统版本
3. 详细的错误描述
4. 相关的日志信息
5. 重现步骤

### 更新日志
请访问官方网站查看最新的更新日志和版本信息。

### 开源信息
本软件基于开源协议发布，源代码托管在GitHub上，欢迎贡献代码和提出建议。

---

**版本**：v1.0.0  
**更新时间**：2025年1月14日  
**文档版本**：1.0
