# coding:utf-8
"""
性能测试

测试系统在各种负载条件下的性能表现。

测试内容：
- 数据库操作性能
- 并发用户处理性能
- 内存使用监控
- 响应时间测试
- 系统资源使用
"""

import pytest
import time
import threading
import psutil
import gc
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor, as_completed

from app.xueyuan.database.dao import UserDAO, CourseDAO, LogDAO
from app.xueyuan.user.manager import UserManager
from app.xueyuan.logging import log_manager
from app.xueyuan.common.concurrency import ThreadPoolManager
from tests.conftest import create_test_user, create_test_course, create_test_log


@pytest.mark.performance
@pytest.mark.slow
class TestDatabasePerformance:
    """数据库性能测试"""
    
    def test_user_crud_performance(self, test_db):
        """测试用户CRUD操作性能"""
        user_dao = UserDAO()
        
        # 测试批量创建用户性能
        start_time = time.time()
        
        users_count = 1000
        for i in range(users_count):
            user = create_test_user(phone=f"138000{i:05d}")
            user_dao.create(user)
        
        create_time = time.time() - start_time
        
        # 测试批量查询性能
        start_time = time.time()
        
        for i in range(100):  # 查询100个用户
            phone = f"138000{i:05d}"
            user = user_dao.get_by_phone(phone)
            assert user is not None
        
        query_time = time.time() - start_time
        
        # 测试批量更新性能
        start_time = time.time()
        
        for i in range(100):
            phone = f"138000{i:05d}"
            user = user_dao.get_by_phone(phone)
            user.compulsory_progress = 50
            user_dao.update(user)
        
        update_time = time.time() - start_time
        
        # 性能断言
        assert create_time < 30.0  # 1000个用户创建应在30秒内完成
        assert query_time < 5.0    # 100次查询应在5秒内完成
        assert update_time < 10.0  # 100次更新应在10秒内完成
        
        print(f"创建{users_count}个用户耗时: {create_time:.2f}秒")
        print(f"查询100个用户耗时: {query_time:.2f}秒")
        print(f"更新100个用户耗时: {update_time:.2f}秒")
    
    def test_log_insertion_performance(self, test_db):
        """测试日志插入性能"""
        log_dao = LogDAO()
        
        start_time = time.time()
        
        # 插入大量日志
        logs_count = 5000
        for i in range(logs_count):
            log_data = create_test_log(
                message=f"性能测试日志 {i}",
                user_phone=f"138000{i % 100:05d}"
            )
            log_dao.create(log_data)
        
        insert_time = time.time() - start_time
        
        # 测试日志查询性能
        start_time = time.time()
        
        logs = log_dao.get_logs({}, limit=1000)
        
        query_time = time.time() - start_time
        
        # 性能断言
        assert insert_time < 60.0  # 5000条日志插入应在60秒内完成
        assert query_time < 5.0    # 查询1000条日志应在5秒内完成
        assert len(logs) == 1000
        
        print(f"插入{logs_count}条日志耗时: {insert_time:.2f}秒")
        print(f"查询1000条日志耗时: {query_time:.2f}秒")
    
    def test_concurrent_database_access(self, test_db):
        """测试并发数据库访问性能"""
        user_dao = UserDAO()
        
        def create_user_worker(worker_id):
            """工作线程函数"""
            results = []
            for i in range(50):  # 每个线程创建50个用户
                phone = f"139{worker_id:02d}{i:03d}"
                user = create_test_user(phone=phone)
                start_time = time.time()
                result = user_dao.create(user)
                end_time = time.time()
                results.append((result, end_time - start_time))
            return results
        
        # 使用10个线程并发创建用户
        start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(create_user_worker, i) for i in range(10)]
            
            all_results = []
            for future in as_completed(futures):
                results = future.result()
                all_results.extend(results)
        
        total_time = time.time() - start_time
        
        # 验证结果
        successful_operations = sum(1 for result, _ in all_results if result)
        average_time = sum(duration for _, duration in all_results) / len(all_results)
        
        assert successful_operations == 500  # 所有操作都应该成功
        assert total_time < 30.0  # 总时间应在30秒内
        assert average_time < 0.1  # 平均每次操作应在0.1秒内
        
        print(f"并发创建500个用户总耗时: {total_time:.2f}秒")
        print(f"平均每次操作耗时: {average_time:.4f}秒")


@pytest.mark.performance
@pytest.mark.slow
class TestConcurrencyPerformance:
    """并发处理性能测试"""
    
    def test_thread_pool_performance(self):
        """测试线程池性能"""
        thread_pool = ThreadPoolManager(max_workers=10)
        
        def cpu_intensive_task(n):
            """CPU密集型任务"""
            result = 0
            for i in range(n):
                result += i * i
            return result
        
        # 测试任务提交和执行性能
        start_time = time.time()
        
        tasks = []
        for i in range(100):
            future = thread_pool.submit_task(
                cpu_intensive_task,
                args=(10000,),
                priority=1
            )
            tasks.append(future)
        
        # 等待所有任务完成
        results = []
        for future in tasks:
            result = future.result(timeout=30)
            results.append(result)
        
        total_time = time.time() - start_time
        
        # 验证结果
        assert len(results) == 100
        assert all(isinstance(r, int) for r in results)
        assert total_time < 20.0  # 应在20秒内完成
        
        print(f"线程池执行100个任务耗时: {total_time:.2f}秒")
        
        # 清理
        thread_pool.shutdown()
    
    def test_concurrent_user_simulation(self, test_db):
        """测试并发用户模拟"""
        user_manager = UserManager()
        
        def simulate_user_activity(user_id):
            """模拟用户活动"""
            phone = f"150000{user_id:05d}"
            
            # 创建用户
            user_data = {
                "phone": phone,
                "password": "test_password",
                "name": f"性能测试用户{user_id}",
                "status": "active"
            }
            
            start_time = time.time()
            
            # 创建用户
            create_result = user_manager.create_user(user_data)
            
            # 获取用户
            user = user_manager.get_user(phone)
            
            # 更新用户进度
            if user:
                user.compulsory_progress = 25
                update_result = user_manager.update_user(user)
            else:
                update_result = False
            
            # 再次获取用户
            final_user = user_manager.get_user(phone)
            
            end_time = time.time()
            
            return {
                "user_id": user_id,
                "create_result": create_result,
                "update_result": update_result,
                "final_user": final_user,
                "duration": end_time - start_time
            }
        
        # 模拟50个并发用户
        start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=20) as executor:
            futures = [executor.submit(simulate_user_activity, i) for i in range(50)]
            
            results = []
            for future in as_completed(futures):
                result = future.result()
                results.append(result)
        
        total_time = time.time() - start_time
        
        # 验证结果
        successful_creates = sum(1 for r in results if r["create_result"])
        successful_updates = sum(1 for r in results if r["update_result"])
        average_duration = sum(r["duration"] for r in results) / len(results)
        
        assert successful_creates == 50
        assert successful_updates == 50
        assert total_time < 30.0
        assert average_duration < 2.0
        
        print(f"50个并发用户活动总耗时: {total_time:.2f}秒")
        print(f"平均每个用户活动耗时: {average_duration:.2f}秒")


@pytest.mark.performance
@pytest.mark.slow
class TestMemoryPerformance:
    """内存性能测试"""
    
    def test_memory_usage_during_bulk_operations(self, test_db):
        """测试批量操作时的内存使用"""
        process = psutil.Process()
        
        # 记录初始内存使用
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        user_dao = UserDAO()
        
        # 执行大量数据库操作
        for batch in range(10):  # 10个批次
            batch_users = []
            
            # 创建一批用户对象
            for i in range(100):
                user = create_test_user(phone=f"160{batch:02d}{i:03d}")
                batch_users.append(user)
            
            # 批量插入
            for user in batch_users:
                user_dao.create(user)
            
            # 记录当前内存使用
            current_memory = process.memory_info().rss / 1024 / 1024
            memory_increase = current_memory - initial_memory
            
            print(f"批次 {batch + 1}: 内存使用 {current_memory:.2f}MB (+{memory_increase:.2f}MB)")
            
            # 内存使用不应该无限增长
            assert memory_increase < 500  # 内存增长不应超过500MB
            
            # 强制垃圾回收
            gc.collect()
        
        # 最终内存检查
        final_memory = process.memory_info().rss / 1024 / 1024
        total_increase = final_memory - initial_memory
        
        print(f"初始内存: {initial_memory:.2f}MB")
        print(f"最终内存: {final_memory:.2f}MB")
        print(f"总增长: {total_increase:.2f}MB")
        
        # 内存增长应该在合理范围内
        assert total_increase < 200  # 总内存增长不应超过200MB
    
    def test_memory_leak_detection(self, test_db):
        """测试内存泄漏检测"""
        import tracemalloc
        
        # 开始内存跟踪
        tracemalloc.start()
        
        user_dao = UserDAO()
        log_dao = LogDAO()
        
        # 执行重复操作
        for cycle in range(5):
            # 创建和删除用户
            for i in range(100):
                phone = f"170{cycle:02d}{i:03d}"
                user = create_test_user(phone=phone)
                user_dao.create(user)
            
            # 创建日志
            for i in range(200):
                log_data = create_test_log(
                    message=f"内存测试日志 {cycle}-{i}",
                    user_phone=f"170{cycle:02d}{i % 100:03d}"
                )
                log_dao.create(log_data)
            
            # 删除部分数据
            for i in range(50):
                phone = f"170{cycle:02d}{i:03d}"
                user_dao.delete(phone)
            
            # 记录内存快照
            snapshot = tracemalloc.take_snapshot()
            top_stats = snapshot.statistics('lineno')
            
            print(f"循环 {cycle + 1} 内存使用前10项:")
            for stat in top_stats[:10]:
                print(f"  {stat}")
        
        # 停止内存跟踪
        tracemalloc.stop()


@pytest.mark.performance
@pytest.mark.slow
class TestResponseTimePerformance:
    """响应时间性能测试"""
    
    def test_database_query_response_time(self, populated_test_db):
        """测试数据库查询响应时间"""
        user_dao = UserDAO()
        log_dao = LogDAO()
        
        # 测试用户查询响应时间
        response_times = []
        
        for i in range(100):
            start_time = time.time()
            user = user_dao.get_by_phone("13800000001")
            end_time = time.time()
            
            response_time = (end_time - start_time) * 1000  # 转换为毫秒
            response_times.append(response_time)
            
            assert user is not None
        
        # 计算统计信息
        avg_response_time = sum(response_times) / len(response_times)
        max_response_time = max(response_times)
        min_response_time = min(response_times)
        
        # 响应时间断言
        assert avg_response_time < 50  # 平均响应时间应小于50ms
        assert max_response_time < 200  # 最大响应时间应小于200ms
        
        print(f"用户查询平均响应时间: {avg_response_time:.2f}ms")
        print(f"用户查询最大响应时间: {max_response_time:.2f}ms")
        print(f"用户查询最小响应时间: {min_response_time:.2f}ms")
        
        # 测试日志查询响应时间
        log_response_times = []
        
        for i in range(50):
            start_time = time.time()
            logs = log_dao.get_logs({}, limit=100)
            end_time = time.time()
            
            response_time = (end_time - start_time) * 1000
            log_response_times.append(response_time)
            
            assert len(logs) > 0
        
        avg_log_response_time = sum(log_response_times) / len(log_response_times)
        
        assert avg_log_response_time < 100  # 日志查询平均响应时间应小于100ms
        
        print(f"日志查询平均响应时间: {avg_log_response_time:.2f}ms")
    
    def test_logging_performance(self, log_manager):
        """测试日志记录性能"""
        response_times = []
        
        # 测试日志记录响应时间
        for i in range(1000):
            start_time = time.time()
            
            log_manager.info(
                f"性能测试日志 {i}",
                module="PerformanceTest",
                user_phone="13800000001",
                extra_data={"test_id": i, "timestamp": datetime.now().isoformat()}
            )
            
            end_time = time.time()
            
            response_time = (end_time - start_time) * 1000
            response_times.append(response_time)
        
        # 等待日志写入完成
        time.sleep(1.0)
        
        # 计算统计信息
        avg_response_time = sum(response_times) / len(response_times)
        max_response_time = max(response_times)
        percentile_95 = sorted(response_times)[int(len(response_times) * 0.95)]
        
        # 性能断言
        assert avg_response_time < 10  # 平均日志记录时间应小于10ms
        assert percentile_95 < 50      # 95%的日志记录时间应小于50ms
        
        print(f"日志记录平均响应时间: {avg_response_time:.2f}ms")
        print(f"日志记录最大响应时间: {max_response_time:.2f}ms")
        print(f"日志记录95%响应时间: {percentile_95:.2f}ms")


@pytest.mark.performance
@pytest.mark.slow
class TestStressTest:
    """压力测试"""
    
    def test_system_under_load(self, test_db):
        """测试系统负载能力"""
        user_manager = UserManager()
        
        # 模拟高负载场景
        def stress_worker(worker_id):
            """压力测试工作函数"""
            results = []
            
            for i in range(20):  # 每个工作线程处理20个操作
                try:
                    # 创建用户
                    phone = f"180{worker_id:03d}{i:03d}"
                    user_data = {
                        "phone": phone,
                        "password": "stress_test",
                        "name": f"压力测试用户{worker_id}-{i}",
                        "status": "active"
                    }
                    
                    start_time = time.time()
                    
                    # 执行多个操作
                    create_result = user_manager.create_user(user_data)
                    user = user_manager.get_user(phone)
                    
                    if user:
                        user.compulsory_progress = 30
                        update_result = user_manager.update_user(user)
                    else:
                        update_result = False
                    
                    # 记录日志
                    log_manager.info(
                        f"压力测试操作 {worker_id}-{i}",
                        module="StressTest",
                        user_phone=phone
                    )
                    
                    end_time = time.time()
                    
                    results.append({
                        "success": create_result and update_result,
                        "duration": end_time - start_time
                    })
                    
                except Exception as e:
                    results.append({
                        "success": False,
                        "duration": 0,
                        "error": str(e)
                    })
            
            return results
        
        # 使用大量线程进行压力测试
        start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=50) as executor:
            futures = [executor.submit(stress_worker, i) for i in range(50)]
            
            all_results = []
            for future in as_completed(futures):
                try:
                    results = future.result(timeout=60)
                    all_results.extend(results)
                except Exception as e:
                    print(f"工作线程异常: {e}")
        
        total_time = time.time() - start_time
        
        # 分析结果
        total_operations = len(all_results)
        successful_operations = sum(1 for r in all_results if r["success"])
        success_rate = successful_operations / total_operations * 100
        
        durations = [r["duration"] for r in all_results if r["success"]]
        if durations:
            avg_duration = sum(durations) / len(durations)
            max_duration = max(durations)
        else:
            avg_duration = 0
            max_duration = 0
        
        # 压力测试断言
        assert success_rate >= 95  # 成功率应该至少95%
        assert total_time < 120    # 总时间应在2分钟内
        assert avg_duration < 5    # 平均操作时间应小于5秒
        
        print(f"压力测试结果:")
        print(f"  总操作数: {total_operations}")
        print(f"  成功操作数: {successful_operations}")
        print(f"  成功率: {success_rate:.2f}%")
        print(f"  总耗时: {total_time:.2f}秒")
        print(f"  平均操作时间: {avg_duration:.2f}秒")
        print(f"  最大操作时间: {max_duration:.2f}秒")
