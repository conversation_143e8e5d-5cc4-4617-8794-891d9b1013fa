# coding:utf-8
"""
用户服务

该模块提供用户业务逻辑服务，包括用户认证、
密码加密、状态管理等。

主要功能：
- 密码加密和验证
- 用户认证
- 状态管理
- 业务逻辑处理

类说明：
- UserService: 用户服务类
"""

import hashlib
import secrets
import time
from typing import Optional, Dict, Any, List
from datetime import datetime, timedelta

from ..common.constants import UserStatus
from ..database.dao import DAOFactory


class UserService:
    """
    用户服务类
    
    提供用户相关的业务逻辑服务
    """
    
    def __init__(self):
        """初始化用户服务"""
        self.user_dao = DAOFactory.get_user_dao()
        self.log_dao = DAOFactory.get_log_dao()
        
        # 密码加密配置
        self.salt_length = 32
        self.hash_iterations = 100000
        
        # 登录尝试限制
        self.max_login_attempts = 5
        self.lockout_duration = 30 * 60  # 30分钟
        
        # 会话管理
        self.session_timeout = 24 * 60 * 60  # 24小时
        self.active_sessions: Dict[str, Dict[str, Any]] = {}
        
        print("[用户服务] 用户服务初始化完成")
    
    def hash_password(self, password: str, salt: Optional[str] = None) -> Dict[str, str]:
        """
        加密密码
        
        Args:
            password: 明文密码
            salt: 盐值（可选）
            
        Returns:
            Dict[str, str]: 包含哈希值和盐值
        """
        if salt is None:
            salt = secrets.token_hex(self.salt_length)
        
        # 使用PBKDF2进行密码哈希
        password_hash = hashlib.pbkdf2_hmac(
            'sha256',
            password.encode('utf-8'),
            salt.encode('utf-8'),
            self.hash_iterations
        )
        
        return {
            'hash': password_hash.hex(),
            'salt': salt
        }
    
    def verify_password(self, password: str, stored_hash: str, salt: str) -> bool:
        """
        验证密码
        
        Args:
            password: 明文密码
            stored_hash: 存储的哈希值
            salt: 盐值
            
        Returns:
            bool: 密码是否正确
        """
        try:
            # 计算输入密码的哈希值
            password_hash = hashlib.pbkdf2_hmac(
                'sha256',
                password.encode('utf-8'),
                salt.encode('utf-8'),
                self.hash_iterations
            )
            
            # 比较哈希值
            return password_hash.hex() == stored_hash
            
        except Exception as e:
            print(f"[用户服务] 密码验证失败: {e}")
            return False
    
    async def authenticate_user(self, phone: str, password: str) -> Dict[str, Any]:
        """
        用户认证
        
        Args:
            phone: 手机号
            password: 密码
            
        Returns:
            Dict[str, Any]: 认证结果
        """
        result = {
            'success': False,
            'user': None,
            'message': '',
            'session_token': None
        }
        
        try:
            # 检查登录尝试限制
            if self._is_account_locked(phone):
                result['message'] = '账户已被锁定，请稍后再试'
                return result
            
            # 查找用户
            user = self.user_dao.get_by_phone(phone)
            if not user:
                self._record_failed_attempt(phone)
                result['message'] = '用户不存在'
                return result
            
            # 检查用户状态
            if user.get('status') == UserStatus.DISABLED.value:
                result['message'] = '账户已被禁用'
                return result
            
            # 验证密码
            stored_hash = user.get('password_hash', '')
            salt = user.get('salt', '')
            
            if not self.verify_password(password, stored_hash, salt):
                self._record_failed_attempt(phone)
                result['message'] = '密码错误'
                return result
            
            # 认证成功
            self._clear_failed_attempts(phone)
            
            # 更新最后登录时间
            self.user_dao.update(phone, {
                'last_login_at': datetime.now(),
                'login_count': user.get('login_count', 0) + 1
            })
            
            # 创建会话
            session_token = self._create_session(user)
            
            # 记录登录日志
            await self._log_user_action(phone, '用户登录成功')
            
            result.update({
                'success': True,
                'user': self._sanitize_user_data(user),
                'message': '登录成功',
                'session_token': session_token
            })
            
            print(f"[用户服务] 用户认证成功: {phone}")
            return result
            
        except Exception as e:
            print(f"[用户服务] 用户认证失败: {e}")
            result['message'] = '认证过程发生错误'
            return result
    
    def _is_account_locked(self, phone: str) -> bool:
        """
        检查账户是否被锁定
        
        Args:
            phone: 手机号
            
        Returns:
            bool: 是否被锁定
        """
        # 这里可以实现基于Redis或数据库的锁定机制
        # 简化实现，使用内存存储
        lock_key = f"login_attempts_{phone}"
        if hasattr(self, '_login_attempts'):
            attempts = self._login_attempts.get(lock_key, {})
            if attempts.get('count', 0) >= self.max_login_attempts:
                lock_time = attempts.get('lock_time', 0)
                if time.time() - lock_time < self.lockout_duration:
                    return True
        return False
    
    def _record_failed_attempt(self, phone: str):
        """
        记录失败尝试
        
        Args:
            phone: 手机号
        """
        if not hasattr(self, '_login_attempts'):
            self._login_attempts = {}
        
        lock_key = f"login_attempts_{phone}"
        attempts = self._login_attempts.get(lock_key, {'count': 0, 'lock_time': 0})
        attempts['count'] += 1
        
        if attempts['count'] >= self.max_login_attempts:
            attempts['lock_time'] = time.time()
        
        self._login_attempts[lock_key] = attempts
        print(f"[用户服务] 记录登录失败: {phone} (尝试次数: {attempts['count']})")
    
    def _clear_failed_attempts(self, phone: str):
        """
        清除失败尝试记录
        
        Args:
            phone: 手机号
        """
        if hasattr(self, '_login_attempts'):
            lock_key = f"login_attempts_{phone}"
            if lock_key in self._login_attempts:
                del self._login_attempts[lock_key]
    
    def _create_session(self, user: Dict[str, Any]) -> str:
        """
        创建用户会话
        
        Args:
            user: 用户信息
            
        Returns:
            str: 会话令牌
        """
        session_token = secrets.token_urlsafe(32)
        session_data = {
            'user_phone': user['phone'],
            'user_name': user['name'],
            'created_at': time.time(),
            'expires_at': time.time() + self.session_timeout,
            'last_activity': time.time()
        }
        
        self.active_sessions[session_token] = session_data
        print(f"[用户服务] 创建会话: {user['phone']}")
        return session_token
    
    def validate_session(self, session_token: str) -> Optional[Dict[str, Any]]:
        """
        验证会话
        
        Args:
            session_token: 会话令牌
            
        Returns:
            Optional[Dict[str, Any]]: 会话数据
        """
        if session_token not in self.active_sessions:
            return None
        
        session_data = self.active_sessions[session_token]
        current_time = time.time()
        
        # 检查会话是否过期
        if current_time > session_data['expires_at']:
            del self.active_sessions[session_token]
            return None
        
        # 更新最后活动时间
        session_data['last_activity'] = current_time
        return session_data
    
    def destroy_session(self, session_token: str) -> bool:
        """
        销毁会话
        
        Args:
            session_token: 会话令牌
            
        Returns:
            bool: 是否成功
        """
        if session_token in self.active_sessions:
            user_phone = self.active_sessions[session_token].get('user_phone', '')
            del self.active_sessions[session_token]
            print(f"[用户服务] 销毁会话: {user_phone}")
            return True
        return False
    
    def _sanitize_user_data(self, user: Dict[str, Any]) -> Dict[str, Any]:
        """
        清理用户数据（移除敏感信息）
        
        Args:
            user: 原始用户数据
            
        Returns:
            Dict[str, Any]: 清理后的用户数据
        """
        safe_fields = ['phone', 'name', 'status', 'created_at', 'last_login_at', 'login_count']
        return {field: user.get(field) for field in safe_fields if field in user}
    
    async def _log_user_action(self, user_phone: str, message: str, level: str = "INFO"):
        """
        记录用户操作日志
        
        Args:
            user_phone: 用户手机号
            message: 日志消息
            level: 日志级别
        """
        try:
            log_data = {
                'user_phone': user_phone,
                'level': level,
                'message': f"[用户服务] {message}",
                'module': 'UserService',
                'timestamp': datetime.now()
            }
            
            self.log_dao.create(log_data)
            
        except Exception as e:
            print(f"[用户服务] 记录用户日志失败: {e}")
    
    def get_user_status_info(self, phone: str) -> Dict[str, Any]:
        """
        获取用户状态信息
        
        Args:
            phone: 手机号
            
        Returns:
            Dict[str, Any]: 状态信息
        """
        try:
            user = self.user_dao.get_by_phone(phone)
            if not user:
                return {'exists': False}
            
            # 检查是否有活跃会话
            active_sessions = [
                token for token, data in self.active_sessions.items()
                if data['user_phone'] == phone
            ]
            
            return {
                'exists': True,
                'status': user.get('status'),
                'last_login': user.get('last_login_at'),
                'login_count': user.get('login_count', 0),
                'is_locked': self._is_account_locked(phone),
                'active_sessions': len(active_sessions),
                'created_at': user.get('created_at')
            }
            
        except Exception as e:
            print(f"[用户服务] 获取用户状态失败: {e}")
            return {'exists': False, 'error': str(e)}
    
    def cleanup_expired_sessions(self):
        """清理过期会话"""
        current_time = time.time()
        expired_tokens = [
            token for token, data in self.active_sessions.items()
            if current_time > data['expires_at']
        ]
        
        for token in expired_tokens:
            del self.active_sessions[token]
        
        if expired_tokens:
            print(f"[用户服务] 清理过期会话: {len(expired_tokens)}个")
    
    def get_active_sessions(self) -> List[Dict[str, Any]]:
        """
        获取活跃会话列表
        
        Returns:
            List[Dict[str, Any]]: 会话列表
        """
        sessions = []
        current_time = time.time()
        
        for token, data in self.active_sessions.items():
            if current_time <= data['expires_at']:
                sessions.append({
                    'token': token[:8] + '...',  # 只显示部分令牌
                    'user_phone': data['user_phone'],
                    'user_name': data['user_name'],
                    'created_at': datetime.fromtimestamp(data['created_at']),
                    'last_activity': datetime.fromtimestamp(data['last_activity']),
                    'expires_at': datetime.fromtimestamp(data['expires_at'])
                })
        
        return sessions
    
    def get_service_stats(self) -> Dict[str, Any]:
        """
        获取服务统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        return {
            'active_sessions_count': len(self.active_sessions),
            'max_login_attempts': self.max_login_attempts,
            'lockout_duration': self.lockout_duration,
            'session_timeout': self.session_timeout,
            'hash_iterations': self.hash_iterations
        }


# 全局用户服务实例
user_service = UserService()
