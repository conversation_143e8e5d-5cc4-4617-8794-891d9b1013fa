# coding:utf-8
"""
通用工具函数模块

提供项目中常用的工具函数，包括：
- ID生成
- 时间格式化
- 数据验证
- 字符串处理
等功能。
"""

import re
import uuid
import hashlib
import secrets
from datetime import datetime
from typing import Optional, Any


def generate_id() -> str:
    """
    生成唯一ID
    
    Returns:
        str: 32位唯一ID字符串
    """
    return uuid.uuid4().hex


def generate_short_id() -> str:
    """
    生成短ID
    
    Returns:
        str: 8位短ID字符串
    """
    return secrets.token_hex(4)


def format_time(timestamp: Optional[datetime] = None, format_str: str = "%Y-%m-%d %H:%M:%S") -> str:
    """
    格式化时间
    
    Args:
        timestamp: 时间戳，默认为当前时间
        format_str: 格式化字符串
        
    Returns:
        str: 格式化后的时间字符串
    """
    if timestamp is None:
        timestamp = datetime.now()
    return timestamp.strftime(format_str)


def validate_phone(phone: str) -> bool:
    """
    验证手机号格式
    
    Args:
        phone: 手机号字符串
        
    Returns:
        bool: 是否为有效手机号
    """
    if not phone:
        return False
    
    # 中国大陆手机号正则表达式
    pattern = r'^1[3-9]\d{9}$'
    return bool(re.match(pattern, phone))


def validate_email(email: str) -> bool:
    """
    验证邮箱格式
    
    Args:
        email: 邮箱字符串
        
    Returns:
        bool: 是否为有效邮箱
    """
    if not email:
        return False
    
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return bool(re.match(pattern, email))


def hash_password(password: str, salt: Optional[str] = None) -> tuple[str, str]:
    """
    哈希密码
    
    Args:
        password: 原始密码
        salt: 盐值，如果为None则自动生成
        
    Returns:
        tuple: (哈希后的密码, 盐值)
    """
    if salt is None:
        salt = secrets.token_hex(16)
    
    # 使用PBKDF2进行哈希
    hashed = hashlib.pbkdf2_hmac(
        'sha256',
        password.encode('utf-8'),
        salt.encode('utf-8'),
        100000  # 迭代次数
    )
    
    return hashed.hex(), salt


def verify_password(password: str, hashed_password: str, salt: str) -> bool:
    """
    验证密码
    
    Args:
        password: 原始密码
        hashed_password: 哈希后的密码
        salt: 盐值
        
    Returns:
        bool: 密码是否正确
    """
    hashed, _ = hash_password(password, salt)
    return hashed == hashed_password


def sanitize_filename(filename: str) -> str:
    """
    清理文件名，移除非法字符
    
    Args:
        filename: 原始文件名
        
    Returns:
        str: 清理后的文件名
    """
    # 移除Windows文件名中的非法字符
    illegal_chars = r'[<>:"/\\|?*]'
    sanitized = re.sub(illegal_chars, '_', filename)
    
    # 移除前后空格和点
    sanitized = sanitized.strip(' .')
    
    # 如果文件名为空，使用默认名称
    if not sanitized:
        sanitized = 'untitled'
    
    return sanitized


def truncate_string(text: str, max_length: int, suffix: str = "...") -> str:
    """
    截断字符串
    
    Args:
        text: 原始字符串
        max_length: 最大长度
        suffix: 后缀
        
    Returns:
        str: 截断后的字符串
    """
    if len(text) <= max_length:
        return text
    
    return text[:max_length - len(suffix)] + suffix


def format_file_size(size_bytes: int) -> str:
    """
    格式化文件大小
    
    Args:
        size_bytes: 字节数
        
    Returns:
        str: 格式化后的文件大小
    """
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    size = float(size_bytes)
    
    while size >= 1024.0 and i < len(size_names) - 1:
        size /= 1024.0
        i += 1
    
    return f"{size:.1f} {size_names[i]}"


def format_duration(seconds: int) -> str:
    """
    格式化时长
    
    Args:
        seconds: 秒数
        
    Returns:
        str: 格式化后的时长
    """
    if seconds < 60:
        return f"{seconds}秒"
    elif seconds < 3600:
        minutes = seconds // 60
        remaining_seconds = seconds % 60
        return f"{minutes}分{remaining_seconds}秒"
    else:
        hours = seconds // 3600
        remaining_minutes = (seconds % 3600) // 60
        remaining_seconds = seconds % 60
        return f"{hours}小时{remaining_minutes}分{remaining_seconds}秒"


def safe_cast(value: Any, target_type: type, default: Any = None) -> Any:
    """
    安全类型转换
    
    Args:
        value: 要转换的值
        target_type: 目标类型
        default: 转换失败时的默认值
        
    Returns:
        Any: 转换后的值或默认值
    """
    try:
        return target_type(value)
    except (ValueError, TypeError):
        return default


def is_valid_url(url: str) -> bool:
    """
    验证URL格式
    
    Args:
        url: URL字符串
        
    Returns:
        bool: 是否为有效URL
    """
    if not url:
        return False
    
    pattern = r'^https?://[^\s/$.?#].[^\s]*$'
    return bool(re.match(pattern, url))


def extract_domain(url: str) -> Optional[str]:
    """
    从URL中提取域名
    
    Args:
        url: URL字符串
        
    Returns:
        Optional[str]: 域名或None
    """
    if not is_valid_url(url):
        return None
    
    pattern = r'https?://([^/]+)'
    match = re.match(pattern, url)
    return match.group(1) if match else None


def merge_dicts(*dicts) -> dict:
    """
    合并多个字典
    
    Args:
        *dicts: 要合并的字典
        
    Returns:
        dict: 合并后的字典
    """
    result = {}
    for d in dicts:
        if isinstance(d, dict):
            result.update(d)
    return result


def chunk_list(lst: list, chunk_size: int) -> list:
    """
    将列表分块
    
    Args:
        lst: 原始列表
        chunk_size: 块大小
        
    Returns:
        list: 分块后的列表
    """
    return [lst[i:i + chunk_size] for i in range(0, len(lst), chunk_size)]


def remove_duplicates(lst: list, key_func=None) -> list:
    """
    移除列表中的重复项
    
    Args:
        lst: 原始列表
        key_func: 用于比较的键函数
        
    Returns:
        list: 去重后的列表
    """
    if key_func is None:
        return list(dict.fromkeys(lst))
    
    seen = set()
    result = []
    for item in lst:
        key = key_func(item)
        if key not in seen:
            seen.add(key)
            result.append(item)
    
    return result
