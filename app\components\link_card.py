# coding:utf-8
"""
链接卡片组件模块

该模块定义了链接卡片相关的组件，用于展示可点击的页面导航链接。
包括单个链接卡片和链接卡片视图容器。

主要功能：
- 显示图标、标题和描述的链接卡片
- 支持点击切换到对应页面
- 提供水平滚动的卡片视图容器
- 提供带动画效果的流式布局卡片视图容器
- 自动文本换行和样式应用

组件说明：
- LinkCard: 单个链接卡片组件
- LinkCardView: 链接卡片视图容器（水平滚动）
- FlowLinkCardView: 流式布局链接卡片视图容器（带动画效果）
"""

from PySide6.QtCore import Qt
from PySide6.QtWidgets import QFrame, QLabel, QVBoxLayout, QWidget, QHBoxLayout

from qfluentwidgets import IconWidget, FluentIcon, TextWrap, SingleDirectionScrollArea, FlowLayout
from ..common.style_sheet import StyleSheet
from ..common.signal_bus import signalBus


class LinkCard(QFrame):
    """
    链接卡片组件

    显示图标、标题、描述的卡片组件，
    点击后可以切换到对应的页面。
    """

    def __init__(self, icon, title, content, routeKey, parent=None):
        """
        初始化链接卡片

        Args:
            icon: 卡片图标
            title (str): 卡片标题
            content (str): 卡片描述内容
            routeKey (str): 页面路由键
            parent: 父组件
        """
        super().__init__(parent=parent)
        self.routeKey = routeKey

        self.setFixedSize(194, 220)
        self.iconWidget = IconWidget(icon, self)
        self.titleLabel = QLabel(title, self)
        self.contentLabel = QLabel(TextWrap.wrap(content, 28, False)[0], self)
        self.urlWidget = IconWidget(FluentIcon.LINK, self)

        self.__initWidget()

    def __initWidget(self):
        """
        初始化组件界面

        设置组件布局、样式和鼠标指针。
        """
        self.setCursor(Qt.PointingHandCursor)

        self.iconWidget.setFixedSize(54, 54)
        self.urlWidget.setFixedSize(16, 16)

        self.vBoxLayout = QVBoxLayout(self)
        self.vBoxLayout.setSpacing(0)
        self.vBoxLayout.setContentsMargins(24, 24, 24, 13)
        self.vBoxLayout.addWidget(self.iconWidget)
        self.vBoxLayout.addSpacing(16)
        self.vBoxLayout.addWidget(self.titleLabel)
        self.vBoxLayout.addSpacing(8)
        self.vBoxLayout.addWidget(self.contentLabel)
        self.vBoxLayout.setAlignment(Qt.AlignLeft | Qt.AlignTop)
        self.urlWidget.move(154, 192)

        self.titleLabel.setObjectName('titleLabel')
        self.contentLabel.setObjectName('contentLabel')

    def mouseReleaseEvent(self, e):
        """
        鼠标释放事件处理

        当用户点击卡片时，切换到对应的页面。
        """
        super().mouseReleaseEvent(e)
        signalBus.switchToPageSignal.emit(self.routeKey)




class LinkCardView(QWidget):
    """
    链接卡片视图容器

    提供水平滚动的链接卡片容器，用于展示多个链接卡片。
    支持动态添加卡片和自动布局，支持标题显示。
    """

    def __init__(self, title: str = None, parent=None):
        """
        初始化链接卡片视图

        Args:
            title (str, optional): 视图标题
            parent: 父组件
        """
        super().__init__(parent)
        self.vBoxLayout = QVBoxLayout(self)
        self.scrollArea = SingleDirectionScrollArea(self, Qt.Horizontal)
        self.view = QWidget(self.scrollArea)
        self.hBoxLayout = QHBoxLayout(self.view)

        # 如果有标题，创建标题标签
        if title:
            self.titleLabel = QLabel(title, self)
            self.titleLabel.setObjectName('viewTitleLabel')
            self.vBoxLayout.addWidget(self.titleLabel)
        else:
            self.titleLabel = None

        # 设置布局
        self.vBoxLayout.setContentsMargins(36, 0, 36, 0)
        self.vBoxLayout.setSpacing(10)
        self.hBoxLayout.setContentsMargins(0, 0, 0, 0)
        self.hBoxLayout.setSpacing(12)
        self.hBoxLayout.setAlignment(Qt.AlignLeft)

        # 添加滚动区域到主布局
        self.vBoxLayout.addWidget(self.scrollArea, 1)

        # 设置滚动区域
        self.scrollArea.setWidget(self.view)
        self.scrollArea.setWidgetResizable(True)
        self.scrollArea.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.scrollArea.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)

        self.view.setObjectName('view')
        StyleSheet.LINK_CARD.apply(self)

    def addCard(self, icon, title, content, routeKey):
        """
        添加链接卡片

        Args:
            icon: 卡片图标
            title (str): 卡片标题
            content (str): 卡片描述内容
            routeKey (str): 页面路由键
        """
        card = LinkCard(icon, title, content, routeKey, self.view)
        self.hBoxLayout.addWidget(card, 0, Qt.AlignLeft)


class FlowLinkCardView(QWidget):
    """
    流式布局链接卡片视图容器

    提供带动画效果的流式布局链接卡片容器，用于展示多个链接卡片。
    支持动态添加卡片、自动布局、响应式设计和标题显示。
    """

    def __init__(self, title: str = None, parent=None):
        """
        初始化流式布局链接卡片视图

        Args:
            title (str, optional): 视图标题
            parent: 父组件
        """
        super().__init__(parent)
        self.vBoxLayout = QVBoxLayout(self)
        self.flowLayout = FlowLayout()

        # 如果有标题，创建标题标签
        if title:
            self.titleLabel = QLabel(title, self)
            self.titleLabel.setObjectName('viewTitleLabel')
            self.vBoxLayout.addWidget(self.titleLabel)
        else:
            self.titleLabel = None

        # 设置布局，参考SampleCardView的设置
        self.vBoxLayout.setContentsMargins(36, 0, 36, 0)
        self.vBoxLayout.setSpacing(10)
        self.flowLayout.setContentsMargins(0, 0, 0, 0)
        self.flowLayout.setHorizontalSpacing(12)
        self.flowLayout.setVerticalSpacing(12)

        # 添加流式布局到主布局
        self.vBoxLayout.addLayout(self.flowLayout, 1)

        # 应用样式，与LinkCardView保持一致
        self.setObjectName('flowLinkCardView')
        StyleSheet.LINK_CARD.apply(self)

    def addCard(self, icon, title, content, routeKey):
        """
        添加链接卡片到流式布局

        Args:
            icon: 卡片图标
            title (str): 卡片标题
            content (str): 卡片描述内容
            routeKey (str): 页面路由键

        Returns:
            LinkCard: 创建的链接卡片
        """
        card = LinkCard(icon, title, content, routeKey, self)
        self.flowLayout.addWidget(card)
        return card

    def addNavigationCard(self, icon, title, content, routeKey):
        """
        添加导航链接卡片到流式布局

        Args:
            icon: 卡片图标
            title (str): 卡片标题
            content (str): 卡片描述内容
            routeKey (str): 页面路由键

        Returns:
            LinkCard: 创建的导航链接卡片
        """
        card = LinkCard(icon, title, content, routeKey, self)
        self.flowLayout.addWidget(card)
        return card




