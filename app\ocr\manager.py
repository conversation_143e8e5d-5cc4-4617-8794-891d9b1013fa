# coding:utf-8
"""
OCR管理器

该模块提供OCR管理器类 OCRManager，
负责管理多个OCR引擎，提供统一的OCR识别接口。

主要功能：
- 管理多个OCR引擎
- 自动选择可用的引擎
- 提供备用引擎机制
- 统一的识别接口

类说明：
- OCRManager: OCR管理器类
"""

import time
from typing import Union, Optional, List, Dict, Any
from pathlib import Path

from .engines import BaseOCREngine, DdddOCREngine, BaiduOCREngine, OCRResult
from ..xueyuan.common.config_loader import get_study_config


class OCRManager:
    """
    OCR管理器类
    
    管理多个OCR引擎，提供统一的OCR识别接口。
    支持主引擎和备用引擎的自动切换机制。
    """
    
    def __init__(self):
        """初始化OCR管理器"""
        self.engines: Dict[str, BaseOCREngine] = {}
        self.primary_engine_name = ""
        self.fallback_engine_name = ""
        self.is_initialized = False
        
        # 统计信息
        self.stats = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "engine_usage": {},
            "average_processing_time": 0.0
        }
    
    def initialize(self) -> bool:
        """
        初始化OCR管理器
        
        Returns:
            bool: 初始化是否成功
        """
        try:
            print("[OCR] 开始初始化OCR管理器...")
            
            # 获取配置
            cfg = get_study_config()
            primary_engine = cfg.get(cfg.primaryOCREngine)
            fallback_engine = cfg.get(cfg.fallbackOCREngine)
            
            # 初始化Ddddocr引擎
            dddd_engine = DdddOCREngine()
            if dddd_engine.initialize():
                self.engines["ddddocr"] = dddd_engine
                print("[OCR] Ddddocr引擎初始化成功")
            else:
                print("[OCR] Ddddocr引擎初始化失败")
            
            # 初始化百度OCR引擎
            baidu_app_id = cfg.get(cfg.baiduAppId)
            baidu_api_key = cfg.get(cfg.baiduApiKey)
            baidu_secret_key = cfg.get(cfg.baiduSecretKey)
            
            if baidu_api_key and baidu_secret_key:
                baidu_engine = BaiduOCREngine(
                    app_id=baidu_app_id,
                    api_key=baidu_api_key,
                    secret_key=baidu_secret_key
                )
                if baidu_engine.initialize():
                    self.engines["baidu"] = baidu_engine
                    print("[OCR] 百度OCR引擎初始化成功")
                else:
                    print("[OCR] 百度OCR引擎初始化失败")
            else:
                print("[OCR] 百度OCR配置不完整，跳过初始化")
            
            # 设置主引擎和备用引擎
            self.primary_engine_name = primary_engine if primary_engine in self.engines else ""
            self.fallback_engine_name = fallback_engine if fallback_engine in self.engines else ""
            
            # 如果没有配置主引擎，自动选择第一个可用引擎
            if not self.primary_engine_name and self.engines:
                self.primary_engine_name = list(self.engines.keys())[0]
            
            # 如果没有配置备用引擎，选择除主引擎外的第一个引擎
            if not self.fallback_engine_name and len(self.engines) > 1:
                for engine_name in self.engines:
                    if engine_name != self.primary_engine_name:
                        self.fallback_engine_name = engine_name
                        break
            
            # 初始化统计信息
            for engine_name in self.engines:
                self.stats["engine_usage"][engine_name] = 0
            
            self.is_initialized = len(self.engines) > 0
            
            if self.is_initialized:
                print(f"[OCR] OCR管理器初始化成功，可用引擎: {list(self.engines.keys())}")
                print(f"[OCR] 主引擎: {self.primary_engine_name}, 备用引擎: {self.fallback_engine_name}")
            else:
                print("[OCR] OCR管理器初始化失败，没有可用的引擎")
            
            return self.is_initialized
            
        except Exception as e:
            print(f"[OCR] OCR管理器初始化失败: {e}")
            return False
    
    def recognize_text(self, image_data: Union[bytes, str, Path], 
                      engine_name: Optional[str] = None) -> OCRResult:
        """
        识别图片中的文字
        
        Args:
            image_data: 图片数据
            engine_name: 指定使用的引擎名称，如果为None则使用主引擎
            
        Returns:
            OCRResult: 识别结果
        """
        if not self.is_initialized:
            return self._create_error_result("OCR管理器未初始化")
        
        self.stats["total_requests"] += 1
        start_time = time.time()
        
        # 确定使用的引擎
        target_engine_name = engine_name or self.primary_engine_name
        
        # 尝试使用指定引擎
        if target_engine_name in self.engines:
            result = self._recognize_with_engine(image_data, target_engine_name)
            if result.success:
                self._update_stats(target_engine_name, True, time.time() - start_time)
                return result
        
        # 如果主引擎失败，尝试备用引擎
        if (not engine_name and  # 只有在没有指定引擎时才使用备用引擎
            self.fallback_engine_name and 
            self.fallback_engine_name != target_engine_name and
            self.fallback_engine_name in self.engines):
            
            print(f"[OCR] 主引擎失败，尝试备用引擎: {self.fallback_engine_name}")
            result = self._recognize_with_engine(image_data, self.fallback_engine_name)
            if result.success:
                self._update_stats(self.fallback_engine_name, True, time.time() - start_time)
                return result
        
        # 所有引擎都失败
        self._update_stats(target_engine_name, False, time.time() - start_time)
        return self._create_error_result("所有OCR引擎都无法识别图片")
    
    def _recognize_with_engine(self, image_data: Union[bytes, str, Path], 
                              engine_name: str) -> OCRResult:
        """
        使用指定引擎识别文字
        
        Args:
            image_data: 图片数据
            engine_name: 引擎名称
            
        Returns:
            OCRResult: 识别结果
        """
        try:
            engine = self.engines[engine_name]
            result = engine.recognize_text(image_data)
            
            if result.success:
                print(f"[OCR] 使用 {engine_name} 引擎识别成功: {result.text[:50]}...")
            else:
                print(f"[OCR] 使用 {engine_name} 引擎识别失败: {result.error_message}")
            
            return result
            
        except Exception as e:
            print(f"[OCR] 引擎 {engine_name} 执行异常: {e}")
            return self._create_error_result(f"引擎执行异常: {e}")
    
    def get_available_engines(self) -> List[str]:
        """
        获取可用的引擎列表
        
        Returns:
            List[str]: 可用引擎名称列表
        """
        return list(self.engines.keys())
    
    def get_engine_info(self, engine_name: str) -> Optional[Dict[str, Any]]:
        """
        获取引擎信息
        
        Args:
            engine_name: 引擎名称
            
        Returns:
            Optional[Dict[str, Any]]: 引擎信息，如果引擎不存在则返回None
        """
        if engine_name in self.engines:
            return self.engines[engine_name].get_engine_info()
        return None
    
    def get_all_engines_info(self) -> Dict[str, Dict[str, Any]]:
        """
        获取所有引擎信息
        
        Returns:
            Dict[str, Dict[str, Any]]: 所有引擎的信息
        """
        return {name: engine.get_engine_info() for name, engine in self.engines.items()}
    
    def set_primary_engine(self, engine_name: str) -> bool:
        """
        设置主引擎
        
        Args:
            engine_name: 引擎名称
            
        Returns:
            bool: 设置是否成功
        """
        if engine_name in self.engines:
            self.primary_engine_name = engine_name
            print(f"[OCR] 主引擎已设置为: {engine_name}")
            return True
        else:
            print(f"[OCR] 引擎 {engine_name} 不存在")
            return False
    
    def set_fallback_engine(self, engine_name: str) -> bool:
        """
        设置备用引擎
        
        Args:
            engine_name: 引擎名称
            
        Returns:
            bool: 设置是否成功
        """
        if engine_name in self.engines:
            self.fallback_engine_name = engine_name
            print(f"[OCR] 备用引擎已设置为: {engine_name}")
            return True
        else:
            print(f"[OCR] 引擎 {engine_name} 不存在")
            return False
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        stats = self.stats.copy()
        if stats["total_requests"] > 0:
            stats["success_rate"] = stats["successful_requests"] / stats["total_requests"]
        else:
            stats["success_rate"] = 0.0
        
        return stats
    
    def reset_stats(self):
        """重置统计信息"""
        self.stats = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "engine_usage": {name: 0 for name in self.engines},
            "average_processing_time": 0.0
        }
        print("[OCR] 统计信息已重置")
    
    def cleanup(self):
        """清理所有引擎资源"""
        for engine in self.engines.values():
            engine.cleanup()
        
        self.engines.clear()
        self.is_initialized = False
        print("[OCR] OCR管理器资源已清理")
    
    def _update_stats(self, engine_name: str, success: bool, processing_time: float):
        """
        更新统计信息
        
        Args:
            engine_name: 引擎名称
            success: 是否成功
            processing_time: 处理时间
        """
        if success:
            self.stats["successful_requests"] += 1
        else:
            self.stats["failed_requests"] += 1
        
        if engine_name in self.stats["engine_usage"]:
            self.stats["engine_usage"][engine_name] += 1
        
        # 更新平均处理时间
        total_time = self.stats["average_processing_time"] * (self.stats["total_requests"] - 1)
        self.stats["average_processing_time"] = (total_time + processing_time) / self.stats["total_requests"]
    
    def _create_error_result(self, error_message: str) -> OCRResult:
        """
        创建错误结果
        
        Args:
            error_message: 错误信息
            
        Returns:
            OCRResult: 错误结果
        """
        return OCRResult(
            success=False,
            text="",
            confidence=0.0,
            error_message=error_message,
            engine_name="manager",
            processing_time=0.0
        )


# 全局OCR管理器实例
ocr_manager = OCRManager()
