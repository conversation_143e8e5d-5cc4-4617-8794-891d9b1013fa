# coding:utf-8
"""
API捕获模块

该模块提供API捕获功能，基于Playwright浏览器自动化技术，
捕获指定的API请求和响应数据。

主要组件：
- capture: API捕获核心类
- manager: API捕获管理器
- handlers: API响应处理器

使用示例：
    from app.xueyuan.api import APICaptureManager
    
    manager = APICaptureManager()
    await manager.start_capture()
"""

from .capture import APICapture
from .handlers import (
    APIResponseHandler,
    OnlineDataHandler,
    StudentArchivesHandler,
    APIHandlerFactory
)
from .manager import APICaptureManager, api_capture_manager

__all__ = [
    'APICapture',
    'APIResponseHandler',
    'OnlineDataHandler',
    'StudentArchivesHandler',
    'APIHandlerFactory',
    'APICaptureManager',
    'api_capture_manager'
]

__all__ = ['APICapture', 'APICaptureManager', 'APIResponseHandler']
