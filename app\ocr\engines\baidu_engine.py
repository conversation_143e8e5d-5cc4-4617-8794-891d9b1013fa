# coding:utf-8
"""
百度OCR引擎实现

该模块实现了基于百度OCR API的OCR引擎。
百度OCR提供高精度的文字识别服务，支持多种识别类型。

主要功能：
- 通用文字识别
- 高精度文字识别
- 手写文字识别
- 身份证识别等

类说明：
- BaiduOCREngine: 百度OCR引擎实现类
"""

import time
import base64
import json
from typing import Union, Optional, Dict, Any, List
from pathlib import Path

from .base import BaseOCREngine, OCRResult


class BaiduOCREngine(BaseOCREngine):
    """
    百度OCR引擎实现类
    
    基于百度OCR API实现的OCR引擎，提供高精度的文字识别服务。
    需要API密钥才能使用。
    """
    
    def __init__(self, app_id: str = "", api_key: str = "", secret_key: str = "", **kwargs):
        """
        初始化百度OCR引擎
        
        Args:
            app_id: 百度OCR应用ID
            api_key: 百度OCR API Key
            secret_key: 百度OCR Secret Key
            **kwargs: 其他配置参数
                - ocr_type: OCR类型 (general_basic, accurate_basic, handwriting等)
                - timeout: 请求超时时间 (默认10秒)
        """
        super().__init__("baidu_ocr", **kwargs)
        
        # API配置
        self.app_id = app_id
        self.api_key = api_key
        self.secret_key = secret_key
        
        # 其他配置
        self.ocr_type = kwargs.get('ocr_type', 'general_basic')
        self.timeout = kwargs.get('timeout', 10)
        
        # 访问令牌
        self.access_token = ""
        self.token_expires_at = 0
        
        # API客户端
        self.client = None
    
    def initialize(self) -> bool:
        """
        初始化OCR引擎
        
        Returns:
            bool: 初始化是否成功
        """
        try:
            # 检查API配置
            if not all([self.api_key, self.secret_key]):
                self.error_message = "缺少API密钥配置"
                print(f"[OCR] {self.error_message}")
                return False
            
            # 尝试导入百度OCR SDK
            from aip import AipOcr
            
            # 创建客户端
            self.client = AipOcr(self.app_id, self.api_key, self.secret_key)
            
            # 设置连接超时
            self.client.setConnectionTimeoutInMillis(self.timeout * 1000)
            self.client.setSocketTimeoutInMillis(self.timeout * 1000)
            
            # 测试连接
            if self._test_connection():
                self.is_initialized = True
                self.error_message = ""
                print(f"[OCR] {self.name} 引擎初始化成功")
                return True
            else:
                return False
                
        except ImportError as e:
            self.error_message = f"百度OCR SDK未安装: {e}"
            print(f"[OCR] {self.error_message}")
            return False
        except Exception as e:
            self.error_message = f"初始化失败: {e}"
            print(f"[OCR] {self.error_message}")
            return False
    
    def _test_connection(self) -> bool:
        """
        测试API连接
        
        Returns:
            bool: 连接是否成功
        """
        try:
            # 创建一个1x1像素的测试图片
            test_image = base64.b64encode(b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x02\x00\x00\x00\x90wS\xde\x00\x00\x00\tpHYs\x00\x00\x0b\x13\x00\x00\x0b\x13\x01\x00\x9a\x9c\x18\x00\x00\x00\nIDATx\x9cc\xf8\x00\x00\x00\x01\x00\x01\x00\x00\x00\x00IEND\xaeB`\x82').decode()
            
            # 调用API测试
            result = self.client.basicGeneral(test_image)
            
            # 检查结果
            if 'error_code' in result:
                self.error_message = f"API测试失败: {result.get('error_msg', '未知错误')}"
                print(f"[OCR] {self.error_message}")
                return False
            
            return True
            
        except Exception as e:
            self.error_message = f"连接测试失败: {e}"
            print(f"[OCR] {self.error_message}")
            return False
    
    def recognize_text(self, image_data: Union[bytes, str, Path]) -> OCRResult:
        """
        识别图片中的文字
        
        Args:
            image_data: 图片数据
            
        Returns:
            OCRResult: 识别结果
        """
        if not self.is_initialized:
            return self._create_error_result("引擎未初始化")
        
        if not self.client:
            return self._create_error_result("API客户端不可用")
        
        try:
            start_time = time.time()
            
            # 加载图片数据并转换为base64
            image_bytes = self._load_image_data(image_data)
            image_base64 = base64.b64encode(image_bytes).decode()
            
            # 根据OCR类型调用相应的API
            result = self._call_ocr_api(image_base64)
            
            processing_time = time.time() - start_time
            
            # 处理API响应
            return self._process_api_response(result, processing_time)
            
        except FileNotFoundError as e:
            return self._create_error_result(f"图片文件不存在: {e}")
        except Exception as e:
            return self._create_error_result(f"识别失败: {e}")
    
    def _call_ocr_api(self, image_base64: str) -> Dict[str, Any]:
        """
        调用OCR API
        
        Args:
            image_base64: base64编码的图片数据
            
        Returns:
            Dict[str, Any]: API响应结果
        """
        options = {
            'detect_direction': 'true',
            'probability': 'true'
        }
        
        if self.ocr_type == 'general_basic':
            return self.client.basicGeneral(image_base64, options)
        elif self.ocr_type == 'accurate_basic':
            return self.client.basicAccurate(image_base64, options)
        elif self.ocr_type == 'handwriting':
            return self.client.handwriting(image_base64, options)
        else:
            # 默认使用通用文字识别
            return self.client.basicGeneral(image_base64, options)
    
    def _process_api_response(self, result: Dict[str, Any], processing_time: float) -> OCRResult:
        """
        处理API响应
        
        Args:
            result: API响应结果
            processing_time: 处理时间
            
        Returns:
            OCRResult: 处理后的识别结果
        """
        # 检查错误
        if 'error_code' in result:
            error_msg = result.get('error_msg', '未知错误')
            return self._create_error_result(f"API错误: {error_msg}")
        
        # 提取文字内容
        words_result = result.get('words_result', [])
        if not words_result:
            return self._create_success_result(
                text="",
                confidence=0.0,
                processing_time=processing_time,
                raw_result=result
            )
        
        # 合并所有文字
        text_lines = []
        total_confidence = 0.0
        positions = []
        
        for word_info in words_result:
            word_text = word_info.get('words', '')
            if word_text:
                text_lines.append(word_text)
                
                # 提取置信度
                probability = word_info.get('probability', {})
                if probability:
                    avg_prob = sum(probability.values()) / len(probability)
                    total_confidence += avg_prob
                
                # 提取位置信息
                location = word_info.get('location', {})
                if location:
                    positions.append({
                        'text': word_text,
                        'left': location.get('left', 0),
                        'top': location.get('top', 0),
                        'width': location.get('width', 0),
                        'height': location.get('height', 0)
                    })
        
        # 计算平均置信度
        avg_confidence = total_confidence / len(words_result) if words_result else 0.0
        
        # 合并文字
        final_text = '\n'.join(text_lines)
        
        return self._create_success_result(
            text=final_text,
            confidence=avg_confidence / 100.0,  # 转换为0-1范围
            processing_time=processing_time,
            raw_result=result,
            positions=positions if positions else None
        )
    
    def is_available(self) -> bool:
        """
        检查引擎是否可用
        
        Returns:
            bool: 引擎是否可用
        """
        try:
            from aip import AipOcr
            return bool(self.api_key and self.secret_key)
        except ImportError:
            return False
    
    def cleanup(self):
        """清理资源"""
        self.client = None
        self.access_token = ""
        self.token_expires_at = 0
        self.is_initialized = False
        print(f"[OCR] {self.name} 引擎资源已清理")
    
    def get_engine_info(self) -> Dict[str, Any]:
        """
        获取引擎信息
        
        Returns:
            Dict[str, Any]: 引擎信息
        """
        return {
            "name": self.name,
            "version": "unknown",
            "initialized": self.is_initialized,
            "ocr_type": self.ocr_type,
            "timeout": self.timeout,
            "features": ["text_recognition", "position_detection", "confidence_score"],
            "requires_api_key": True,
            "offline_capable": False,
            "api_configured": bool(self.api_key and self.secret_key)
        }
    
    def set_ocr_type(self, ocr_type: str):
        """
        设置OCR类型
        
        Args:
            ocr_type: OCR类型 (general_basic, accurate_basic, handwriting等)
        """
        self.ocr_type = ocr_type
        print(f"[OCR] 百度OCR类型已设置为: {ocr_type}")
    
    def get_supported_types(self) -> List[str]:
        """
        获取支持的OCR类型
        
        Returns:
            List[str]: 支持的OCR类型列表
        """
        return [
            'general_basic',      # 通用文字识别（标准版）
            'accurate_basic',     # 通用文字识别（高精度版）
            'handwriting',        # 手写文字识别
            'idcard',            # 身份证识别
            'bankcard',          # 银行卡识别
            'driving_license',   # 驾驶证识别
            'vehicle_license'    # 行驶证识别
        ]
