# coding:utf-8
"""
数据库单元测试

测试数据库管理器、DAO类和数据模型的功能。

测试内容：
- 数据库连接和初始化
- 用户CRUD操作
- 课程CRUD操作
- 日志CRUD操作
- API数据CRUD操作
- 事务处理
- 错误处理
"""

import pytest
from datetime import datetime, timedelta
from unittest.mock import patch, MagicMock

from app.xueyuan.database.manager import DatabaseManager
from app.xueyuan.database.dao import UserDAO, CourseDAO, LogDAO, APIDataDAO
from app.xueyuan.database.models import User, Course, Log, APIData
from tests.conftest import create_test_user, create_test_course, create_test_log


class TestDatabaseManager:
    """数据库管理器测试"""
    
    def test_init_database(self, test_db):
        """测试数据库初始化"""
        assert test_db.init_database() is True
        
        # 检查表是否创建
        with test_db.get_connection() as conn:
            cursor = conn.cursor()
            
            # 检查用户表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='users'")
            assert cursor.fetchone() is not None
            
            # 检查课程表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='courses'")
            assert cursor.fetchone() is not None
            
            # 检查日志表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='logs'")
            assert cursor.fetchone() is not None
            
            # 检查API数据表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='api_data'")
            assert cursor.fetchone() is not None
    
    def test_get_connection(self, test_db):
        """测试获取数据库连接"""
        with test_db.get_connection() as conn:
            assert conn is not None
            
            # 测试连接可用性
            cursor = conn.cursor()
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            assert result[0] == 1
    
    def test_execute_query(self, test_db):
        """测试执行查询"""
        # 测试查询
        result = test_db.execute_query("SELECT COUNT(*) FROM users")
        assert isinstance(result, list)
        
        # 测试插入
        test_db.execute_query(
            "INSERT INTO users (phone, password, name, status) VALUES (?, ?, ?, ?)",
            ("13800000000", "password", "测试用户", "active")
        )
        
        # 验证插入
        result = test_db.execute_query("SELECT COUNT(*) FROM users WHERE phone = ?", ("13800000000",))
        assert result[0][0] == 1


class TestUserDAO:
    """用户DAO测试"""
    
    def test_create_user(self, test_db):
        """测试创建用户"""
        user_dao = UserDAO()
        user = create_test_user(phone="13800000001")
        
        result = user_dao.create(user)
        assert result is True
        
        # 验证用户已创建
        created_user = user_dao.get_by_phone("13800000001")
        assert created_user is not None
        assert created_user.phone == "13800000001"
        assert created_user.name == "测试用户"
    
    def test_get_user_by_phone(self, populated_test_db):
        """测试根据手机号获取用户"""
        user_dao = UserDAO()
        
        user = user_dao.get_by_phone("13800000001")
        assert user is not None
        assert user.phone == "13800000001"
        assert user.name == "测试用户1"
        
        # 测试不存在的用户
        user = user_dao.get_by_phone("99999999999")
        assert user is None
    
    def test_update_user(self, populated_test_db):
        """测试更新用户"""
        user_dao = UserDAO()
        
        # 获取用户
        user = user_dao.get_by_phone("13800000001")
        assert user is not None
        
        # 更新用户信息
        user.name = "更新后的用户名"
        user.compulsory_progress = 50
        
        result = user_dao.update(user)
        assert result is True
        
        # 验证更新
        updated_user = user_dao.get_by_phone("13800000001")
        assert updated_user.name == "更新后的用户名"
        assert updated_user.compulsory_progress == 50
    
    def test_delete_user(self, populated_test_db):
        """测试删除用户"""
        user_dao = UserDAO()
        
        # 删除用户
        result = user_dao.delete("13800000001")
        assert result is True
        
        # 验证删除
        user = user_dao.get_by_phone("13800000001")
        assert user is None
    
    def test_get_all_users(self, populated_test_db):
        """测试获取所有用户"""
        user_dao = UserDAO()
        
        users = user_dao.get_all()
        assert len(users) == 3
        
        # 验证用户数据
        phones = [user.phone for user in users]
        assert "13800000001" in phones
        assert "13800000002" in phones
        assert "13800000003" in phones
    
    def test_get_users_by_status(self, populated_test_db):
        """测试根据状态获取用户"""
        user_dao = UserDAO()
        
        # 获取活跃用户
        active_users = user_dao.get_by_status("active")
        assert len(active_users) == 1
        assert active_users[0].phone == "13800000001"
        
        # 获取已完成用户
        completed_users = user_dao.get_by_status("completed")
        assert len(completed_users) == 1
        assert completed_users[0].phone == "13800000003"


class TestCourseDAO:
    """课程DAO测试"""
    
    def test_create_course(self, test_db):
        """测试创建课程"""
        course_dao = CourseDAO()
        course = create_test_course(title="新测试课程")
        
        result = course_dao.create(course)
        assert result is True
        
        # 验证课程已创建
        courses = course_dao.get_all()
        assert len(courses) == 1
        assert courses[0].title == "新测试课程"
    
    def test_get_courses_by_type(self, populated_test_db):
        """测试根据类型获取课程"""
        course_dao = CourseDAO()
        
        # 获取必修课程
        compulsory_courses = course_dao.get_by_type("compulsory")
        assert len(compulsory_courses) == 2
        
        # 获取选修课程
        elective_courses = course_dao.get_by_type("elective")
        assert len(elective_courses) == 1
    
    def test_update_course_status(self, populated_test_db):
        """测试更新课程状态"""
        course_dao = CourseDAO()
        
        courses = course_dao.get_all()
        course = courses[0]
        
        # 更新状态
        course.status = "completed"
        result = course_dao.update(course)
        assert result is True
        
        # 验证更新
        updated_course = course_dao.get_by_id(course.id)
        assert updated_course.status == "completed"


class TestLogDAO:
    """日志DAO测试"""
    
    def test_create_log(self, test_db):
        """测试创建日志"""
        log_dao = LogDAO()
        log_data = create_test_log(message="测试日志消息")
        
        result = log_dao.create(log_data)
        assert result is True
        
        # 验证日志已创建
        logs = log_dao.get_logs({}, limit=10)
        assert len(logs) == 1
        assert logs[0]["message"] == "测试日志消息"
    
    def test_get_logs_with_conditions(self, populated_test_db):
        """测试条件查询日志"""
        log_dao = LogDAO()
        
        # 按级别查询
        error_logs = log_dao.get_logs({"level": "ERROR"}, limit=10)
        assert len(error_logs) == 1
        assert error_logs[0]["level"] == "ERROR"
        
        # 按用户查询
        user_logs = log_dao.get_logs({"user_phone": "13800000001"}, limit=10)
        assert len(user_logs) == 1
        assert user_logs[0]["user_phone"] == "13800000001"
        
        # 按时间范围查询
        start_time = datetime.now() - timedelta(hours=3)
        end_time = datetime.now()
        time_logs = log_dao.get_logs({
            "start_time": start_time,
            "end_time": end_time
        }, limit=10)
        assert len(time_logs) == 3
    
    def test_delete_old_logs(self, populated_test_db):
        """测试删除旧日志"""
        log_dao = LogDAO()
        
        # 删除1小时前的日志
        cutoff_time = datetime.now() - timedelta(hours=1)
        result = log_dao.delete_old_logs(cutoff_time)
        assert result is True
        
        # 验证删除
        remaining_logs = log_dao.get_logs({}, limit=10)
        assert len(remaining_logs) == 1  # 只剩下最新的日志
    
    def test_get_log_statistics(self, populated_test_db):
        """测试获取日志统计"""
        log_dao = LogDAO()
        
        stats = log_dao.get_statistics()
        assert "total" in stats
        assert "by_level" in stats
        assert "by_module" in stats
        
        assert stats["total"] == 3
        assert stats["by_level"]["INFO"] == 1
        assert stats["by_level"]["WARNING"] == 1
        assert stats["by_level"]["ERROR"] == 1


class TestAPIDataDAO:
    """API数据DAO测试"""
    
    def test_create_api_data(self, test_db):
        """测试创建API数据"""
        api_dao = APIDataDAO()
        
        api_data = APIData(
            url="https://api.example.com/test",
            method="POST",
            status_code=200,
            response_data={"success": True, "data": "test"},
            user_phone="13800000001"
        )
        
        result = api_dao.create(api_data)
        assert result is True
        
        # 验证API数据已创建
        all_data = api_dao.get_all()
        assert len(all_data) == 1
        assert all_data[0].url == "https://api.example.com/test"
    
    def test_get_api_data_by_user(self, test_db):
        """测试根据用户获取API数据"""
        api_dao = APIDataDAO()
        
        # 创建测试数据
        api_data1 = APIData(
            url="https://api.example.com/test1",
            method="GET",
            status_code=200,
            response_data={"data": "test1"},
            user_phone="13800000001"
        )
        
        api_data2 = APIData(
            url="https://api.example.com/test2",
            method="POST",
            status_code=201,
            response_data={"data": "test2"},
            user_phone="13800000002"
        )
        
        api_dao.create(api_data1)
        api_dao.create(api_data2)
        
        # 查询特定用户的数据
        user_data = api_dao.get_by_user("13800000001")
        assert len(user_data) == 1
        assert user_data[0].url == "https://api.example.com/test1"
    
    def test_delete_old_api_data(self, test_db):
        """测试删除旧API数据"""
        api_dao = APIDataDAO()
        
        # 创建测试数据
        api_data = APIData(
            url="https://api.example.com/test",
            method="GET",
            status_code=200,
            response_data={"data": "test"},
            user_phone="13800000001"
        )
        api_dao.create(api_data)
        
        # 删除旧数据
        cutoff_time = datetime.now() + timedelta(hours=1)  # 未来时间，应该删除所有数据
        result = api_dao.delete_old_data(cutoff_time)
        assert result is True
        
        # 验证删除
        remaining_data = api_dao.get_all()
        assert len(remaining_data) == 0


@pytest.mark.unit
class TestDatabaseIntegration:
    """数据库集成测试"""
    
    def test_transaction_rollback(self, test_db):
        """测试事务回滚"""
        user_dao = UserDAO()
        
        try:
            with test_db.get_connection() as conn:
                # 开始事务
                conn.execute("BEGIN")
                
                # 插入用户
                user = create_test_user(phone="13800000999")
                user_dao.create(user)
                
                # 模拟错误
                raise Exception("测试错误")
                
        except Exception:
            # 事务应该回滚
            pass
        
        # 验证用户未被创建
        user = user_dao.get_by_phone("13800000999")
        assert user is None
    
    def test_concurrent_access(self, test_db):
        """测试并发访问"""
        import threading
        import time
        
        user_dao = UserDAO()
        results = []
        
        def create_user_thread(phone):
            try:
                user = create_test_user(phone=phone)
                result = user_dao.create(user)
                results.append(result)
            except Exception as e:
                results.append(False)
        
        # 创建多个线程同时创建用户
        threads = []
        for i in range(5):
            phone = f"1380000000{i}"
            thread = threading.Thread(target=create_user_thread, args=(phone,))
            threads.append(thread)
        
        # 启动所有线程
        for thread in threads:
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 验证结果
        assert len(results) == 5
        assert all(results)  # 所有操作都应该成功
        
        # 验证用户数量
        users = user_dao.get_all()
        assert len(users) == 5
