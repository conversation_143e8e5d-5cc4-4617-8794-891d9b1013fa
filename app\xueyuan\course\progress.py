# coding:utf-8
"""
简化的进度跟踪器

该模块提供基本的学习进度跟踪功能。

主要功能：
- 简单的学习进度记录
- 基本的进度查询

类说明：
- ProgressTracker: 简化的进度跟踪器类
"""

from typing import Optional, Dict, Any
from datetime import datetime

from ..database.dao import DAOFactory


class ProgressTracker:
    """
    简化的进度跟踪器类

    提供基本的学习进度跟踪功能
    """

    def __init__(self):
        """初始化进度跟踪器"""
        # 数据库DAO
        self.log_dao = DAOFactory.get_log_dao()

        print("[进度跟踪] 简化进度跟踪器初始化完成")

    async def record_progress(self, user_phone: str, course_id: str, message: str) -> bool:
        """
        记录学习进度 - 简化版本

        Args:
            user_phone: 用户手机号
            course_id: 课程ID
            message: 进度消息

        Returns:
            bool: 是否记录成功
        """
        try:
            from ..database.models import Log
            from ..common.constants import LogLevel

            log = Log(
                level=LogLevel.INFO,
                message=f"[进度] {message}",
                module="course.progress",
                user_phone=user_phone
            )
            self.log_dao.create(log)

            print(f"[进度跟踪] {message}")
            return True

        except Exception as e:
            print(f"[进度跟踪] 记录进度失败: {e}")
            return False

    def get_simple_progress(self, user_phone: str) -> Dict[str, Any]:
        """
        获取简单的进度信息

        Args:
            user_phone: 用户手机号

        Returns:
            Dict[str, Any]: 进度信息
        """
        try:
            return {
                'user_phone': user_phone,
                'status': 'active',
                'last_activity': datetime.now().isoformat(),
                'message': '学习进行中'
            }

        except Exception as e:
            print(f"[进度跟踪] 获取进度信息失败: {e}")
            return {}

    async def get_progress_summary(self, user_phone: str) -> Dict[str, Any]:
        """
        获取进度摘要

        Args:
            user_phone: 用户手机号

        Returns:
            Dict[str, Any]: 进度摘要
        """
        try:
            # 从日志中统计学习进度
            logs = self.log_dao.get_by_user_phone(user_phone)

            # 统计课程相关的日志
            course_logs = []
            for log in logs:
                # 处理Log对象或字典
                if hasattr(log, 'message'):
                    message = log.message
                elif isinstance(log, dict):
                    message = log.get('message', '')
                else:
                    message = str(log)

                if "课程" in message:
                    course_logs.append(log)

            # 简单统计
            total_courses = 0
            completed_courses = 0

            for log in course_logs:
                if hasattr(log, 'message'):
                    message = log.message
                elif isinstance(log, dict):
                    message = log.get('message', '')
                else:
                    message = str(log)

                if "开始学习课程" in message and ':' in message:
                    total_courses += 1
                elif "完成课程" in message and ':' in message:
                    completed_courses += 1

            progress_percentage = (completed_courses / total_courses * 100) if total_courses > 0 else 0.0

            # 获取最近活动时间
            if logs:
                first_log = logs[0]
                if hasattr(first_log, 'created_at'):
                    last_updated = first_log.created_at
                elif isinstance(first_log, dict):
                    last_updated = first_log.get('created_at', datetime.now().isoformat())
                else:
                    last_updated = datetime.now().isoformat()
            else:
                last_updated = datetime.now().isoformat()

            summary = {
                "user_phone": user_phone,
                "total_courses": total_courses,
                "completed_courses": completed_courses,
                "in_progress_courses": total_courses - completed_courses,
                "progress_percentage": round(progress_percentage, 2),
                "last_updated": last_updated
            }

            print(f"[进度跟踪] 进度摘要: 总课程{total_courses}门，已完成{completed_courses}门")
            return summary

        except Exception as e:
            print(f"[进度跟踪] 获取进度摘要失败: {e}")
            return {
                "user_phone": user_phone,
                "total_courses": 0,
                "completed_courses": 0,
                "in_progress_courses": 0,
                "progress_percentage": 0.0,
                "last_updated": datetime.now().isoformat(),
                "error": str(e)
            }


# 全局进度跟踪器实例
progress_tracker = ProgressTracker()
