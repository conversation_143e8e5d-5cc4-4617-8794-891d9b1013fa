# coding:utf-8
"""
视图模块

该模块提供学习工具的主要视图界面，包括主窗口
和各个功能模块的界面。

主要视图：
- MainWindow: 主窗口
- UserManageInterface: 用户管理界面
- StudyControlInterface: 学习控制界面
- ProgressMonitorInterface: 进度监控界面
- LogViewInterface: 日志查看界面
- StudySettingInterface: 学习设置界面

使用示例：
    from app.xueyuan.view import MainWindow, UserManageInterface

    main_window = MainWindow()
    main_window.show()
"""

from .main_window import MainWindow
from .user_manage_interface import UserManageInterface
from .study_control_interface import StudyControlInterface
from .progress_monitor_interface import ProgressMonitorInterface
from .log_view_interface import LogViewInterface
from .study_setting_interface import StudySettingInterface

__all__ = [
    'MainWindow',
    'UserManageInterface',
    'StudyControlInterface',
    'ProgressMonitorInterface',
    'LogViewInterface',
    'StudySettingInterface'
]
