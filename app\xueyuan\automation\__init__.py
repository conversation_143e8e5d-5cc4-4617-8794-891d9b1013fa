# coding:utf-8
"""
自动化学习引擎模块

该模块提供基于Playwright的浏览器自动化功能，
实现自动登录、课程学习、状态检查等功能。

主要组件：
- engine: 学习引擎核心类
- browser: 浏览器管理器
- login: 登录处理器
- study: 学习处理器
- monitor: 状态监控器

使用示例：
    from app.xueyuan.automation import StudyEngine
    
    engine = StudyEngine()
    await engine.start()
"""

from .engine import StudyEngine
from .browser import BrowserManager
from .login import LoginHandler
from .study import StudyHandler
from .monitor import StatusMonitor

__all__ = ['StudyEngine', 'BrowserManager', 'LoginHandler', 'StudyHandler', 'StatusMonitor']
