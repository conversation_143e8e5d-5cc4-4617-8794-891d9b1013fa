# coding:utf-8
"""
任务调度器

该模块提供任务调度功能，整合线程池管理和并发控制，
提供统一的任务执行接口。

主要功能：
- 任务调度和分发
- 优先级队列管理
- 资源分配和控制
- 任务状态监控

类说明：
- TaskScheduler: 任务调度器类
- ScheduledTask: 调度任务类
"""

import asyncio
import threading
import time
from typing import Optional, Dict, Any, List, Callable, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum

from .thread_pool import ThreadPoolManager, TaskStatus, TaskResult
from .concurrency import ConcurrencyController, RateLimitConfig
from .config_loader import get_study_config
from ..database.dao import DAOFactory


class TaskPriority(Enum):
    """任务优先级枚举"""
    LOW = 1
    NORMAL = 5
    HIGH = 10
    URGENT = 20


class TaskType(Enum):
    """任务类型枚举"""
    USER_LOGIN = "user_login"
    COURSE_LEARNING = "course_learning"
    VIDEO_MONITORING = "video_monitoring"
    OCR_RECOGNITION = "ocr_recognition"
    API_CAPTURE = "api_capture"
    DATA_SYNC = "data_sync"
    SYSTEM_MAINTENANCE = "system_maintenance"


@dataclass
class ScheduledTask:
    """调度任务数据类"""
    task_id: str
    task_type: TaskType
    func: Callable
    args: tuple = field(default_factory=tuple)
    kwargs: dict = field(default_factory=dict)
    priority: TaskPriority = TaskPriority.NORMAL
    user_phone: Optional[str] = None
    max_retries: int = 3
    retry_delay: float = 1.0
    timeout: Optional[float] = None
    rate_limit_key: Optional[str] = None
    resource_locks: List[str] = field(default_factory=list)
    callback: Optional[Callable] = None
    scheduled_time: Optional[datetime] = None
    created_at: datetime = field(default_factory=datetime.now)


class TaskScheduler:
    """
    任务调度器类
    
    提供统一的任务调度和执行管理
    """
    
    def __init__(self):
        """初始化任务调度器"""
        # 配置
        cfg = get_study_config()
        self.max_workers = cfg.maxWorkers.value
        self.scheduler_interval = cfg.schedulerInterval.value
        
        # 核心组件
        self.thread_pool = ThreadPoolManager(max_workers=self.max_workers)
        self.concurrency_controller = ConcurrencyController()
        
        # 调度状态
        self.is_running = False
        self.scheduler_task: Optional[asyncio.Task] = None
        
        # 任务队列
        self.pending_tasks: List[ScheduledTask] = []
        self.scheduled_tasks: List[ScheduledTask] = []  # 定时任务
        self.task_lock = asyncio.Lock()
        
        # 任务类型配置
        self.task_configs = self._init_task_configs()
        
        # 统计信息
        self.stats = {
            'total_scheduled': 0,
            'total_executed': 0,
            'total_failed': 0,
            'total_cancelled': 0,
            'start_time': None
        }
        
        # 数据库DAO
        self.log_dao = DAOFactory.get_log_dao()
        
        print("[任务调度] 任务调度器初始化完成")
    
    def _init_task_configs(self) -> Dict[TaskType, Dict[str, Any]]:
        """初始化任务类型配置"""
        return {
            TaskType.USER_LOGIN: {
                'default_timeout': 60.0,
                'rate_limit_key': 'login_attempts',
                'resource_locks': ['user_login'],
                'max_retries': 2
            },
            TaskType.COURSE_LEARNING: {
                'default_timeout': 300.0,
                'rate_limit_key': None,
                'resource_locks': ['course_data'],
                'max_retries': 3
            },
            TaskType.VIDEO_MONITORING: {
                'default_timeout': 120.0,
                'rate_limit_key': None,
                'resource_locks': [],
                'max_retries': 5
            },
            TaskType.OCR_RECOGNITION: {
                'default_timeout': 30.0,
                'rate_limit_key': 'ocr_requests',
                'resource_locks': ['ocr_engine'],
                'max_retries': 2
            },
            TaskType.API_CAPTURE: {
                'default_timeout': 60.0,
                'rate_limit_key': 'api_calls',
                'resource_locks': ['api_capture'],
                'max_retries': 3
            },
            TaskType.DATA_SYNC: {
                'default_timeout': 180.0,
                'rate_limit_key': None,
                'resource_locks': ['database'],
                'max_retries': 2
            },
            TaskType.SYSTEM_MAINTENANCE: {
                'default_timeout': 600.0,
                'rate_limit_key': None,
                'resource_locks': ['system'],
                'max_retries': 1
            }
        }
    
    async def start(self) -> bool:
        """
        启动任务调度器
        
        Returns:
            bool: 是否成功启动
        """
        if self.is_running:
            print("[任务调度] 调度器已在运行中")
            return True
        
        try:
            # 启动线程池
            if not self.thread_pool.start():
                print("[任务调度] 启动线程池失败")
                return False
            
            # 启动调度循环
            self.is_running = True
            self.stats['start_time'] = datetime.now()
            self.scheduler_task = asyncio.create_task(self._scheduler_loop())
            
            print("[任务调度] 任务调度器已启动")
            return True
            
        except Exception as e:
            print(f"[任务调度] 启动调度器失败: {e}")
            return False
    
    async def stop(self, wait: bool = True, timeout: Optional[float] = None):
        """
        停止任务调度器
        
        Args:
            wait: 是否等待任务完成
            timeout: 等待超时时间
        """
        if not self.is_running:
            print("[任务调度] 调度器未在运行中")
            return
        
        try:
            print("[任务调度] 正在停止调度器...")
            
            # 停止调度循环
            self.is_running = False
            
            if self.scheduler_task:
                self.scheduler_task.cancel()
                try:
                    await self.scheduler_task
                except asyncio.CancelledError:
                    pass
            
            # 停止线程池
            self.thread_pool.shutdown(wait=wait, timeout=timeout)
            
            print("[任务调度] 任务调度器已停止")
            
        except Exception as e:
            print(f"[任务调度] 停止调度器失败: {e}")
    
    async def schedule_task(self, task: ScheduledTask) -> bool:
        """
        调度任务
        
        Args:
            task: 调度任务
            
        Returns:
            bool: 是否成功调度
        """
        try:
            # 应用任务类型配置
            self._apply_task_config(task)
            
            async with self.task_lock:
                if task.scheduled_time and task.scheduled_time > datetime.now():
                    # 定时任务
                    self.scheduled_tasks.append(task)
                    self.scheduled_tasks.sort(key=lambda t: t.scheduled_time or datetime.min)
                else:
                    # 立即执行任务
                    self.pending_tasks.append(task)
                    self.pending_tasks.sort(key=lambda t: t.priority.value, reverse=True)
                
                self.stats['total_scheduled'] += 1
            
            print(f"[任务调度] 任务已调度: {task.task_id} ({task.task_type.value})")
            return True
            
        except Exception as e:
            print(f"[任务调度] 调度任务失败: {e}")
            return False
    
    def _apply_task_config(self, task: ScheduledTask):
        """
        应用任务类型配置
        
        Args:
            task: 调度任务
        """
        config = self.task_configs.get(task.task_type, {})
        
        # 设置默认值
        if task.timeout is None:
            task.timeout = config.get('default_timeout')
        
        if task.rate_limit_key is None:
            task.rate_limit_key = config.get('rate_limit_key')
        
        if not task.resource_locks:
            task.resource_locks = config.get('resource_locks', [])
        
        if task.max_retries == 3:  # 默认值
            task.max_retries = config.get('max_retries', 3)
    
    async def _scheduler_loop(self):
        """调度器主循环"""
        try:
            while self.is_running:
                await self._process_scheduled_tasks()
                await self._process_pending_tasks()
                await asyncio.sleep(self.scheduler_interval)
                
        except asyncio.CancelledError:
            print("[任务调度] 调度器循环已取消")
        except Exception as e:
            print(f"[任务调度] 调度器循环异常: {e}")
    
    async def _process_scheduled_tasks(self):
        """处理定时任务"""
        current_time = datetime.now()
        
        async with self.task_lock:
            ready_tasks = []
            remaining_tasks = []
            
            for task in self.scheduled_tasks:
                if task.scheduled_time and task.scheduled_time <= current_time:
                    ready_tasks.append(task)
                else:
                    remaining_tasks.append(task)
            
            # 移动到待执行队列
            for task in ready_tasks:
                self.pending_tasks.append(task)
            
            self.scheduled_tasks = remaining_tasks
            
            if ready_tasks:
                # 重新排序待执行队列
                self.pending_tasks.sort(key=lambda t: t.priority.value, reverse=True)
                print(f"[任务调度] {len(ready_tasks)} 个定时任务已就绪")
    
    async def _process_pending_tasks(self):
        """处理待执行任务"""
        async with self.task_lock:
            if not self.pending_tasks:
                return
            
            # 获取最高优先级任务
            task = self.pending_tasks.pop(0)
        
        # 执行任务
        await self._execute_task(task)
    
    async def _execute_task(self, task: ScheduledTask):
        """
        执行任务
        
        Args:
            task: 调度任务
        """
        try:
            print(f"[任务调度] 开始执行任务: {task.task_id}")
            
            # 检查速率限制
            if task.rate_limit_key:
                if not await self.concurrency_controller.check_rate_limit(task.rate_limit_key):
                    print(f"[任务调度] 任务被速率限制阻止: {task.task_id}")
                    # 重新调度
                    task.scheduled_time = datetime.now() + timedelta(seconds=task.retry_delay)
                    await self.schedule_task(task)
                    return
            
            # 获取并发槽位
            slot_context = None
            if task.user_phone:
                slot_context = self.concurrency_controller.acquire_user_slot(
                    task.user_phone, {'task_id': task.task_id, 'task_type': task.task_type.value}
                )
            else:
                slot_context = self.concurrency_controller.acquire_task_slot(
                    task.task_id, {'task_type': task.task_type.value}
                )
            
            async with slot_context:
                # 获取资源锁
                lock_contexts = []
                for resource_name in task.resource_locks:
                    lock_context = self.concurrency_controller.acquire_resource_lock(
                        resource_name, timeout=task.timeout
                    )
                    lock_contexts.append(lock_context)
                
                # 执行任务
                if lock_contexts:
                    # 有资源锁的情况
                    async with asyncio.gather(*[ctx.__aenter__() for ctx in lock_contexts]):
                        await self._submit_to_thread_pool(task)
                else:
                    # 无资源锁的情况
                    await self._submit_to_thread_pool(task)
            
            self.stats['total_executed'] += 1
            
        except Exception as e:
            print(f"[任务调度] 执行任务失败: {task.task_id} - {e}")
            self.stats['total_failed'] += 1
            
            # 记录错误日志
            await self._log_task_error(task.task_id, e)
    
    async def _submit_to_thread_pool(self, task: ScheduledTask):
        """
        提交任务到线程池
        
        Args:
            task: 调度任务
        """
        # 创建包装函数处理回调
        def task_wrapper():
            try:
                result = task.func(*task.args, **task.kwargs)
                
                # 执行回调
                if task.callback:
                    try:
                        task.callback(TaskResult(
                            task_id=task.task_id,
                            status=TaskStatus.COMPLETED,
                            result=result,
                            end_time=datetime.now()
                        ))
                    except Exception as e:
                        print(f"[任务调度] 回调函数执行失败: {e}")
                
                return result
                
            except Exception as e:
                # 执行失败回调
                if task.callback:
                    try:
                        task.callback(TaskResult(
                            task_id=task.task_id,
                            status=TaskStatus.FAILED,
                            error=e,
                            end_time=datetime.now()
                        ))
                    except Exception as cb_e:
                        print(f"[任务调度] 失败回调函数执行失败: {cb_e}")
                
                raise e
        
        # 提交到线程池
        thread_task_id = self.thread_pool.submit_task(
            task_wrapper,
            task_id=task.task_id,
            priority=task.priority.value,
            max_retries=task.max_retries,
            retry_delay=task.retry_delay,
            timeout=task.timeout
        )
        
        if not thread_task_id:
            raise Exception("提交到线程池失败")
    
    async def cancel_task(self, task_id: str) -> bool:
        """
        取消任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            bool: 是否成功取消
        """
        try:
            async with self.task_lock:
                # 从待执行队列中移除
                self.pending_tasks = [t for t in self.pending_tasks if t.task_id != task_id]
                
                # 从定时任务队列中移除
                self.scheduled_tasks = [t for t in self.scheduled_tasks if t.task_id != task_id]
            
            # 尝试从线程池中取消
            cancelled = self.thread_pool.cancel_task(task_id)
            
            if cancelled:
                self.stats['total_cancelled'] += 1
                print(f"[任务调度] 任务已取消: {task_id}")
            
            return cancelled
            
        except Exception as e:
            print(f"[任务调度] 取消任务失败: {e}")
            return False
    
    def get_task_result(self, task_id: str) -> Optional[TaskResult]:
        """
        获取任务结果
        
        Args:
            task_id: 任务ID
            
        Returns:
            Optional[TaskResult]: 任务结果
        """
        return self.thread_pool.get_task_result(task_id)
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        thread_pool_stats = self.thread_pool.get_stats()
        concurrency_stats = self.concurrency_controller.get_stats()
        
        return {
            'scheduler': {
                'is_running': self.is_running,
                'start_time': self.stats['start_time'],
                'pending_tasks': len(self.pending_tasks),
                'scheduled_tasks': len(self.scheduled_tasks),
                **self.stats
            },
            'thread_pool': thread_pool_stats,
            'concurrency': concurrency_stats
        }
    
    async def _log_task_error(self, task_id: str, error: Exception):
        """
        记录任务错误日志
        
        Args:
            task_id: 任务ID
            error: 错误信息
        """
        try:
            log_data = {
                'user_phone': 'system',
                'level': 'ERROR',
                'message': f"[任务调度] 任务执行失败: {task_id} - {str(error)}",
                'module': 'TaskScheduler',
                'timestamp': datetime.now()
            }
            
            self.log_dao.create(log_data)
            
        except Exception as e:
            print(f"[任务调度] 记录错误日志失败: {e}")


# 全局任务调度器实例
task_scheduler = TaskScheduler()
