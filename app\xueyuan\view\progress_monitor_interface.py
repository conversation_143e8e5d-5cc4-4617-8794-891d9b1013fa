# coding:utf-8
"""
进度监控界面

该模块定义了进度监控界面类 ProgressMonitorInterface，
提供学习进度的实时监控和可视化展示。

主要功能：
- 实时显示学习进度
- 进度图表展示
- 详细进度信息

类说明：
- ProgressMonitorInterface: 进度监控界面类
"""

from PySide6.QtCore import Qt, QTimer
from PySide6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QTableWidgetItem
from qfluentwidgets import (CardWidget, HeaderCardWidget, BodyLabel, CaptionLabel,
                            ProgressBar, TableWidget, FluentIcon as FIF)

from ..database.dao import DAOFactory
from ..common.constants import UserStatus


class ProgressMonitorInterface(QWidget):
    """进度监控界面类"""
    
    def __init__(self, parent=None):
        """初始化进度监控界面"""
        super().__init__(parent)
        self.setObjectName("ProgressMonitorInterface")
        
        # 初始化界面
        self.initWidget()
        self.initLayout()
        
        # 启动定时器更新进度
        self.updateTimer = QTimer()
        self.updateTimer.timeout.connect(self.updateProgress)
        self.updateTimer.start(3000)  # 每3秒更新一次

    def initWidget(self):
        """初始化界面组件"""
        # 创建主布局
        self.vBoxLayout = QVBoxLayout(self)
        
        # 创建总体进度卡片
        self.overallCard = HeaderCardWidget(self)
        self.overallCard.setTitle("总体进度")
        
        # 总体进度内容
        self.overallWidget = QWidget()
        self.overallLayout = QVBoxLayout(self.overallWidget)
        
        # 总体进度条
        self.overallProgressBar = ProgressBar()
        self.overallProgressBar.setValue(0)
        
        # 进度标签
        self.overallLabel = BodyLabel("0 / 0 用户完成学习")
        
        self.overallLayout.addWidget(self.overallProgressBar)
        self.overallLayout.addWidget(self.overallLabel)

        self.overallCard.viewLayout.addWidget(self.overallWidget)
        
        # 创建详细进度表格
        self.detailCard = HeaderCardWidget(self)
        self.detailCard.setTitle("详细进度")
        
        self.progressTable = TableWidget()
        self.progressTable.setColumnCount(6)
        self.progressTable.setHorizontalHeaderLabels([
            "手机号", "姓名", "状态", "学习进度", "总学分", "最后更新"
        ])

        self.detailCard.viewLayout.addWidget(self.progressTable)

    def initLayout(self):
        """初始化布局"""
        self.vBoxLayout.setContentsMargins(0, 0, 0, 0)
        self.vBoxLayout.setSpacing(20)
        
        self.vBoxLayout.addWidget(self.overallCard)
        self.vBoxLayout.addWidget(self.detailCard)

    def updateProgress(self):
        """更新进度信息"""
        try:
            user_dao = DAOFactory.get_user_dao()
            users = user_dao.get_all()
            
            # 计算总体进度
            total_users = len(users)
            completed_users = len([u for u in users if u.status == UserStatus.COMPLETED])
            
            if total_users > 0:
                progress = int((completed_users / total_users) * 100)
                self.overallProgressBar.setValue(progress)
                self.overallLabel.setText(f"{completed_users} / {total_users} 用户完成学习")
            else:
                self.overallProgressBar.setValue(0)
                self.overallLabel.setText("0 / 0 用户完成学习")
            
            # 更新详细进度表格
            self.updateProgressTable(users)
            
        except Exception as e:
            print(f"[界面] 更新进度失败: {e}")

    def updateProgressTable(self, users):
        """更新进度表格"""
        try:
            self.progressTable.setRowCount(len(users))
            
            for i, user in enumerate(users):
                self.progressTable.setItem(i, 0, QTableWidgetItem(user.phone))
                self.progressTable.setItem(i, 1, QTableWidgetItem(user.name or ""))
                self.progressTable.setItem(i, 2, QTableWidgetItem(user.status))
                self.progressTable.setItem(i, 3, QTableWidgetItem(f"{user.progress:.1f}%"))
                self.progressTable.setItem(i, 4, QTableWidgetItem(f"{user.online_total_credit:.1f}"))
                self.progressTable.setItem(i, 5, QTableWidgetItem(user.updated_at.strftime("%Y-%m-%d %H:%M:%S") if user.updated_at else ""))
                
        except Exception as e:
            print(f"[界面] 更新进度表格失败: {e}")
