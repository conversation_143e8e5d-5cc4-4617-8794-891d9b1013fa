# coding:utf-8
"""
OCR识别模块

该模块提供OCR文字识别功能，支持多种OCR引擎：
- ddddocr: 主要OCR引擎，无需API密钥
- baidu: 百度OCR引擎，作为备用引擎

主要组件：
- engines: OCR引擎实现
- manager: OCR管理器
- utils: OCR工具函数

使用示例：
    from app.ocr import OCRManager
    
    ocr_manager = OCRManager()
    result = ocr_manager.recognize_text(image_data)
"""

from .manager import OCRManager
from .engines import DdddOCREngine, BaiduOCREngine

__all__ = ['OCRManager', 'DdddOCREngine', 'BaiduOCREngine']
