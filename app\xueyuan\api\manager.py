# coding:utf-8
"""
API捕获管理器

该模块提供API捕获的管理功能，整合API捕获器和响应处理器，
提供统一的API捕获管理接口。

主要功能：
- 管理API捕获器
- 协调响应处理器
- 提供统一的控制接口
- 状态监控和统计

类说明：
- APICaptureManager: API捕获管理器
"""

import asyncio
from typing import Dict, List, Optional, Any
from playwright.async_api import Page

from .capture import APICapture
from .handlers import APIHandlerFactory, APIResponseHandler
from ..common.config_loader import get_study_config
from ..common.constants import TARGET_APIS


class APICaptureManager:
    """
    API捕获管理器
    
    统一管理API捕获功能，协调捕获器和处理器的工作
    """
    
    def __init__(self):
        """初始化API捕获管理器"""
        self.capture = APICapture()
        self.handlers: Dict[str, APIResponseHandler] = {}
        self.is_running = False
        self.current_user_phone = ""
        
        # 配置
        cfg = get_study_config()
        self.enabled = cfg.get(cfg.apiCaptureEnabled)
        
        # 初始化处理器
        self._init_handlers()
        
        print("[API管理器] 初始化完成")
    
    def _init_handlers(self):
        """初始化API处理器 - 简化版本"""
        print("[API管理器] 简化API处理器初始化完成")
    
    def set_page(self, page: Page):
        """
        设置Playwright页面对象
        
        Args:
            page: Playwright页面对象
        """
        self.capture.set_page(page)
        print("[API管理器] 页面对象已设置")
    
    def set_user_phone(self, phone: str):
        """
        设置当前用户手机号
        
        Args:
            phone: 用户手机号
        """
        self.current_user_phone = phone
        self.capture.set_user_phone(phone)
        print(f"[API管理器] 设置用户手机号: {phone}")
    
    async def start_capture(self) -> bool:
        """
        开始API捕获
        
        Returns:
            bool: 启动是否成功
        """
        if not self.enabled:
            print("[API管理器] API捕获功能已禁用")
            return False
        
        if self.is_running:
            print("[API管理器] API捕获已在运行中")
            return True
        
        try:
            await self.capture.start_capture()
            self.is_running = True
            print("[API管理器] API捕获已启动")
            return True
            
        except Exception as e:
            print(f"[API管理器] 启动API捕获失败: {e}")
            return False
    
    async def stop_capture(self) -> bool:
        """
        停止API捕获
        
        Returns:
            bool: 停止是否成功
        """
        if not self.is_running:
            print("[API管理器] API捕获未在运行中")
            return True
        
        try:
            await self.capture.stop_capture()
            self.is_running = False
            print("[API管理器] API捕获已停止")
            return True
            
        except Exception as e:
            print(f"[API管理器] 停止API捕获失败: {e}")
            return False
    
    async def _handle_api_response(self, data: Dict[str, Any]):
        """
        处理API响应（回调函数）
        
        Args:
            data: API响应数据
        """
        # 这个方法会被多个API调用，需要确定是哪个API
        # 由于回调函数的限制，这里需要从数据中推断API类型
        # 或者修改回调机制来传递更多信息
        
        try:
            # 根据数据结构判断API类型
            api_url = self._identify_api_from_data(data)
            if api_url and api_url in self.handlers:
                handler = self.handlers[api_url]
                success = await handler.handle(data)
                if success:
                    print(f"[API管理器] API响应处理成功: {api_url}")
                else:
                    print(f"[API管理器] API响应处理失败: {api_url}")
            else:
                print(f"[API管理器] 未找到对应的处理器: {api_url}")
                
        except Exception as e:
            print(f"[API管理器] API响应处理异常: {e}")
    
    def _identify_api_from_data(self, data: Dict[str, Any]) -> Optional[str]:
        """
        从响应数据中识别API类型
        
        Args:
            data: 响应数据
            
        Returns:
            Optional[str]: API URL
        """
        if not isinstance(data, dict) or 'data' not in data:
            return None
        
        response_data = data.get('data', {})
        
        # 根据响应数据的特征字段判断API类型
        if 'onlineTotalCredit' in response_data:
            return "https://study.jxgbwlxy.gov.cn/api/report/myData/online"
        elif 'stuName' in response_data and 'workCompany' in response_data:
            return "https://study.jxgbwlxy.gov.cn/api/study/student/studentArchives/student"
        
        return None
    
    def add_target_api(self, url: str, handler_class: Optional[type] = None):
        """
        添加目标API
        
        Args:
            url: API URL
            handler_class: 处理器类（可选）
        """
        self.capture.add_target_api(url)
        
        if handler_class:
            APIHandlerFactory.register_handler(url, handler_class)
            handler = handler_class()
            self.handlers[url] = handler
            self.capture.add_callback(url, self._handle_api_response)
            print(f"[API管理器] 添加目标API和处理器: {url}")
        else:
            print(f"[API管理器] 添加目标API: {url}")
    
    def remove_target_api(self, url: str):
        """
        移除目标API
        
        Args:
            url: API URL
        """
        self.capture.remove_target_api(url)
        if url in self.handlers:
            del self.handlers[url]
        print(f"[API管理器] 移除目标API: {url}")
    
    def get_captured_data(self, url: Optional[str] = None) -> Dict[str, Any]:
        """
        获取捕获的数据
        
        Args:
            url: 指定URL，如果为None则返回所有数据
            
        Returns:
            Dict[str, Any]: 捕获的数据
        """
        return self.capture.get_captured_data(url)
    
    def clear_captured_data(self):
        """清空捕获的数据"""
        self.capture.clear_captured_data()
        print("[API管理器] 捕获数据已清空")
    
    def get_status(self) -> Dict[str, Any]:
        """
        获取管理器状态
        
        Returns:
            Dict[str, Any]: 状态信息
        """
        capture_stats = self.capture.get_capture_stats()
        
        return {
            "enabled": self.enabled,
            "is_running": self.is_running,
            "current_user": self.current_user_phone,
            "handlers_count": len(self.handlers),
            "supported_apis": list(self.handlers.keys()),
            "capture_stats": capture_stats
        }
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        capture_stats = self.capture.get_capture_stats()
        
        return {
            "total_captures": capture_stats.get("capture_count", 0),
            "active_apis": len(capture_stats.get("captured_apis", [])),
            "target_apis": capture_stats.get("target_apis_count", 0),
            "handlers_registered": len(self.handlers),
            "is_capturing": capture_stats.get("is_capturing", False)
        }
    
    async def test_handlers(self) -> Dict[str, bool]:
        """
        测试所有处理器
        
        Returns:
            Dict[str, bool]: 测试结果
        """
        results = {}
        
        for api_url, handler in self.handlers.items():
            try:
                # 使用模拟数据测试处理器
                test_data = self._get_test_data(api_url)
                if test_data:
                    result = await handler.handle(test_data)
                    results[api_url] = result
                    print(f"[API管理器] 处理器测试 {api_url}: {'通过' if result else '失败'}")
                else:
                    results[api_url] = False
                    print(f"[API管理器] 处理器测试 {api_url}: 无测试数据")
                    
            except Exception as e:
                results[api_url] = False
                print(f"[API管理器] 处理器测试 {api_url} 异常: {e}")
        
        return results
    
    def _get_test_data(self, api_url: str) -> Optional[Dict[str, Any]]:
        """
        获取测试数据
        
        Args:
            api_url: API URL
            
        Returns:
            Optional[Dict[str, Any]]: 测试数据
        """
        test_data = {
            "https://study.jxgbwlxy.gov.cn/api/report/myData/online": {
                "msg": "操作成功",
                "code": 0,
                "data": {
                    "examineStatus": "1",
                    "completeStatus": "0",
                    "onlineTotalCredit": "100.0",
                    "compulsoryCredit": 50.0,
                    "electivesCredit": 50.0
                }
            },
            "https://study.jxgbwlxy.gov.cn/api/study/student/studentArchives/student": {
                "msg": "操作成功",
                "code": 0,
                "data": {
                    "id": "123456",
                    "stuName": "测试用户",
                    "stuStatus": "0",
                    "workRank": "3",
                    "workCompany": {
                        "id": "9415",
                        "name": "测试单位"
                    }
                }
            }
        }
        
        return test_data.get(api_url)


# 全局API捕获管理器实例
api_capture_manager = APICaptureManager()
