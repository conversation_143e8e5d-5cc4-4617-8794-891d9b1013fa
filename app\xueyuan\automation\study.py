# coding:utf-8
"""
学习处理器

该模块提供自动学习功能，包括课程浏览、视频播放、
进度跟踪等。

主要功能：
- 课程列表获取
- 自动播放视频
- 学习进度跟踪
- 学习状态监控

类说明：
- StudyHandler: 学习处理器类
"""

import asyncio
import time
from datetime import datetime
from typing import Optional, Dict, Any, List
from playwright.async_api import Page

from ..common.config_loader import get_study_config
from ..database.dao import DAOFactory


class StudyHandler:
    """
    学习处理器类
    
    提供自动学习功能，支持课程浏览和视频播放
    """
    
    def __init__(self, page: Optional[Page] = None):
        """
        初始化学习处理器
        
        Args:
            page: Playwright页面对象
        """
        self.page = page
        self.is_studying = False
        self.current_course = {}
        self.study_progress = {}
        
        # 配置
        cfg = get_study_config()
        self.base_url = cfg.get(cfg.baseUrl)
        self.auto_next = cfg.get(cfg.autoNextCourse)
        
        # 数据库DAO
        self.course_dao = DAOFactory.get_course_dao()
        self.log_dao = DAOFactory.get_log_dao()
        
        # 简化的页面选择器
        self.selectors = {
            'video_player': 'video',
            'play_button': '.play-btn'
        }
        
        print("[学习] 学习处理器初始化完成")
    
    def set_page(self, page: Page):
        """
        设置Playwright页面对象
        
        Args:
            page: Playwright页面对象
        """
        self.page = page
        print("[学习] 页面对象已设置")
    
    async def start_study(self, user_phone: str) -> bool:
        """
        开始自动学习流程

        Args:
            user_phone: 用户手机号

        Returns:
            bool: 启动是否成功
        """
        if not self.page:
            print("[学习] 页面对象未设置")
            return False

        if self.is_studying:
            print("[学习] 学习已在进行中")
            return True

        try:
            print(f"[学习] 开始学习流程 - 用户: {user_phone}")
            self.is_studying = True

            # 首先检查是否已登录，如果没有登录则先登录
            if not await self._check_login_status():
                print("[学习] 用户未登录，需要先登录")
                return False

            # 导航到学习页面
            if not await self._navigate_to_study_page():
                print("[学习] 导航到学习页面失败")
                return False

            # 获取课程列表
            courses = await self._get_course_list()
            if not courses or (not courses.get("必修课") and not courses.get("选修课")):
                print("[学习] 未找到可学习的课程")
                return False

            total_courses = len(courses.get("必修课", [])) + len(courses.get("选修课", []))
            print(f"[学习] 找到 {total_courses} 门课程 (必修: {len(courses.get('必修课', []))}, 选修: {len(courses.get('选修课', []))})")

            # 记录学习开始
            await self._log_study_action(user_phone, f"开始学习，共{total_courses}门课程")

            # 学习必修课程
            compulsory_courses = courses.get("必修课", [])
            for i, course in enumerate(compulsory_courses):
                if not self.is_studying:  # 检查是否被停止
                    break

                if course.get("completed") == "0":  # 只学习未完成的课程
                    print(f"[学习] 开始学习必修课程第{i+1}门: {course.get('name', '未知课程')}")
                    await self._study_course(course, user_phone)

                    # 课程间隔
                    if self.auto_next and i < len(compulsory_courses) - 1:
                        await asyncio.sleep(2)

            # 学习选修课程
            elective_courses = courses.get("选修课", [])
            for i, course in enumerate(elective_courses):
                if not self.is_studying:  # 检查是否被停止
                    break

                if course.get("completed") == "0":  # 只学习未完成的课程
                    print(f"[学习] 开始学习选修课程第{i+1}门: {course.get('name', '未知课程')}")
                    await self._study_course(course, user_phone)

                    # 课程间隔
                    if self.auto_next and i < len(elective_courses) - 1:
                        await asyncio.sleep(2)

            print("[学习] 学习流程完成")
            await self._log_study_action(user_phone, "学习流程完成")
            return True

        except Exception as e:
            print(f"[学习] 学习流程失败: {e}")
            await self._log_study_action(user_phone, f"学习失败: {e}")
            return False
        finally:
            self.is_studying = False
    
    async def stop_study(self):
        """停止自动学习"""
        if not self.is_studying:
            print("[学习] 学习未在进行中")
            return
        
        self.is_studying = False
        print("[学习] 自动学习已停止")
    
    async def _log_study_action(self, user_phone: str, message: str):
        """记录学习操作日志"""
        try:
            from ..database.models import Log
            from ..common.constants import LogLevel

            log = Log(
                level=LogLevel.INFO,
                message=message,
                module="automation.study",
                user_phone=user_phone
            )
            self.log_dao.create(log)
            print(f"[学习] {message}")

        except Exception as e:
            print(f"[学习] 记录日志失败: {e}")

    async def _check_login_status(self) -> bool:
        """检查登录状态"""
        try:
            current_url = self.page.url

            # 如果当前页面是空白页面或者不是学习网站，先导航到登录页面
            if "about:blank" in current_url or "study.jxgbwlxy.gov.cn" not in current_url:
                print("[学习] 导航到登录页面...")
                await self.page.goto("https://study.jxgbwlxy.gov.cn/index")
                await self.page.wait_for_load_state("networkidle")
                await asyncio.sleep(2)
                current_url = self.page.url

            # 检查是否在登录页面
            if "/index" in current_url:
                print("[学习] 当前在登录页面，用户未登录")
                print("[学习] 提示：请先使用登录功能进行登录")
                return False

            # 检查是否在学习相关页面
            if any(path in current_url for path in ["/study/data", "/study/courseMine", "/study/"]):
                print("[学习] 用户已登录")
                return True

            # 尝试导航到学习档案页面验证登录状态
            await self.page.goto("https://study.jxgbwlxy.gov.cn/study/data")
            await self.page.wait_for_load_state("networkidle")
            await asyncio.sleep(2)

            final_url = self.page.url
            if "/study/data" in final_url:
                print("[学习] 登录状态验证成功")
                return True
            else:
                print("[学习] 登录状态验证失败，可能需要重新登录")
                return False

        except Exception as e:
            print(f"[学习] 检查登录状态失败: {e}")
            return False

    async def _navigate_to_study_page(self) -> bool:
        """
        导航到学习页面

        Returns:
            bool: 是否成功
        """
        try:
            # 导航到"我的课程"页面
            study_url = f"{self.base_url}/study/courseMine?id=0"
            await self.page.goto(study_url)
            await self.page.wait_for_load_state("networkidle")

            # 等待页面加载完成
            await asyncio.sleep(2)

            print("[学习] 成功导航到我的课程页面")
            return True

        except Exception as e:
            print(f"[学习] 导航到学习页面失败: {e}")
            return False

    async def _get_course_list(self) -> dict:
        """
        获取课程列表（按照开发要求文档实现）

        Returns:
            dict: 课程字典，包含必修课和选修课
        """
        try:
            courses = {"必修课": [], "选修课": []}

            # 1. 处理必修课
            print("[学习] 开始处理必修课...")
            await self._process_compulsory_courses(courses)

            # 2. 处理选修课
            print("[学习] 开始处理选修课...")
            await self._process_elective_courses(courses)

            print(f"[学习] 课程获取完成 - 必修课: {len(courses['必修课'])}门, 选修课: {len(courses['选修课'])}门")
            return courses

        except Exception as e:
            print(f"[学习] 获取课程列表失败: {e}")
            return {"必修课": [], "选修课": []}

    async def _process_compulsory_courses(self, courses: dict):
        """处理必修课程"""
        try:
            # 点击"我的必修课"选项卡
            compulsory_tab = await self.page.wait_for_selector('text="我的必修课"', timeout=10000)
            await compulsory_tab.click()
            await asyncio.sleep(2)

            # 监听API请求获取课程数据
            api_data = await self._capture_course_api("https://study.jxgbwlxy.gov.cn/api/study/years/yearsCourseware/annualPortalCourseListNew")

            if api_data and api_data.get('code') == 0:
                records = api_data.get('data', {}).get('records', [])
                for record in records:
                    course_info = {
                        "name": record.get('name', ''),
                        "completed": record.get('completed', '0'),
                        "id": record.get('id', ''),
                        "credit": record.get('credit', 0),
                        "percentage": record.get('percentage', '0'),
                        "coursewareId": record.get('courseware', {}).get('id', ''),
                        "videoUrl": f"https://study.jxgbwlxy.gov.cn/video?id={record.get('courseware', {}).get('id', '')}",
                        "completed_date": ""
                    }
                    courses["必修课"].append(course_info)

                # 处理分页
                data = api_data.get('data', {})
                current_page = data.get('current', 1)
                total_pages = data.get('pages', 1)

                # 如果有更多页面，继续获取
                while current_page < total_pages:
                    next_btn = await self.page.query_selector('.btn-next')
                    if next_btn:
                        await next_btn.click()
                        await asyncio.sleep(2)

                        # 获取下一页数据
                        next_api_data = await self._capture_course_api("https://study.jxgbwlxy.gov.cn/api/study/years/yearsCourseware/annualPortalCourseListNew")
                        if next_api_data and next_api_data.get('code') == 0:
                            next_records = next_api_data.get('data', {}).get('records', [])
                            for record in next_records:
                                course_info = {
                                    "name": record.get('name', ''),
                                    "completed": record.get('completed', '0'),
                                    "id": record.get('id', ''),
                                    "credit": record.get('credit', 0),
                                    "percentage": record.get('percentage', '0'),
                                    "coursewareId": record.get('courseware', {}).get('id', ''),
                                    "videoUrl": f"https://study.jxgbwlxy.gov.cn/video?id={record.get('courseware', {}).get('id', '')}",
                                    "completed_date": ""
                                }
                                courses["必修课"].append(course_info)
                            current_page += 1
                        else:
                            break
                    else:
                        break

        except Exception as e:
            print(f"[学习] 处理必修课失败: {e}")

    async def _process_elective_courses(self, courses: dict):
        """处理选修课程"""
        try:
            # 点击"我的选修课"选项卡
            elective_tab = await self.page.wait_for_selector('text="我的选修课"', timeout=10000)
            await elective_tab.click()
            await asyncio.sleep(2)

            # 监听API请求获取课程数据
            api_data = await self._capture_course_api("https://study.jxgbwlxy.gov.cn/api/study/my/elective/myElectivesNew")

            if api_data and api_data.get('code') == 0:
                records = api_data.get('data', {}).get('records', [])

                # 如果选修课为空，需要添加选修课
                if not records:
                    print("[学习] 选修课列表为空，开始添加选修课...")
                    await self._add_elective_courses()
                    # 重新获取选修课数据
                    api_data = await self._capture_course_api("https://study.jxgbwlxy.gov.cn/api/study/my/elective/myElectivesNew")
                    if api_data and api_data.get('code') == 0:
                        records = api_data.get('data', {}).get('records', [])

                for record in records:
                    course_info = {
                        "name": record.get('name', ''),
                        "completed": record.get('completed', '0'),
                        "id": record.get('id', ''),
                        "credit": record.get('credit', 0),
                        "percentage": record.get('percentage', '0'),
                        "coursewareId": record.get('courseware', {}).get('id', ''),
                        "videoUrl": f"https://study.jxgbwlxy.gov.cn/video?id={record.get('courseware', {}).get('id', '')}",
                        "completed_date": ""
                    }
                    courses["选修课"].append(course_info)

        except Exception as e:
            print(f"[学习] 处理选修课失败: {e}")

    async def _capture_course_api(self, target_url: str) -> dict:
        """捕获课程API数据"""
        try:
            # 这里应该实现API监听逻辑
            # 暂时返回空数据，需要集成API捕获功能
            print(f"[API] 尝试捕获API: {target_url}")
            await asyncio.sleep(1)
            return {}
        except Exception as e:
            print(f"[API] 捕获API失败: {e}")
            return {}

    async def _add_elective_courses(self):
        """添加选修课程"""
        try:
            # 跳转到课程分类页面
            await self.page.goto("https://study.jxgbwlxy.gov.cn/study/course-view")
            await self.page.wait_for_load_state("networkidle")
            await asyncio.sleep(2)

            # 检查页面模式并切换到表格模式
            grid_btn = await self.page.query_selector('.el-button--danger.el-button--small.is-plain .el-icon-s-grid')
            if grid_btn:
                # 当前是图片模式，切换到表格模式
                switch_btn = await self.page.query_selector('.el-button--danger.el-button--small.is-plain')
                if switch_btn:
                    await switch_btn.click()
                    await asyncio.sleep(2)

            # 根据设置的选课数量添加课程
            from ..config.config import cfg
            elective_count = cfg.get(cfg.electiveCourses)

            added_count = 0
            page_num = 1

            while added_count < elective_count:
                # 查找添加课程按钮
                add_buttons = await self.page.query_selector_all('.el-icon-circle-plus-outline')

                for button in add_buttons:
                    if added_count >= elective_count:
                        break

                    try:
                        await button.click()
                        await asyncio.sleep(1)
                        added_count += 1
                        print(f"[学习] 已添加第 {added_count} 门选修课")
                    except Exception as e:
                        print(f"[学习] 添加选修课失败: {e}")

                # 如果还需要更多课程，翻页
                if added_count < elective_count:
                    next_btn = await self.page.query_selector('.btn-next')
                    if next_btn:
                        await next_btn.click()
                        await asyncio.sleep(2)
                        page_num += 1
                    else:
                        print("[学习] 没有更多页面，停止添加选修课")
                        break

            print(f"[学习] 选修课添加完成，共添加 {added_count} 门课程")

            # 返回到我的课程页面
            await self.page.goto("https://study.jxgbwlxy.gov.cn/study/courseMine?id=0")
            await self.page.wait_for_load_state("networkidle")
            await asyncio.sleep(2)

        except Exception as e:
            print(f"[学习] 添加选修课失败: {e}")

    async def _study_course(self, course: dict, user_phone: str):
        """
        学习单个课程（按照开发要求文档实现）

        Args:
            course: 课程信息
            user_phone: 用户手机号
        """
        try:
            course_name = course.get('name', '未知课程')
            video_url = course.get('videoUrl', '')

            print(f"[学习] 开始学习课程: {course_name}")

            if not video_url:
                print(f"[学习] 课程 {course_name} 没有视频URL，跳过")
                return

            # 打开视频播放页面
            await self.page.goto(video_url)
            await self.page.wait_for_load_state("networkidle")
            await asyncio.sleep(2)

            # 处理可能的温馨提示页面
            await self._handle_warm_tips()

            # 执行学习状态判断并更新用户信息
            await self._update_user_status(user_phone)

            # 开始视频学习
            await self._handle_video_learning(course, user_phone)

            print(f"[学习] 课程学习完成: {course_name}")
            await self._log_study_action(user_phone, f"完成课程: {course_name}")

        except Exception as e:
            print(f"[学习] 学习课程失败: {e}")

    async def _handle_warm_tips(self):
        """处理温馨提示页面"""
        try:
            current_url = self.page.url
            if "videoChoose" in current_url:
                print("[学习] 检测到温馨提示页面，点击进入课件")
                choose_content = await self.page.wait_for_selector('.choose-content', timeout=5000)
                if choose_content:
                    await choose_content.click()
                    await self.page.wait_for_load_state("networkidle")
                    await asyncio.sleep(2)
        except Exception as e:
            print(f"[学习] 处理温馨提示失败: {e}")

    async def _update_user_status(self, user_phone: str):
        """更新用户状态"""
        try:
            # 这里应该调用API获取用户状态
            # 暂时跳过，需要集成API捕获功能
            print(f"[学习] 更新用户 {user_phone} 状态")
        except Exception as e:
            print(f"[学习] 更新用户状态失败: {e}")

    async def _handle_video_learning(self, course: dict, user_phone: str):
        """处理视频学习"""
        try:
            course_name = course.get('name', '未知课程')

            # 获取视频播放时间和总时长
            video_info = await self._get_video_info()
            if video_info:
                print(f"[学习] 视频信息 - 当前时间: {video_info['current_time']}s, 总时长: {video_info['duration']}s")

            # 监听视频播放完成
            while True:
                # 检查是否有未完成的课件
                has_unfinished = await self._check_unfinished_courseware()

                if not has_unfinished:
                    # 所有课件都已完成，更新课程状态
                    course['completed'] = '1'
                    course['completed_date'] = datetime.now().strftime('%Y-%m-%d')
                    print(f"[学习] 课程 {course_name} 所有课件已完成")
                    break

                # 等待一段时间再检查
                await asyncio.sleep(10)

        except Exception as e:
            print(f"[学习] 视频学习处理失败: {e}")

    async def _get_video_info(self) -> dict:
        """获取视频信息"""
        try:
            video_info = await self.page.evaluate("""
                () => {
                    const video = document.querySelector('video');
                    if (video) {
                        return {
                            current_time: video.currentTime,
                            duration: video.duration,
                            paused: video.paused
                        };
                    }
                    return null;
                }
            """)
            return video_info
        except Exception as e:
            print(f"[学习] 获取视频信息失败: {e}")
            return None

    async def _check_unfinished_courseware(self) -> bool:
        """检查是否存在未完成的课件"""
        try:
            has_unfinished = await self.page.evaluate("""
                () => {
                    const elements = document.querySelectorAll('span[title]');
                    return Array.from(elements).some(el => el.textContent.includes('未完成'));
                }
            """)
            return has_unfinished
        except Exception as e:
            print(f"[学习] 检查未完成课件失败: {e}")
            return False

    async def _handle_video_playback(self):
        """处理视频播放"""
        try:
            # 查找视频播放器
            video_selector = self.selectors['video_player']
            video_element = await self.page.query_selector(video_selector)

            if video_element:
                print("[学习] 找到视频播放器")

                # 尝试点击播放按钮
                play_button = await self.page.query_selector(self.selectors['play_button'])
                if play_button:
                    await play_button.click()
                    print("[学习] 已点击播放按钮")

                # 监控视频播放进度
                await self._monitor_video_progress()
            else:
                print("[学习] 未找到视频播放器，可能是其他类型的课程")

        except Exception as e:
            print(f"[学习] 处理视频播放失败: {e}")

    async def _monitor_video_progress(self):
        """监控视频播放进度"""
        try:
            print("[学习] 开始监控视频播放进度")

            while self.is_studying:
                # 检查视频是否还在播放
                video_element = await self.page.query_selector(self.selectors['video_player'])
                if not video_element:
                    break

                # 获取视频播放状态
                is_paused = await video_element.evaluate("video => video.paused")
                current_time = await video_element.evaluate("video => video.currentTime")
                duration = await video_element.evaluate("video => video.duration")

                if duration and current_time:
                    progress = (current_time / duration) * 100
                    print(f"[学习] 视频播放进度: {progress:.1f}%")

                    # 如果视频播放完成
                    if progress >= 95:  # 95%认为完成
                        print("[学习] 视频播放完成")
                        break

                # 如果视频暂停，尝试继续播放
                if is_paused:
                    await video_element.evaluate("video => video.play()")

                await asyncio.sleep(5)  # 每5秒检查一次

        except Exception as e:
            print(f"[学习] 监控视频进度失败: {e}")

    async def _wait_for_completion(self):
        """等待学习完成"""
        try:
            # 等待一定时间确保学习完成
            await asyncio.sleep(10)

            # 这里可以添加更复杂的完成检测逻辑
            # 比如检查页面上的完成标识、进度条等

        except Exception as e:
            print(f"[学习] 等待完成失败: {e}")

    def get_status(self) -> Dict[str, Any]:
        """
        获取学习状态

        Returns:
            Dict[str, Any]: 状态信息
        """
        return {
            "is_studying": self.is_studying,
            "current_course": getattr(self, 'current_course', None),
            "study_progress": getattr(self, 'study_progress', 0),
            "auto_next": self.auto_next,
            "has_page": self.page is not None
        }

