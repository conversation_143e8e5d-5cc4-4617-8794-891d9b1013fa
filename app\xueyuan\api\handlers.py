# coding:utf-8
"""
API响应处理器

该模块提供API响应数据的处理功能，包括数据解析、验证和转换。

主要功能：
- 响应数据解析
- 数据验证和清洗
- 业务逻辑处理
- 数据格式转换

类说明：
- APIResponseHandler: API响应处理器基类
- OnlineDataHandler: 在线学习数据处理器
- StudentArchivesHandler: 学生档案数据处理器
"""

import json
from typing import Dict, Any, Optional, List
from datetime import datetime
from abc import ABC, abstractmethod

from ..database.dao import DAOFactory


class APIResponseHandler(ABC):
    """
    API响应处理器基类
    
    定义API响应数据处理的标准接口
    """
    
    def __init__(self, api_url: str):
        """
        初始化处理器
        
        Args:
            api_url: API URL
        """
        self.api_url = api_url
        self.user_dao = DAOFactory.get_user_dao()
        self.course_dao = DAOFactory.get_course_dao()
        self.log_dao = DAOFactory.get_log_dao()
    
    @abstractmethod
    async def handle(self, data: Dict[str, Any]) -> bool:
        """
        处理API响应数据
        
        Args:
            data: API响应数据
            
        Returns:
            bool: 处理是否成功
        """
        pass
    
    def validate_response(self, data: Dict[str, Any]) -> bool:
        """
        验证响应数据格式
        
        Args:
            data: 响应数据
            
        Returns:
            bool: 数据是否有效
        """
        if not isinstance(data, dict):
            return False
        
        # 检查基本字段
        if 'code' not in data or 'msg' not in data:
            return False
        
        # 检查响应状态
        if data.get('code') != 0:
            print(f"[API处理] API响应错误: {data.get('msg', '未知错误')}")
            return False
        
        return True
    
    def log_processing(self, message: str, level: str = "INFO"):
        """
        记录处理日志
        
        Args:
            message: 日志消息
            level: 日志级别
        """
        try:
            log_data = {
                'user_phone': '',  # 需要从上下文获取
                'level': level,
                'message': f"[API处理] {message}",
                'module': self.__class__.__name__,
                'timestamp': datetime.now()
            }
            self.log_dao.create(log_data)
        except Exception as e:
            print(f"[API处理] 日志记录失败: {e}")


class OnlineDataHandler(APIResponseHandler):
    """
    在线学习数据处理器
    
    处理 /api/report/myData/online API的响应数据
    """
    
    def __init__(self):
        super().__init__("https://study.jxgbwlxy.gov.cn/api/report/myData/online")
    
    async def handle(self, data: Dict[str, Any]) -> bool:
        """
        处理在线学习数据
        
        Args:
            data: API响应数据
            
        Returns:
            bool: 处理是否成功
        """
        try:
            if not self.validate_response(data):
                return False
            
            # 提取学习数据
            learning_data = data.get('data', {})
            if not learning_data:
                print("[API处理] 在线学习数据为空")
                return False
            
            # 解析学习状态
            examine_status = learning_data.get('examineStatus', '0')
            complete_status = learning_data.get('completeStatus', '0')
            online_total_credit = float(learning_data.get('onlineTotalCredit', 0))
            compulsory_credit = float(learning_data.get('compulsoryCredit', 0))
            electives_credit = float(learning_data.get('electivesCredit', 0))
            
            print(f"[API处理] 学习状态 - 审核状态: {examine_status}, 完成状态: {complete_status}")
            print(f"[API处理] 学分信息 - 总学分: {online_total_credit}, 必修: {compulsory_credit}, 选修: {electives_credit}")
            
            # 更新用户学习状态（这里需要知道当前用户）
            # 由于没有用户上下文，暂时只记录日志
            self.log_processing(f"在线学习数据更新 - 总学分: {online_total_credit}")
            
            # 可以在这里添加更多业务逻辑
            # 例如：检查学分是否达到要求、更新学习进度等
            
            return True
            
        except Exception as e:
            print(f"[API处理] 在线学习数据处理失败: {e}")
            self.log_processing(f"在线学习数据处理失败: {e}", "ERROR")
            return False
    
    def extract_credit_info(self, data: Dict[str, Any]) -> Dict[str, float]:
        """
        提取学分信息
        
        Args:
            data: 学习数据
            
        Returns:
            Dict[str, float]: 学分信息
        """
        return {
            'total_credit': float(data.get('onlineTotalCredit', 0)),
            'compulsory_credit': float(data.get('compulsoryCredit', 0)),
            'electives_credit': float(data.get('electivesCredit', 0)),
            'zwy_credit': float(data.get('zwyCredit', 0)),
            'years_responsibilities_credits': float(data.get('yearsResponsibilitiesCredits', 0)),
            'shift_credit': float(data.get('shiftCredit', 0)),
            'online_answer_credits': float(data.get('onlineAnswerCredits', 0))
        }
    
    def check_completion_status(self, data: Dict[str, Any]) -> Dict[str, bool]:
        """
        检查完成状态
        
        Args:
            data: 学习数据
            
        Returns:
            Dict[str, bool]: 完成状态信息
        """
        return {
            'is_examined': data.get('examineStatus') == '1',
            'is_completed': data.get('completeStatus') == '1',
            'has_sufficient_credit': float(data.get('onlineTotalCredit', 0)) >= 100  # 假设需要100学分
        }


class StudentArchivesHandler(APIResponseHandler):
    """
    学生档案数据处理器
    
    处理 /api/study/student/studentArchives/student API的响应数据
    """
    
    def __init__(self):
        super().__init__("https://study.jxgbwlxy.gov.cn/api/study/student/studentArchives/student")
    
    async def handle(self, data: Dict[str, Any]) -> bool:
        """
        处理学生档案数据
        
        Args:
            data: API响应数据
            
        Returns:
            bool: 处理是否成功
        """
        try:
            if not self.validate_response(data):
                return False
            
            # 提取学生档案数据
            student_data = data.get('data', {})
            if not student_data:
                print("[API处理] 学生档案数据为空")
                return False
            
            # 解析学生信息
            student_id = student_data.get('id', '')
            student_name = student_data.get('stuName', '')
            student_status = student_data.get('stuStatus', '0')
            work_rank = student_data.get('workRank', '0')
            
            # 解析工作单位信息
            work_company = student_data.get('workCompany', {})
            company_name = work_company.get('name', '') if work_company else ''
            
            print(f"[API处理] 学生信息 - ID: {student_id}, 姓名: {student_name}")
            print(f"[API处理] 状态信息 - 学生状态: {student_status}, 工作级别: {work_rank}")
            print(f"[API处理] 工作单位: {company_name}")
            
            # 更新或创建用户记录
            await self._update_user_info(student_data)
            
            self.log_processing(f"学生档案数据更新 - {student_name} ({student_id})")
            
            return True
            
        except Exception as e:
            print(f"[API处理] 学生档案数据处理失败: {e}")
            self.log_processing(f"学生档案数据处理失败: {e}", "ERROR")
            return False
    
    async def _update_user_info(self, student_data: Dict[str, Any]):
        """
        更新用户信息
        
        Args:
            student_data: 学生数据
        """
        try:
            student_id = student_data.get('id', '')
            student_name = student_data.get('stuName', '')
            
            if not student_id or not student_name:
                print("[API处理] 学生ID或姓名为空，跳过用户信息更新")
                return
            
            # 查找现有用户（这里需要通过其他方式关联，比如手机号）
            # 由于API响应中没有手机号，暂时只记录信息
            
            print(f"[API处理] 需要更新用户信息: {student_name} (ID: {student_id})")
            
            # 可以在这里添加用户信息更新逻辑
            # 例如：根据学生ID查找用户、更新姓名和状态等
            
        except Exception as e:
            print(f"[API处理] 用户信息更新失败: {e}")
    
    def extract_student_info(self, data: Dict[str, Any]) -> Dict[str, str]:
        """
        提取学生基本信息
        
        Args:
            data: 学生数据
            
        Returns:
            Dict[str, str]: 学生信息
        """
        work_company = data.get('workCompany', {})
        
        return {
            'student_id': data.get('id', ''),
            'student_name': data.get('stuName', ''),
            'student_status': data.get('stuStatus', '0'),
            'work_rank': data.get('workRank', '0'),
            'company_id': work_company.get('id', '') if work_company else '',
            'company_name': work_company.get('name', '') if work_company else '',
            'parent_ids': work_company.get('parentIds', '') if work_company else ''
        }


class APIHandlerFactory:
    """API处理器工厂类"""
    
    _handlers = {
        "https://study.jxgbwlxy.gov.cn/api/report/myData/online": OnlineDataHandler,
        "https://study.jxgbwlxy.gov.cn/api/study/student/studentArchives/student": StudentArchivesHandler
    }
    
    @classmethod
    def get_handler(cls, api_url: str) -> Optional[APIResponseHandler]:
        """
        获取API处理器
        
        Args:
            api_url: API URL
            
        Returns:
            Optional[APIResponseHandler]: 处理器实例
        """
        handler_class = cls._handlers.get(api_url)
        if handler_class:
            return handler_class()
        return None
    
    @classmethod
    def register_handler(cls, api_url: str, handler_class: type):
        """
        注册API处理器
        
        Args:
            api_url: API URL
            handler_class: 处理器类
        """
        cls._handlers[api_url] = handler_class
        print(f"[API处理] 注册处理器: {api_url} -> {handler_class.__name__}")
    
    @classmethod
    def get_supported_apis(cls) -> List[str]:
        """
        获取支持的API列表
        
        Returns:
            List[str]: API URL列表
        """
        return list(cls._handlers.keys())
