# coding:utf-8
"""
课程学习流程模块

该模块提供课程学习相关功能，包括课程数据获取、
视频播放监控、学习进度跟踪等。

主要组件：
- manager: 课程管理器
- monitor: 学习监控器
- progress: 进度跟踪器

使用示例：
    from app.xueyuan.course import CourseManager
    
    manager = CourseManager()
    courses = await manager.get_user_courses(user_phone)
"""

from .manager import CourseManager, course_manager
from .monitor import LearningMonitor
from .progress import ProgressTracker

__all__ = ['CourseManager', 'course_manager', 'LearningMonitor', 'ProgressTracker']
