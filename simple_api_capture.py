import asyncio
from playwright.async_api import async_playwright
import json
import time

# 目标API列表 - 只捕获这两个API
TARGET_APIS = [
    "https://study.jxgbwlxy.gov.cn/api/report/myData/online",
    "https://study.jxgbwlxy.gov.cn/api/study/student/studentArchives/student"
]

def is_target_api(url: str) -> bool:
    """判断是否为目标API"""
    return url in TARGET_APIS

async def capture_simple_api_data():
    """简化的API数据捕获"""
    async with async_playwright() as p:
        # 启动浏览器
        browser = await p.chromium.launch(headless=False, slow_mo=500)
        context = await browser.new_context()
        page = await context.new_page()
        
        # 存储捕获的API数据
        api_data = {}
        
        # 网络监听器
        async def handle_response(response):
            url = response.url
            
            if is_target_api(url):
                print(f"[API捕获] 发现目标API: {url}")
                
                try:
                    if response.status == 200:
                        data = await response.json()
                        api_data[url] = {
                            'status': response.status,
                            'data': data,
                            'timestamp': time.time()
                        }
                        print(f"[API捕获] 成功捕获数据: {url}")
                        print(f"[API捕获] 数据大小: {len(str(data))} 字符")

                        # 输出捕获到的内容
                        print("=" * 60)
                        print(f"[API内容] URL: {url}")
                        print(f"[API内容] 状态码: {response.status}")
                        print(f"[API内容] 捕获时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime())}")
                        print("[API内容] 响应数据:")

                        # 格式化输出JSON数据
                        try:
                            formatted_data = json.dumps(data, ensure_ascii=False, indent=2)
                            # 如果数据太长，截断显示
                            if len(formatted_data) > 2000:
                                print(formatted_data[:2000] + "\n... [数据过长，已截断显示] ...")
                            else:
                                print(formatted_data)
                        except Exception as format_error:
                            print(f"数据格式化失败: {format_error}")
                            print(f"原始数据类型: {type(data)}")
                            print(f"原始数据: {str(data)[:500]}...")

                        print("=" * 60)

                        # 直接更新/保存数据到文件
                        save_api_data(api_data)
                        
                    else:
                        print(f"[API捕获] API响应状态异常: {url} (状态: {response.status})")
                        
                except Exception as e:
                    print(f"[API捕获] 处理API响应失败: {url} - {e}")
        
        # 设置网络监听器
        page.on("response", handle_response)
        print("[系统] 已设置网络监听器")
        
        # 1. 登录流程
        print("[登录] 开始登录...")
        await page.goto("https://study.jxgbwlxy.gov.cn/index")
        await page.wait_for_load_state("networkidle")
        
        # 填写登录信息
        await page.fill('.el-input__inner[placeholder="您的手机号"]', "18907995545")
        await page.fill('.el-input__inner[placeholder="请输入密码"]', "Shuai52018336")
        
        # 输入验证码
        captcha = input("请输入验证码: ")
        await page.fill('.el-input__inner[placeholder="验证码"]', captcha)
        
        # 登录
        await page.click('.loginBtn.el-button')
        await page.wait_for_url("**/study/data", timeout=30000)
        print("[登录] 登录成功")
        
        # 等待API请求
        await page.wait_for_load_state("networkidle")
        await asyncio.sleep(3)
        
        print(f"[系统] 登录阶段完成，已捕获 {len(api_data)} 个目标API")
        
        # 2. 访问课程播放页面
        print("[课程] 准备访问课程播放页面...")
        
        # 可以在这里修改为具体的课程页面URL
        video_url = "https://study.jxgbwlxy.gov.cn/video?id=202501xxptFKJ2025070TO12a"
        
        try:
            await page.goto(video_url)
            await page.wait_for_load_state("networkidle")
            print("[课程] 成功访问课程页面")
            
            # 等待可能的延迟API请求
            await asyncio.sleep(2)
            
            # 尝试触发更多API请求（滚动页面等）
            # await page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
            # await asyncio.sleep(2)
            # await page.evaluate("window.scrollTo(0, 0)")
            # await asyncio.sleep(2)
            
        except Exception as e:
            print(f"[课程] 访问课程页面失败: {e}")
        
        print(f"[系统] 课程页面访问完成，总共捕获 {len(api_data)} 个目标API")
        
        # 3. 最终保存
        if api_data:
            save_api_data(api_data)
            print(f"[系统] 数据捕获完成，共获取 {len(api_data)} 个API响应")
            
            # 显示捕获的API列表
            for url in api_data.keys():
                print(f"  - {url}")
        else:
            print("[系统] 未捕获到目标API数据")
        
        # 保持浏览器打开一段时间
        await asyncio.sleep(3)
        await browser.close()
        
        return api_data

def save_api_data(api_data: dict):
    """保存API数据到文件"""
    try:
        filename = "simple_api_data.json"
        with open(filename, "w", encoding="utf-8") as f:
            json.dump(api_data, f, ensure_ascii=False, indent=2)
        print(f"[保存] 数据已保存到 {filename}")
    except Exception as e:
        print(f"[保存] 保存数据失败: {e}")

def load_existing_data():
    """加载现有数据"""
    try:
        with open("simple_api_data.json", "r", encoding="utf-8") as f:
            return json.load(f)
    except FileNotFoundError:
        return {}
    except Exception as e:
        print(f"[加载] 加载现有数据失败: {e}")
        return {}

if __name__ == "__main__":
    print("=" * 50)
    print("简化API捕获工具")
    print("目标API:")
    for api in TARGET_APIS:
        print(f"  - {api}")
    print("=" * 50)
    
    # 运行捕获
    asyncio.run(capture_simple_api_data())
