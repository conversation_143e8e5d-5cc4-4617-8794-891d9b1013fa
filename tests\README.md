# 学习工具测试文档

本文档描述了学习工具项目的测试架构、测试类型和运行方法。

## 测试架构

```
tests/
├── __init__.py                 # 测试模块初始化
├── conftest.py                 # pytest配置和夹具
├── README.md                   # 测试文档
├── unit/                       # 单元测试
│   ├── __init__.py
│   ├── test_config.py          # 配置管理测试
│   ├── test_database.py        # 数据库测试
│   └── test_logging.py         # 日志系统测试
├── integration/                # 集成测试
│   ├── __init__.py
│   └── test_user_learning_flow.py  # 用户学习流程测试
└── performance/                # 性能测试
    ├── __init__.py
    └── test_performance.py     # 性能和压力测试
```

## 测试类型

### 1. 单元测试 (Unit Tests)

测试单个模块或类的功能，运行速度快，覆盖面广。

**覆盖模块：**
- 配置管理 (`test_config.py`)
- 数据库操作 (`test_database.py`)
- 日志系统 (`test_logging.py`)

**特点：**
- 使用模拟对象隔离依赖
- 测试边界条件和异常情况
- 验证函数输入输出正确性

### 2. 集成测试 (Integration Tests)

测试多个模块之间的协作，验证系统整体功能。

**覆盖场景：**
- 用户注册和登录流程
- 完整的课程学习流程
- 数据库与界面集成
- OCR与自动化集成

**特点：**
- 使用真实数据库连接
- 模拟外部服务（浏览器、OCR API）
- 测试端到端工作流

### 3. 性能测试 (Performance Tests)

测试系统在各种负载条件下的性能表现。

**测试内容：**
- 数据库操作性能
- 并发处理能力
- 内存使用监控
- 响应时间测试
- 系统压力测试

**特点：**
- 使用大量测试数据
- 模拟高并发场景
- 监控系统资源使用

## 运行测试

### 使用测试脚本（推荐）

```bash
# 检查测试环境
python run_tests.py check

# 运行单元测试
python run_tests.py unit

# 运行集成测试
python run_tests.py integration

# 运行性能测试
python run_tests.py performance

# 运行快速测试（排除慢速测试）
python run_tests.py fast

# 运行所有测试
python run_tests.py all

# 生成覆盖率报告
python run_tests.py coverage
```

### 使用pytest直接运行

```bash
# 运行所有测试
pytest

# 运行特定目录的测试
pytest tests/unit/
pytest tests/integration/
pytest tests/performance/

# 运行特定文件的测试
pytest tests/unit/test_database.py

# 运行特定测试类
pytest tests/unit/test_database.py::TestDatabaseManager

# 运行特定测试方法
pytest tests/unit/test_database.py::TestDatabaseManager::test_database_initialization

# 使用标记过滤测试
pytest -m unit           # 只运行单元测试
pytest -m integration    # 只运行集成测试
pytest -m performance    # 只运行性能测试
pytest -m "not slow"     # 排除慢速测试

# 详细输出
pytest -v

# 显示print输出
pytest -s

# 遇到第一个失败就停止
pytest -x

# 生成覆盖率报告
pytest --cov=app --cov-report=html
```

## 测试夹具 (Fixtures)

### 数据库夹具

- `test_db`: 临时测试数据库
- `populated_test_db`: 预填充数据的测试数据库
- `temp_dir`: 临时目录

### 数据夹具

- `test_user`: 测试用户对象
- `test_course`: 测试课程对象
- `test_log`: 测试日志对象

### 模拟对象夹具

- `mock_browser_manager`: 模拟浏览器管理器
- `mock_ocr_engine`: 模拟OCR引擎
- `mock_api_capture`: 模拟API捕获器

## 测试数据

测试使用以下类型的数据：

### 用户数据
```python
{
    "phone": "13800000001",
    "password": "test_password_123",
    "name": "测试用户",
    "status": "active",
    "compulsory_progress": 0,
    "elective_progress": 0
}
```

### 课程数据
```python
{
    "title": "测试课程",
    "course_type": "compulsory",
    "duration": 3600,
    "status": "available",
    "url": "https://example.com/course/test"
}
```

### 日志数据
```python
{
    "level": "INFO",
    "message": "测试日志消息",
    "module": "TestModule",
    "user_phone": "13800000001",
    "timestamp": "2025-01-14T10:00:00"
}
```

## 测试配置

### pytest.ini
```ini
[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = 
    -v
    --tb=short
    --strict-markers
    --disable-warnings
markers =
    unit: 单元测试
    integration: 集成测试
    performance: 性能测试
    slow: 慢速测试
```

### 环境要求

**Python版本：** >= 3.8

**必需包：**
- pytest
- PySide6
- qfluentwidgets
- playwright
- ddddocr
- psutil

**可选包：**
- pytest-cov (覆盖率测试)
- pytest-xdist (并行测试)
- pytest-html (HTML报告)

## 测试最佳实践

### 1. 测试命名

- 测试文件：`test_*.py`
- 测试类：`Test*`
- 测试方法：`test_*`
- 描述性命名：`test_user_creation_with_valid_data`

### 2. 测试结构

```python
def test_feature_name():
    # 1. 准备 (Arrange)
    user_data = {"phone": "13800000001", "name": "测试用户"}
    
    # 2. 执行 (Act)
    result = user_manager.create_user(user_data)
    
    # 3. 断言 (Assert)
    assert result is True
    assert user_manager.get_user("13800000001") is not None
```

### 3. 使用夹具

```python
def test_database_operation(test_db, test_user):
    # 使用预配置的测试数据库和测试用户
    user_dao = UserDAO()
    result = user_dao.create(test_user)
    assert result is True
```

### 4. 模拟外部依赖

```python
@patch('app.xueyuan.automation.browser.BrowserManager')
def test_learning_workflow(mock_browser):
    mock_browser.start_browser.return_value = True
    # 测试逻辑
```

### 5. 异常测试

```python
def test_invalid_user_creation():
    with pytest.raises(ValueError):
        user_manager.create_user({"invalid": "data"})
```

## 持续集成

测试可以集成到CI/CD流水线中：

```yaml
# GitHub Actions示例
- name: Run Tests
  run: |
    python run_tests.py check
    python run_tests.py fast
    python run_tests.py coverage
```

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查SQLite文件权限
   - 确保临时目录可写

2. **模拟对象未生效**
   - 检查patch路径是否正确
   - 确保在正确的作用域内使用

3. **性能测试超时**
   - 调整测试参数
   - 检查系统资源使用

4. **依赖包缺失**
   - 运行 `python run_tests.py check`
   - 安装缺失的包

### 调试技巧

1. **使用详细输出**
   ```bash
   pytest -v -s
   ```

2. **运行单个测试**
   ```bash
   pytest tests/unit/test_database.py::TestDatabaseManager::test_database_initialization -v
   ```

3. **使用pdb调试**
   ```python
   import pdb; pdb.set_trace()
   ```

4. **查看覆盖率报告**
   ```bash
   python run_tests.py coverage
   # 打开 htmlcov/index.html
   ```

## 贡献指南

添加新测试时请遵循以下规则：

1. 为新功能编写对应的单元测试
2. 重要的工作流需要集成测试
3. 性能关键的功能需要性能测试
4. 保持测试的独立性和可重复性
5. 使用描述性的测试名称和注释
6. 及时更新测试文档
