LinkCard {
    border: 1px solid rgb(234, 234, 234);
    border-radius: 10px;
    background-color: rgba(249, 249, 249, 0.95);
}

LinkCard:hover {
    background-color: rgba(249, 249, 249, 0.93);
    border: 1px solid rgb(220, 220, 220);
}

#titleLabel {
    font: 18px 'Segoe UI', 'Microsoft YaHei', 'PingFang SC';
    color: black;
}

#contentLabel {
    font: 12px 'Segoe UI', 'Microsoft YaHei', 'PingFang SC';
    color: rgb(93, 93, 93);
}

LinkCardView, FlowLinkCardView {
    background-color: transparent;
    border: none;
}

#view {
    background-color: transparent;
}

FlowLinkCardView > #viewTitleLabel {
    font: 24px 'Segoe UI Semibold', 'Microsoft YaHei';
    color: black;
    background-color: transparent;
    padding: 0px 0px 10px 0px;
}

LinkCardView > #viewTitleLabel {
    font: 24px 'Segoe UI Semibold', 'Microsoft YaHei';
    color: black;
    background-color: transparent;
    padding: 0px 0px 10px 0px;
}