# coding:utf-8
"""
学习工具集成测试脚本

该脚本用于测试学习工具与主应用的集成是否成功，
包括配置加载、界面创建、导航设置等功能。

使用方法：
    python test_integration.py
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_config_integration():
    """测试配置集成"""
    print("=" * 50)
    print("测试配置集成...")
    
    try:
        # 测试主应用配置
        from app.common.config import cfg, is_xueyuan_enabled, get_xueyuan_config
        
        print(f"✓ 主应用配置加载成功")
        print(f"  - 学习工具启用状态: {cfg.get(cfg.xueyuanEnabled)}")
        print(f"  - 学习工具可用性: {is_xueyuan_enabled()}")
        print(f"  - 最大并发用户数: {cfg.get(cfg.xueyuanMaxConcurrentUsers)}")
        print(f"  - 默认浏览器: {cfg.get(cfg.xueyuanDefaultBrowser)}")
        
        # 测试学习工具配置
        study_cfg = get_xueyuan_config()
        if study_cfg:
            print(f"✓ 学习工具配置加载成功")
            print(f"  - 异步登录: {study_cfg.get(study_cfg.asyncLogin)}")
            print(f"  - 浏览器类型: {study_cfg.get(study_cfg.browserType)}")
            print(f"  - OCR引擎: {study_cfg.get(study_cfg.primaryEngine)}")
            print(f"  - 学习模式: {study_cfg.get(study_cfg.studyMode)}")
        else:
            print("⚠ 学习工具配置未加载")
        
        return True
        
    except Exception as e:
        print(f"✗ 配置集成测试失败: {e}")
        return False

def test_interface_import():
    """测试界面导入"""
    print("=" * 50)
    print("测试界面导入...")
    
    try:
        # 测试学习工具界面导入
        from app.xueyuan.view import (
            UserManageInterface, StudyControlInterface, 
            ProgressMonitorInterface, LogViewInterface, StudySettingInterface
        )
        
        print("✓ 学习工具界面导入成功")
        print(f"  - UserManageInterface: {UserManageInterface}")
        print(f"  - StudyControlInterface: {StudyControlInterface}")
        print(f"  - ProgressMonitorInterface: {ProgressMonitorInterface}")
        print(f"  - LogViewInterface: {LogViewInterface}")
        print(f"  - StudySettingInterface: {StudySettingInterface}")
        
        return True
        
    except Exception as e:
        print(f"✗ 界面导入测试失败: {e}")
        return False

def test_config_utils():
    """测试配置工具"""
    print("=" * 50)
    print("测试配置工具...")
    
    try:
        from app.xueyuan.common.config_utils import ConfigUtils, get_config_summary, is_config_valid
        
        print("✓ 配置工具导入成功")
        
        # 测试配置摘要
        summary = get_config_summary()
        print(f"✓ 配置摘要获取成功")
        print(f"  - 系统配置: {summary.get('system', {})}")
        print(f"  - 浏览器配置: {summary.get('browser', {})}")
        
        # 测试配置验证
        is_valid = is_config_valid()
        print(f"✓ 配置验证: {'有效' if is_valid else '无效'}")
        
        return True
        
    except Exception as e:
        print(f"✗ 配置工具测试失败: {e}")
        return False

def test_main_window_integration():
    """测试主窗口集成"""
    print("=" * 50)
    print("测试主窗口集成...")
    
    try:
        # 不实际创建窗口，只测试类定义
        from app.view.main_window import MainWindow
        
        print("✓ 主窗口类导入成功")
        print(f"  - MainWindow: {MainWindow}")
        
        # 检查是否有学习工具相关的方法和属性
        main_window_attrs = dir(MainWindow)
        xueyuan_attrs = [attr for attr in main_window_attrs if 'xueyuan' in attr.lower() or 'study' in attr.lower()]
        
        if xueyuan_attrs:
            print(f"✓ 发现学习工具相关属性: {xueyuan_attrs}")
        
        return True
        
    except Exception as e:
        print(f"✗ 主窗口集成测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("学习工具集成测试")
    print("=" * 50)
    
    tests = [
        ("配置集成", test_config_integration),
        ("界面导入", test_interface_import),
        ("配置工具", test_config_utils),
        ("主窗口集成", test_main_window_integration),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 输出测试结果
    print("=" * 50)
    print("测试结果汇总:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！学习工具集成成功！")
        return 0
    else:
        print("⚠ 部分测试失败，请检查集成配置")
        return 1

if __name__ == "__main__":
    sys.exit(main())
