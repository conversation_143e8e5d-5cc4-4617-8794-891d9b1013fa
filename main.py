# coding:utf-8
"""
PyQt-Fluent-Widgets 应用程序主入口文件

这是基于 PySide6 和 PyQt-Fluent-Widgets 框架构建的现代化桌面应用程序的主启动文件。
该文件负责初始化应用程序、配置DPI缩放、设置国际化支持，并启动主窗口。

主要功能：
- 配置高DPI显示支持
- 初始化Qt应用程序实例
- 设置多语言国际化支持
- 创建并显示主窗口
- 启动应用程序事件循环

作者: zhiyiYo
版本: 基于 PyQt-Fluent-Widgets 框架
"""

import os
import sys

from PySide6.QtCore import Qt, QTranslator
from PySide6.QtWidgets import QApplication
from qfluentwidgets import FluentTranslator

from app.common.config import cfg
from app.view.main_window import MainWindow


# 启用DPI缩放设置
if cfg.get(cfg.dpiScale) != "Auto":
    os.environ["QT_ENABLE_HIGHDPI_SCALING"] = "0"
    os.environ["QT_SCALE_FACTOR"] = str(cfg.get(cfg.dpiScale))

# 创建应用程序实例
app = QApplication(sys.argv)
app.setAttribute(Qt.AA_DontCreateNativeWidgetSiblings)


# 国际化设置
locale = cfg.get(cfg.language).value
translator = FluentTranslator(locale)
galleryTranslator = QTranslator()
galleryTranslator.load(locale, "gallery", ".", ":/gallery/i18n")

app.installTranslator(translator)
app.installTranslator(galleryTranslator)

# 创建并显示主窗口
w = MainWindow()
w.show()

# 启动应用程序事件循环
app.exec()