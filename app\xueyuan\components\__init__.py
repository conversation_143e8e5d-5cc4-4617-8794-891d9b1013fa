# coding:utf-8
"""
界面组件模块

该模块提供学习工具的各种界面组件，包括状态栏、
工具栏、设置界面、关于界面等。

主要组件：
- StatusBar: 状态栏组件
- ToolBar: 工具栏组件
- SettingsInterface: 设置界面
- AboutInterface: 关于界面

使用示例：
    from app.xueyuan.components import StatusBar, ToolBar
    
    status_bar = StatusBar(parent)
    tool_bar = ToolBar(parent)
"""

from .status_bar import StatusBar
from .toolbar import ToolBar
from .settings_interface import SettingsInterface
from .about_interface import AboutInterface

__all__ = [
    'StatusBar',
    'ToolBar', 
    'SettingsInterface',
    'AboutInterface'
]
