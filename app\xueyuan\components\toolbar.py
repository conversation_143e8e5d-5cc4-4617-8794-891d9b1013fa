# coding:utf-8
"""
工具栏组件

该模块提供主窗口的工具栏组件，包含常用操作按钮、
快捷功能和系统控制等。

主要功能：
- 常用操作按钮
- 快捷功能访问
- 系统控制
- 主题切换

类说明：
- ToolBar: 工具栏类
"""

from PySide6.QtWidgets import QToolBar, QWidget, QHBoxLayout, QSizePolicy
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QAction, QIcon
from qfluentwidgets import (ToolButton, PushButton, ToggleButton, SplitPushButton,
                           FluentIcon as FIF, RoundMenu, MenuAnimationType, Action,
                           setTheme, Theme, isDarkTheme, ComboBox, SearchLineEdit)
from typing import Optional, Callable

from ..common.config_loader import study_cfg
from ..logging import log_manager


class ToolBar(QToolBar):
    """
    工具栏类
    
    提供主窗口的工具栏功能
    """
    
    # 信号定义
    start_automation = Signal()
    stop_automation = Signal()
    refresh_data = Signal()
    export_data = Signal()
    import_data = Signal()
    show_settings = Signal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self.cfg = study_cfg
        self.parent_window = parent
        
        self._init_ui()
        self._connect_signals()
    
    def _init_ui(self):
        """初始化界面"""
        # 设置工具栏属性
        self.setWindowTitle("工具栏")
        self.setMovable(False)
        self.setFloatable(False)
        self.setToolButtonStyle(Qt.ToolButtonTextBesideIcon)
        
        # 设置工具栏样式
        self.setStyleSheet("""
            QToolBar {
                background-color: transparent;
                border: none;
                spacing: 8px;
                padding: 4px;
            }
            QToolBar::separator {
                background-color: rgba(0, 0, 0, 0.1);
                width: 1px;
                margin: 4px 8px;
            }
        """)
        
        # 创建工具栏按钮
        self._create_automation_buttons()
        self.addSeparator()
        
        self._create_data_buttons()
        self.addSeparator()
        
        self._create_view_buttons()
        self.addSeparator()
        
        self._create_system_buttons()
        
        # 添加弹性空间
        spacer = QWidget()
        spacer.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        self.addWidget(spacer)
        
        # 搜索框
        self._create_search_widget()
        
        # 主题切换按钮
        self._create_theme_button()
    
    def _create_automation_buttons(self):
        """创建自动化控制按钮"""
        # 开始自动化按钮
        self.start_btn = ToolButton(FIF.PLAY)
        self.start_btn.setText("开始学习")
        self.start_btn.setToolTip("开始自动化学习")
        self.start_btn.clicked.connect(self.start_automation.emit)
        self.addWidget(self.start_btn)
        
        # 停止自动化按钮
        self.stop_btn = ToolButton(FIF.PAUSE)
        self.stop_btn.setText("停止学习")
        self.stop_btn.setToolTip("停止自动化学习")
        self.stop_btn.clicked.connect(self.stop_automation.emit)
        self.stop_btn.setEnabled(False)
        self.addWidget(self.stop_btn)
        
        # 批量操作按钮
        self.batch_btn = SplitPushButton(FIF.PEOPLE, "批量操作")
        self.batch_btn.setToolTip("批量用户操作")
        
        # 批量操作菜单
        batch_menu = RoundMenu(parent=self)
        batch_menu.addAction(Action(FIF.ADD, "批量添加用户"))
        batch_menu.addAction(Action(FIF.DELETE, "批量删除用户"))
        batch_menu.addAction(Action(FIF.SYNC, "批量同步状态"))
        batch_menu.addSeparator()
        batch_menu.addAction(Action(FIF.DOWNLOAD, "导出用户数据"))
        
        self.batch_btn.setFlyout(batch_menu)
        self.addWidget(self.batch_btn)
    
    def _create_data_buttons(self):
        """创建数据操作按钮"""
        # 刷新按钮
        self.refresh_btn = ToolButton(FIF.SYNC)
        self.refresh_btn.setText("刷新")
        self.refresh_btn.setToolTip("刷新当前数据")
        self.refresh_btn.clicked.connect(self.refresh_data.emit)
        self.addWidget(self.refresh_btn)
        
        # 导入导出按钮
        self.import_export_btn = SplitPushButton(FIF.SAVE, "数据管理")
        self.import_export_btn.setToolTip("数据导入导出")
        
        # 导入导出菜单
        data_menu = RoundMenu(parent=self)
        data_menu.addAction(Action(FIF.FOLDER, "导入用户数据"))
        data_menu.addAction(Action(FIF.SAVE, "导出用户数据"))
        data_menu.addSeparator()
        data_menu.addAction(Action(FIF.HISTORY, "导出日志数据"))
        data_menu.addAction(Action(FIF.BOOK_SHELF, "导出课程数据"))
        
        self.import_export_btn.setFlyout(data_menu)
        self.addWidget(self.import_export_btn)
    
    def _create_view_buttons(self):
        """创建视图控制按钮"""
        # 视图切换按钮
        self.view_btn = SplitPushButton(FIF.VIEW, "视图")
        self.view_btn.setToolTip("切换视图")
        
        # 视图菜单
        view_menu = RoundMenu(parent=self)
        view_menu.addAction(Action(FIF.PEOPLE, "用户管理"))
        view_menu.addAction(Action(FIF.PLAY, "自动化学习"))
        view_menu.addAction(Action(FIF.BOOK_SHELF, "课程管理"))
        view_menu.addAction(Action(FIF.HISTORY, "日志查看"))
        
        self.view_btn.setFlyout(view_menu)
        self.addWidget(self.view_btn)
        
        # 全屏按钮
        self.fullscreen_btn = ToggleButton(FIF.FULL_SCREEN)
        self.fullscreen_btn.setText("全屏")
        self.fullscreen_btn.setToolTip("切换全屏模式")
        self.fullscreen_btn.clicked.connect(self._toggle_fullscreen)
        self.addWidget(self.fullscreen_btn)
    
    def _create_system_buttons(self):
        """创建系统控制按钮"""
        # 设置按钮
        self.settings_btn = ToolButton(FIF.SETTING)
        self.settings_btn.setText("设置")
        self.settings_btn.setToolTip("打开设置")
        self.settings_btn.clicked.connect(self.show_settings.emit)
        self.addWidget(self.settings_btn)
        
        # 帮助按钮
        self.help_btn = SplitPushButton(FIF.HELP, "帮助")
        self.help_btn.setToolTip("获取帮助")
        
        # 帮助菜单
        help_menu = RoundMenu(parent=self)
        help_menu.addAction(Action(FIF.DOCUMENT, "用户手册"))
        help_menu.addAction(Action(FIF.QUESTION, "常见问题"))
        help_menu.addAction(Action(FIF.FEEDBACK, "反馈建议"))
        help_menu.addSeparator()
        help_menu.addAction(Action(FIF.INFO, "关于程序"))
        
        self.help_btn.setFlyout(help_menu)
        self.addWidget(self.help_btn)
    
    def _create_search_widget(self):
        """创建搜索组件"""
        self.search_box = SearchLineEdit()
        self.search_box.setPlaceholderText("搜索用户、课程、日志...")
        self.search_box.setFixedWidth(200)
        self.search_box.searchSignal.connect(self._on_search)
        self.addWidget(self.search_box)
    
    def _create_theme_button(self):
        """创建主题切换按钮"""
        self.theme_btn = ToggleButton(FIF.CONSTRACT)
        self.theme_btn.setText("深色")
        self.theme_btn.setToolTip("切换主题")
        self.theme_btn.setChecked(isDarkTheme())
        self.theme_btn.clicked.connect(self._toggle_theme)
        self.addWidget(self.theme_btn)
    
    def _connect_signals(self):
        """连接信号"""
        # 主题变化信号
        self.cfg.themeChanged.connect(self._on_theme_changed)
    
    def _toggle_fullscreen(self):
        """切换全屏模式"""
        if self.parent_window:
            if self.parent_window.isFullScreen():
                self.parent_window.showNormal()
                self.fullscreen_btn.setText("全屏")
                self.fullscreen_btn.setIcon(FIF.FULL_SCREEN)
            else:
                self.parent_window.showFullScreen()
                self.fullscreen_btn.setText("退出全屏")
                self.fullscreen_btn.setIcon(FIF.BACK)
    
    def _toggle_theme(self):
        """切换主题"""
        if isDarkTheme():
            setTheme(Theme.LIGHT)
            self.theme_btn.setText("深色")
            self.theme_btn.setIcon(FIF.CONSTRACT)
        else:
            setTheme(Theme.DARK)
            self.theme_btn.setText("浅色")
            self.theme_btn.setIcon(FIF.BRIGHTNESS)
        
        log_manager.info("主题已切换", module="ToolBar")
    
    def _on_theme_changed(self, theme):
        """主题变化处理"""
        is_dark = theme == Theme.DARK
        self.theme_btn.setChecked(is_dark)
        
        if is_dark:
            self.theme_btn.setText("浅色")
            self.theme_btn.setIcon(FIF.BRIGHTNESS)
        else:
            self.theme_btn.setText("深色")
            self.theme_btn.setIcon(FIF.CONSTRACT)
    
    def _on_search(self, text: str):
        """搜索处理"""
        if self.parent_window and hasattr(self.parent_window, 'search'):
            self.parent_window.search(text)
        
        log_manager.info(f"搜索: {text}", module="ToolBar")
    
    def set_automation_running(self, running: bool):
        """设置自动化运行状态"""
        self.start_btn.setEnabled(not running)
        self.stop_btn.setEnabled(running)
        
        if running:
            self.start_btn.setText("运行中...")
            self.start_btn.setIcon(FIF.SYNC)
        else:
            self.start_btn.setText("开始学习")
            self.start_btn.setIcon(FIF.PLAY)
    
    def set_data_loading(self, loading: bool):
        """设置数据加载状态"""
        self.refresh_btn.setEnabled(not loading)
        
        if loading:
            self.refresh_btn.setText("加载中...")
            self.refresh_btn.setIcon(FIF.SYNC)
        else:
            self.refresh_btn.setText("刷新")
            self.refresh_btn.setIcon(FIF.SYNC)
    
    def update_search_placeholder(self, text: str):
        """更新搜索框占位符"""
        self.search_box.setPlaceholderText(text)
    
    def clear_search(self):
        """清空搜索框"""
        self.search_box.clear()
    
    def get_search_text(self) -> str:
        """获取搜索文本"""
        return self.search_box.text()
    
    def add_custom_button(self, icon: FIF, text: str, tooltip: str, callback: Callable):
        """添加自定义按钮"""
        button = ToolButton(icon)
        button.setText(text)
        button.setToolTip(tooltip)
        button.clicked.connect(callback)
        self.addWidget(button)
        return button
    
    def add_custom_separator(self):
        """添加自定义分隔符"""
        self.addSeparator()
    
    def remove_button(self, button):
        """移除按钮"""
        self.removeAction(button)
    
    def enable_all_buttons(self, enabled: bool = True):
        """启用/禁用所有按钮"""
        for action in self.actions():
            action.setEnabled(enabled)
    
    def get_button_states(self) -> dict:
        """获取按钮状态"""
        return {
            'start_enabled': self.start_btn.isEnabled(),
            'stop_enabled': self.stop_btn.isEnabled(),
            'refresh_enabled': self.refresh_btn.isEnabled(),
            'fullscreen_checked': self.fullscreen_btn.isChecked(),
            'theme_checked': self.theme_btn.isChecked(),
            'search_text': self.search_box.text()
        }
