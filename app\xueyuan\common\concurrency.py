# coding:utf-8
"""
并发控制器

该模块提供并发控制功能，包括信号量控制、
速率限制、资源锁定等。

主要功能：
- 并发数量控制
- 速率限制
- 资源锁定管理
- 异步任务协调

类说明：
- ConcurrencyController: 并发控制器类
- RateLimiter: 速率限制器类
- ResourceLock: 资源锁类
"""

import asyncio
import threading
import time
from typing import Optional, Dict, Any, List, Callable, Union
from datetime import datetime, timedelta
from dataclasses import dataclass
from contextlib import asynccontextmanager, contextmanager
from collections import defaultdict, deque

from .config_loader import get_study_config


@dataclass
class RateLimitConfig:
    """速率限制配置"""
    max_requests: int  # 最大请求数
    time_window: float  # 时间窗口（秒）
    burst_size: int = 0  # 突发大小


class RateLimiter:
    """
    速率限制器类
    
    提供基于令牌桶算法的速率限制功能
    """
    
    def __init__(self, max_requests: int, time_window: float, burst_size: Optional[int] = None):
        """
        初始化速率限制器
        
        Args:
            max_requests: 时间窗口内最大请求数
            time_window: 时间窗口（秒）
            burst_size: 突发大小，默认等于max_requests
        """
        self.max_requests = max_requests
        self.time_window = time_window
        self.burst_size = burst_size or max_requests
        
        # 令牌桶
        self.tokens = self.burst_size
        self.last_update = time.time()
        
        # 请求历史
        self.request_times = deque()
        
        # 锁
        self.lock = threading.Lock()
        
        print(f"[速率限制] 速率限制器初始化: {max_requests}请求/{time_window}秒")
    
    def acquire(self, tokens: int = 1) -> bool:
        """
        获取令牌
        
        Args:
            tokens: 需要的令牌数
            
        Returns:
            bool: 是否成功获取
        """
        with self.lock:
            current_time = time.time()
            
            # 更新令牌桶
            self._update_tokens(current_time)
            
            # 检查是否有足够令牌
            if self.tokens >= tokens:
                self.tokens -= tokens
                self.request_times.append(current_time)
                return True
            
            return False
    
    async def acquire_async(self, tokens: int = 1, timeout: Optional[float] = None) -> bool:
        """
        异步获取令牌
        
        Args:
            tokens: 需要的令牌数
            timeout: 超时时间
            
        Returns:
            bool: 是否成功获取
        """
        start_time = time.time()
        
        while True:
            if self.acquire(tokens):
                return True
            
            # 检查超时
            if timeout and (time.time() - start_time) >= timeout:
                return False
            
            # 等待一小段时间后重试
            await asyncio.sleep(0.1)
    
    def _update_tokens(self, current_time: float):
        """
        更新令牌桶
        
        Args:
            current_time: 当前时间
        """
        # 计算应该添加的令牌数
        time_passed = current_time - self.last_update
        tokens_to_add = (time_passed / self.time_window) * self.max_requests
        
        # 更新令牌数（不超过突发大小）
        self.tokens = min(self.burst_size, self.tokens + tokens_to_add)
        self.last_update = current_time
        
        # 清理过期的请求记录
        cutoff_time = current_time - self.time_window
        while self.request_times and self.request_times[0] < cutoff_time:
            self.request_times.popleft()
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        with self.lock:
            current_time = time.time()
            self._update_tokens(current_time)
            
            return {
                'max_requests': self.max_requests,
                'time_window': self.time_window,
                'burst_size': self.burst_size,
                'current_tokens': self.tokens,
                'requests_in_window': len(self.request_times)
            }


class ResourceLock:
    """
    资源锁类
    
    提供命名资源的锁定功能
    """
    
    def __init__(self):
        """初始化资源锁"""
        self.locks: Dict[str, threading.Lock] = {}
        self.async_locks: Dict[str, asyncio.Lock] = {}
        self.lock_stats: Dict[str, Dict[str, Any]] = defaultdict(lambda: {
            'acquire_count': 0,
            'wait_time_total': 0,
            'last_acquired': None
        })
        self.main_lock = threading.Lock()
        
        print("[资源锁] 资源锁管理器初始化完成")
    
    @contextmanager
    def acquire_lock(self, resource_name: str, timeout: Optional[float] = None):
        """
        获取资源锁（同步版本）
        
        Args:
            resource_name: 资源名称
            timeout: 超时时间
        """
        # 获取或创建锁
        with self.main_lock:
            if resource_name not in self.locks:
                self.locks[resource_name] = threading.Lock()
            lock = self.locks[resource_name]
        
        # 记录开始时间
        start_time = time.time()
        
        try:
            # 获取锁
            acquired = lock.acquire(timeout=timeout)
            if not acquired:
                raise TimeoutError(f"获取资源锁超时: {resource_name}")
            
            # 更新统计
            wait_time = time.time() - start_time
            stats = self.lock_stats[resource_name]
            stats['acquire_count'] += 1
            stats['wait_time_total'] += wait_time
            stats['last_acquired'] = datetime.now()
            
            print(f"[资源锁] 获取锁成功: {resource_name}")
            yield
            
        finally:
            if 'acquired' in locals() and acquired:
                lock.release()
                print(f"[资源锁] 释放锁: {resource_name}")
    
    @asynccontextmanager
    async def acquire_lock_async(self, resource_name: str, timeout: Optional[float] = None):
        """
        获取资源锁（异步版本）
        
        Args:
            resource_name: 资源名称
            timeout: 超时时间
        """
        # 获取或创建异步锁
        if resource_name not in self.async_locks:
            self.async_locks[resource_name] = asyncio.Lock()
        lock = self.async_locks[resource_name]
        
        # 记录开始时间
        start_time = time.time()
        
        try:
            # 获取锁
            if timeout:
                await asyncio.wait_for(lock.acquire(), timeout=timeout)
            else:
                await lock.acquire()
            
            # 更新统计
            wait_time = time.time() - start_time
            stats = self.lock_stats[resource_name]
            stats['acquire_count'] += 1
            stats['wait_time_total'] += wait_time
            stats['last_acquired'] = datetime.now()
            
            print(f"[资源锁] 获取异步锁成功: {resource_name}")
            yield
            
        finally:
            lock.release()
            print(f"[资源锁] 释放异步锁: {resource_name}")
    
    def get_lock_stats(self, resource_name: Optional[str] = None) -> Dict[str, Any]:
        """
        获取锁统计信息
        
        Args:
            resource_name: 资源名称，None表示获取所有
            
        Returns:
            Dict[str, Any]: 统计信息
        """
        if resource_name:
            return dict(self.lock_stats.get(resource_name, {}))
        else:
            return {name: dict(stats) for name, stats in self.lock_stats.items()}


class ConcurrencyController:
    """
    并发控制器类
    
    提供综合的并发控制功能
    """
    
    def __init__(self):
        """初始化并发控制器"""
        # 配置
        cfg = get_study_config()
        self.max_concurrent_tasks = cfg.maxConcurrentTasks.value
        self.max_concurrent_users = cfg.maxConcurrentUsers.value
        self.api_rate_limit = cfg.apiRateLimit.value  # 每分钟API调用次数
        
        # 信号量
        self.task_semaphore = asyncio.Semaphore(self.max_concurrent_tasks)
        self.user_semaphore = asyncio.Semaphore(self.max_concurrent_users)
        
        # 速率限制器
        self.rate_limiters: Dict[str, RateLimiter] = {
            'api_calls': RateLimiter(self.api_rate_limit, 60.0),  # API调用限制
            'login_attempts': RateLimiter(10, 300.0),  # 登录尝试限制（10次/5分钟）
            'ocr_requests': RateLimiter(100, 60.0),  # OCR请求限制（100次/分钟）
        }
        
        # 资源锁
        self.resource_lock = ResourceLock()
        
        # 活跃任务跟踪
        self.active_tasks: Dict[str, Dict[str, Any]] = {}
        self.active_users: Dict[str, Dict[str, Any]] = {}
        
        # 统计信息
        self.stats = {
            'total_tasks': 0,
            'concurrent_tasks': 0,
            'max_concurrent_reached': 0,
            'rate_limit_hits': 0,
            'lock_contentions': 0
        }
        
        print(f"[并发控制] 并发控制器初始化完成")
        print(f"[并发控制] 最大并发任务数: {self.max_concurrent_tasks}")
        print(f"[并发控制] 最大并发用户数: {self.max_concurrent_users}")
    
    @asynccontextmanager
    async def acquire_task_slot(self, task_id: str, task_info: Optional[Dict[str, Any]] = None):
        """
        获取任务槽位
        
        Args:
            task_id: 任务ID
            task_info: 任务信息
        """
        try:
            # 获取信号量
            await self.task_semaphore.acquire()
            
            # 记录活跃任务
            self.active_tasks[task_id] = {
                'start_time': datetime.now(),
                'info': task_info or {}
            }
            
            # 更新统计
            self.stats['total_tasks'] += 1
            self.stats['concurrent_tasks'] += 1
            self.stats['max_concurrent_reached'] = max(
                self.stats['max_concurrent_reached'],
                self.stats['concurrent_tasks']
            )
            
            print(f"[并发控制] 获取任务槽位: {task_id}")
            yield
            
        finally:
            # 释放信号量
            self.task_semaphore.release()
            
            # 移除活跃任务
            if task_id in self.active_tasks:
                del self.active_tasks[task_id]
            
            # 更新统计
            self.stats['concurrent_tasks'] -= 1
            
            print(f"[并发控制] 释放任务槽位: {task_id}")
    
    @asynccontextmanager
    async def acquire_user_slot(self, user_phone: str, user_info: Optional[Dict[str, Any]] = None):
        """
        获取用户槽位
        
        Args:
            user_phone: 用户手机号
            user_info: 用户信息
        """
        try:
            # 获取信号量
            await self.user_semaphore.acquire()
            
            # 记录活跃用户
            self.active_users[user_phone] = {
                'start_time': datetime.now(),
                'info': user_info or {}
            }
            
            print(f"[并发控制] 获取用户槽位: {user_phone}")
            yield
            
        finally:
            # 释放信号量
            self.user_semaphore.release()
            
            # 移除活跃用户
            if user_phone in self.active_users:
                del self.active_users[user_phone]
            
            print(f"[并发控制] 释放用户槽位: {user_phone}")
    
    async def check_rate_limit(self, limiter_name: str, tokens: int = 1) -> bool:
        """
        检查速率限制
        
        Args:
            limiter_name: 限制器名称
            tokens: 需要的令牌数
            
        Returns:
            bool: 是否通过限制
        """
        if limiter_name not in self.rate_limiters:
            print(f"[并发控制] 未知的速率限制器: {limiter_name}")
            return True
        
        limiter = self.rate_limiters[limiter_name]
        result = await limiter.acquire_async(tokens)
        
        if not result:
            self.stats['rate_limit_hits'] += 1
            print(f"[并发控制] 速率限制触发: {limiter_name}")
        
        return result
    
    @asynccontextmanager
    async def acquire_resource_lock(self, resource_name: str, timeout: Optional[float] = None):
        """
        获取资源锁
        
        Args:
            resource_name: 资源名称
            timeout: 超时时间
        """
        async with self.resource_lock.acquire_lock_async(resource_name, timeout):
            yield
    
    def add_rate_limiter(self, name: str, config: RateLimitConfig):
        """
        添加速率限制器
        
        Args:
            name: 限制器名称
            config: 限制器配置
        """
        self.rate_limiters[name] = RateLimiter(
            config.max_requests,
            config.time_window,
            config.burst_size
        )
        print(f"[并发控制] 添加速率限制器: {name}")
    
    def get_active_tasks(self) -> Dict[str, Dict[str, Any]]:
        """
        获取活跃任务列表
        
        Returns:
            Dict[str, Dict[str, Any]]: 活跃任务
        """
        return self.active_tasks.copy()
    
    def get_active_users(self) -> Dict[str, Dict[str, Any]]:
        """
        获取活跃用户列表
        
        Returns:
            Dict[str, Dict[str, Any]]: 活跃用户
        """
        return self.active_users.copy()
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        # 获取速率限制器统计
        rate_limiter_stats = {}
        for name, limiter in self.rate_limiters.items():
            rate_limiter_stats[name] = limiter.get_stats()
        
        # 获取资源锁统计
        lock_stats = self.resource_lock.get_lock_stats()
        
        return {
            'concurrency': {
                'max_concurrent_tasks': self.max_concurrent_tasks,
                'max_concurrent_users': self.max_concurrent_users,
                'current_tasks': len(self.active_tasks),
                'current_users': len(self.active_users),
                'available_task_slots': self.task_semaphore._value,
                'available_user_slots': self.user_semaphore._value
            },
            'statistics': self.stats.copy(),
            'rate_limiters': rate_limiter_stats,
            'resource_locks': lock_stats
        }
    
    async def wait_for_slot(self, slot_type: str = 'task', timeout: Optional[float] = None) -> bool:
        """
        等待槽位可用
        
        Args:
            slot_type: 槽位类型（'task' 或 'user'）
            timeout: 超时时间
            
        Returns:
            bool: 是否获得槽位
        """
        try:
            if slot_type == 'task':
                semaphore = self.task_semaphore
            elif slot_type == 'user':
                semaphore = self.user_semaphore
            else:
                raise ValueError(f"未知的槽位类型: {slot_type}")
            
            if timeout:
                await asyncio.wait_for(semaphore.acquire(), timeout=timeout)
            else:
                await semaphore.acquire()
            
            # 立即释放，只是检查可用性
            semaphore.release()
            return True
            
        except asyncio.TimeoutError:
            return False
        except Exception as e:
            print(f"[并发控制] 等待槽位失败: {e}")
            return False


# 全局并发控制器实例
concurrency_controller = ConcurrencyController()
