# coding:utf-8
"""
浏览器管理器

该模块提供Playwright浏览器的管理功能，包括浏览器启动、
页面管理、配置设置等。

主要功能：
- 浏览器启动和关闭
- 页面创建和管理
- 浏览器配置
- 错误处理和重试

类说明：
- BrowserManager: 浏览器管理器类
"""

import asyncio
from typing import Optional, Dict, Any, List
from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, BrowserContext, Page, Playwright

from ..common.config_loader import get_study_config
from ..common.constants import BrowserType


class BrowserManager:
    """
    浏览器管理器类
    
    基于Playwright实现的浏览器管理功能，
    提供浏览器启动、页面管理等功能。
    """
    
    def __init__(self):
        """初始化浏览器管理器"""
        self.playwright: Optional[Playwright] = None
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None
        self.is_running = False
        
        # 配置
        cfg = get_study_config()
        self.browser_type = cfg.get(cfg.browserType)
        self.headless = cfg.get(cfg.headless)
        self.page_timeout = cfg.get(cfg.pageLoadTimeout) * 1000  # 转换为毫秒
        
        print(f"[浏览器] 初始化完成 - 类型: {self.browser_type}, 无头模式: {self.headless}")
    
    async def start(self) -> bool:
        """
        启动浏览器
        
        Returns:
            bool: 启动是否成功
        """
        if self.is_running:
            print("[浏览器] 浏览器已在运行中")
            return True
        
        try:
            print("[浏览器] 正在启动浏览器...")
            
            # 启动Playwright
            self.playwright = await async_playwright().start()
            
            # 选择浏览器类型
            if self.browser_type == "firefox":
                browser_launcher = self.playwright.firefox
            elif self.browser_type == "webkit":
                browser_launcher = self.playwright.webkit
            else:  # 默认使用chromium
                browser_launcher = self.playwright.chromium
            
            # 启动浏览器
            self.browser = await browser_launcher.launch(
                headless=self.headless,
                slow_mo=500,  # 减慢操作速度，更稳定
                args=[
                    '--no-sandbox',
                    '--disable-blink-features=AutomationControlled',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor'
                ]
            )
            
            # 创建浏览器上下文
            self.context = await self.browser.new_context(
                viewport={'width': 1920, 'height': 1080},
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            )
            
            # 设置超时
            self.context.set_default_timeout(self.page_timeout)
            self.context.set_default_navigation_timeout(self.page_timeout)
            
            # 创建页面
            self.page = await self.context.new_page()
            
            self.is_running = True
            print(f"[浏览器] 浏览器启动成功 - {self.browser_type}")
            return True
            
        except Exception as e:
            print(f"[浏览器] 浏览器启动失败: {e}")
            await self.cleanup()
            return False
    
    async def stop(self):
        """停止浏览器"""
        if not self.is_running:
            print("[浏览器] 浏览器未在运行中")
            return
        
        await self.cleanup()
        print("[浏览器] 浏览器已停止")
    
    async def cleanup(self):
        """清理资源"""
        try:
            if self.page:
                await self.page.close()
                self.page = None
            
            if self.context:
                await self.context.close()
                self.context = None
            
            if self.browser:
                await self.browser.close()
                self.browser = None
            
            if self.playwright:
                await self.playwright.stop()
                self.playwright = None
            
            self.is_running = False
            
        except Exception as e:
            print(f"[浏览器] 清理资源失败: {e}")
    
    async def new_page(self) -> Optional[Page]:
        """
        创建新页面
        
        Returns:
            Optional[Page]: 新页面对象
        """
        if not self.context:
            print("[浏览器] 浏览器上下文不可用")
            return None
        
        try:
            page = await self.context.new_page()
            print("[浏览器] 创建新页面成功")
            return page
        except Exception as e:
            print(f"[浏览器] 创建新页面失败: {e}")
            return None
    
    async def goto(self, url: str, wait_until: str = "networkidle") -> bool:
        """
        导航到指定URL
        
        Args:
            url: 目标URL
            wait_until: 等待条件
            
        Returns:
            bool: 导航是否成功
        """
        if not self.page:
            print("[浏览器] 页面对象不可用")
            return False
        
        try:
            print(f"[浏览器] 导航到: {url}")
            await self.page.goto(url, wait_until=wait_until)
            print(f"[浏览器] 导航成功: {url}")
            return True
        except Exception as e:
            print(f"[浏览器] 导航失败: {url} - {e}")
            return False
    
    async def wait_for_load_state(self, state: str = "networkidle", timeout: Optional[int] = None):
        """
        等待页面加载状态
        
        Args:
            state: 加载状态
            timeout: 超时时间（毫秒）
        """
        if not self.page:
            print("[浏览器] 页面对象不可用")
            return
        
        try:
            await self.page.wait_for_load_state(state, timeout=timeout or self.page_timeout)
        except Exception as e:
            print(f"[浏览器] 等待加载状态失败: {e}")
    
    async def screenshot(self, path: str = None) -> Optional[bytes]:
        """
        截图
        
        Args:
            path: 保存路径（可选）
            
        Returns:
            Optional[bytes]: 截图数据
        """
        if not self.page:
            print("[浏览器] 页面对象不可用")
            return None
        
        try:
            screenshot_data = await self.page.screenshot(path=path, full_page=True)
            if path:
                print(f"[浏览器] 截图已保存: {path}")
            return screenshot_data
        except Exception as e:
            print(f"[浏览器] 截图失败: {e}")
            return None
    
    def get_page(self) -> Optional[Page]:
        """
        获取当前页面对象
        
        Returns:
            Optional[Page]: 页面对象
        """
        return self.page
    
    def get_context(self) -> Optional[BrowserContext]:
        """
        获取浏览器上下文
        
        Returns:
            Optional[BrowserContext]: 浏览器上下文
        """
        return self.context
    
    def get_browser(self) -> Optional[Browser]:
        """
        获取浏览器对象
        
        Returns:
            Optional[Browser]: 浏览器对象
        """
        return self.browser
    
    def is_browser_running(self) -> bool:
        """
        检查浏览器是否在运行
        
        Returns:
            bool: 浏览器是否在运行
        """
        return self.is_running and self.browser is not None
    
    async def get_page_info(self) -> Dict[str, Any]:
        """
        获取页面信息
        
        Returns:
            Dict[str, Any]: 页面信息
        """
        if not self.page:
            return {}
        
        try:
            return {
                "url": self.page.url,
                "title": await self.page.title(),
                "viewport": self.page.viewport_size,
                "is_closed": self.page.is_closed()
            }
        except Exception as e:
            print(f"[浏览器] 获取页面信息失败: {e}")
            return {}
    
    async def execute_script(self, script: str) -> Any:
        """
        执行JavaScript脚本
        
        Args:
            script: JavaScript代码
            
        Returns:
            Any: 执行结果
        """
        if not self.page:
            print("[浏览器] 页面对象不可用")
            return None
        
        try:
            result = await self.page.evaluate(script)
            return result
        except Exception as e:
            print(f"[浏览器] 执行脚本失败: {e}")
            return None
    
    async def wait_for_selector(self, selector: str, timeout: Optional[int] = None) -> bool:
        """
        等待选择器出现
        
        Args:
            selector: CSS选择器
            timeout: 超时时间（毫秒）
            
        Returns:
            bool: 是否找到元素
        """
        if not self.page:
            print("[浏览器] 页面对象不可用")
            return False
        
        try:
            await self.page.wait_for_selector(selector, timeout=timeout or self.page_timeout)
            return True
        except Exception as e:
            print(f"[浏览器] 等待选择器失败: {selector} - {e}")
            return False
    
    def get_status(self) -> Dict[str, Any]:
        """
        获取浏览器状态
        
        Returns:
            Dict[str, Any]: 状态信息
        """
        return {
            "is_running": self.is_running,
            "browser_type": self.browser_type,
            "headless": self.headless,
            "page_timeout": self.page_timeout,
            "has_browser": self.browser is not None,
            "has_context": self.context is not None,
            "has_page": self.page is not None
        }
