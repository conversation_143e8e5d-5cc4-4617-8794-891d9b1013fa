# coding:utf-8
"""
pytest配置文件

该文件包含pytest的全局配置和夹具定义，
为所有测试提供共享的测试环境和数据。

主要功能：
- 测试数据库配置
- 测试用户数据
- 模拟对象创建
- 测试环境清理

夹具说明：
- test_db: 测试数据库
- test_user: 测试用户数据
- mock_browser: 模拟浏览器
- temp_config: 临时配置
"""

import pytest
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, MagicMock
from datetime import datetime, timedelta

# 导入项目模块
from app.xueyuan.database.manager import DatabaseManager
from app.xueyuan.database.models import User, Course, Log, APIData
from app.xueyuan.common.config import StudyConfig
from app.xueyuan.logging.manager import LogManager


@pytest.fixture(scope="session")
def temp_dir():
    """创建临时目录"""
    temp_path = Path(tempfile.mkdtemp(prefix="xueyuan_test_"))
    yield temp_path
    shutil.rmtree(temp_path, ignore_errors=True)


@pytest.fixture(scope="session")
def test_db(temp_dir):
    """创建测试数据库"""
    db_path = temp_dir / "test.db"
    db_manager = DatabaseManager(str(db_path))
    
    # 初始化数据库
    db_manager.init_database()
    
    yield db_manager
    
    # 清理
    db_manager.close()


@pytest.fixture
def test_config(temp_dir):
    """创建测试配置"""
    config = StudyConfig()
    
    # 设置测试配置
    config.set("Logging", "LogDir", str(temp_dir / "logs"))
    config.set("System", "ConcurrentCount", 1)
    config.set("System", "DelayTime", 0)
    config.set("Browser", "Headless", True)
    config.set("OCR", "PrimaryEngine", "ddddocr")
    
    return config


@pytest.fixture
def test_users():
    """创建测试用户数据"""
    return [
        {
            "phone": "13800000001",
            "password": "password123",
            "name": "测试用户1",
            "status": "active",
            "compulsory_progress": 0,
            "elective_progress": 0
        },
        {
            "phone": "13800000002", 
            "password": "password456",
            "name": "测试用户2",
            "status": "inactive",
            "compulsory_progress": 50,
            "elective_progress": 30
        },
        {
            "phone": "13800000003",
            "password": "password789",
            "name": "测试用户3", 
            "status": "completed",
            "compulsory_progress": 100,
            "elective_progress": 100
        }
    ]


@pytest.fixture
def test_courses():
    """创建测试课程数据"""
    return [
        {
            "title": "测试必修课程1",
            "course_type": "compulsory",
            "duration": 3600,
            "status": "available",
            "url": "https://example.com/course1"
        },
        {
            "title": "测试选修课程1",
            "course_type": "elective", 
            "duration": 1800,
            "status": "available",
            "url": "https://example.com/course2"
        },
        {
            "title": "测试必修课程2",
            "course_type": "compulsory",
            "duration": 2700,
            "status": "unavailable",
            "url": "https://example.com/course3"
        }
    ]


@pytest.fixture
def test_logs():
    """创建测试日志数据"""
    base_time = datetime.now()
    return [
        {
            "level": "INFO",
            "message": "测试信息日志",
            "module": "TestModule",
            "user_phone": "13800000001",
            "timestamp": base_time
        },
        {
            "level": "WARNING",
            "message": "测试警告日志",
            "module": "TestModule",
            "user_phone": "13800000002", 
            "timestamp": base_time - timedelta(hours=1)
        },
        {
            "level": "ERROR",
            "message": "测试错误日志",
            "module": "TestModule",
            "user_phone": "13800000003",
            "timestamp": base_time - timedelta(hours=2)
        }
    ]


@pytest.fixture
def mock_browser():
    """创建模拟浏览器"""
    browser = MagicMock()
    
    # 模拟页面对象
    page = MagicMock()
    page.goto = MagicMock()
    page.fill = MagicMock()
    page.click = MagicMock()
    page.wait_for_selector = MagicMock()
    page.screenshot = MagicMock()
    page.evaluate = MagicMock()
    
    # 模拟上下文对象
    context = MagicMock()
    context.new_page.return_value = page
    context.close = MagicMock()
    
    # 设置浏览器返回值
    browser.new_context.return_value = context
    browser.close = MagicMock()
    
    return browser


@pytest.fixture
def mock_ocr_engine():
    """创建模拟OCR引擎"""
    ocr = MagicMock()
    ocr.classification.return_value = "test_captcha"
    return ocr


@pytest.fixture
def mock_api_capture():
    """创建模拟API捕获器"""
    capture = MagicMock()
    capture.start_capture = MagicMock()
    capture.stop_capture = MagicMock()
    capture.get_captured_data.return_value = [
        {
            "url": "https://api.example.com/test",
            "method": "POST",
            "status": 200,
            "response": {"success": True, "data": "test"}
        }
    ]
    return capture


@pytest.fixture
def populated_test_db(test_db, test_users, test_courses, test_logs):
    """填充测试数据的数据库"""
    from app.xueyuan.database.dao import UserDAO, CourseDAO, LogDAO
    
    user_dao = UserDAO()
    course_dao = CourseDAO()
    log_dao = LogDAO()
    
    # 添加测试用户
    for user_data in test_users:
        user = User(**user_data)
        user_dao.create(user)
    
    # 添加测试课程
    for course_data in test_courses:
        course = Course(**course_data)
        course_dao.create(course)
    
    # 添加测试日志
    for log_data in test_logs:
        log_dao.create(log_data)
    
    return test_db


@pytest.fixture
def log_manager(temp_dir):
    """创建测试日志管理器"""
    log_dir = temp_dir / "logs"
    log_dir.mkdir(exist_ok=True)
    
    manager = LogManager()
    manager.log_dir = str(log_dir)
    
    return manager


# pytest配置
def pytest_configure(config):
    """pytest配置"""
    # 添加自定义标记
    config.addinivalue_line(
        "markers", "unit: 单元测试"
    )
    config.addinivalue_line(
        "markers", "integration: 集成测试"
    )
    config.addinivalue_line(
        "markers", "performance: 性能测试"
    )
    config.addinivalue_line(
        "markers", "slow: 慢速测试"
    )


def pytest_collection_modifyitems(config, items):
    """修改测试收集"""
    # 为慢速测试添加标记
    for item in items:
        if "performance" in item.nodeid:
            item.add_marker(pytest.mark.slow)


# 测试辅助函数
def create_test_user(phone="13800000000", **kwargs):
    """创建测试用户"""
    default_data = {
        "phone": phone,
        "password": "test_password",
        "name": "测试用户",
        "status": "active",
        "compulsory_progress": 0,
        "elective_progress": 0
    }
    default_data.update(kwargs)
    return User(**default_data)


def create_test_course(title="测试课程", **kwargs):
    """创建测试课程"""
    default_data = {
        "title": title,
        "course_type": "compulsory",
        "duration": 3600,
        "status": "available",
        "url": "https://example.com/course"
    }
    default_data.update(kwargs)
    return Course(**default_data)


def create_test_log(message="测试日志", **kwargs):
    """创建测试日志"""
    default_data = {
        "level": "INFO",
        "message": message,
        "module": "TestModule",
        "user_phone": "13800000000",
        "timestamp": datetime.now()
    }
    default_data.update(kwargs)
    return default_data


# 导出测试辅助函数
__all__ = [
    "create_test_user",
    "create_test_course", 
    "create_test_log"
]
