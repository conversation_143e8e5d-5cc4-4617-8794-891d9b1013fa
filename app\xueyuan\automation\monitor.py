# coding:utf-8
"""
状态监控器

该模块提供学习状态监控功能，包括进度跟踪、
异常检测、性能监控等。

主要功能：
- 学习进度监控
- 系统状态检查
- 异常情况检测
- 性能指标统计

类说明：
- StatusMonitor: 状态监控器类
"""

import asyncio
import time
from typing import Optional, Dict, Any, List, Callable
from datetime import datetime
from playwright.async_api import Page

from ..common.config_loader import get_study_config
from ..database.dao import DAOFactory


class StatusMonitor:
    """
    状态监控器类
    
    提供学习状态监控和异常检测功能
    """
    
    def __init__(self, page: Optional[Page] = None):
        """
        初始化状态监控器
        
        Args:
            page: Playwright页面对象
        """
        self.page = page
        self.is_monitoring = False
        self.monitor_interval = 10  # 监控间隔（秒）
        self.status_history: List[Dict[str, Any]] = []
        self.callbacks: List[Callable] = []
        
        # 配置
        cfg = get_study_config()
        self.base_url = cfg.get(cfg.baseUrl)
        
        # 数据库DAO
        self.log_dao = DAOFactory.get_log_dao()
        
        # 监控指标
        self.metrics = {
            'start_time': None,
            'total_checks': 0,
            'error_count': 0,
            'last_check_time': None,
            'page_load_times': [],
            'network_errors': 0
        }
        
        print("[监控] 状态监控器初始化完成")
    
    def set_page(self, page: Page):
        """
        设置Playwright页面对象
        
        Args:
            page: Playwright页面对象
        """
        self.page = page
        print("[监控] 页面对象已设置")
    
    def add_callback(self, callback: Callable):
        """
        添加状态变化回调函数
        
        Args:
            callback: 回调函数
        """
        self.callbacks.append(callback)
        print("[监控] 添加状态回调函数")
    
    async def start_monitoring(self) -> bool:
        """
        开始状态监控
        
        Returns:
            bool: 启动是否成功
        """
        if self.is_monitoring:
            print("[监控] 状态监控已在运行中")
            return True
        
        if not self.page:
            print("[监控] 页面对象未设置")
            return False
        
        try:
            print("[监控] 开始状态监控")
            
            self.is_monitoring = True
            self.metrics['start_time'] = time.time()
            
            # 启动监控循环
            asyncio.create_task(self._monitoring_loop())
            
            print("[监控] 状态监控已启动")
            return True
            
        except Exception as e:
            print(f"[监控] 启动状态监控失败: {e}")
            return False
    
    async def stop_monitoring(self):
        """停止状态监控"""
        if not self.is_monitoring:
            print("[监控] 状态监控未在运行中")
            return
        
        self.is_monitoring = False
        print("[监控] 状态监控已停止")
    
    async def _monitoring_loop(self):
        """监控循环"""
        try:
            while self.is_monitoring:
                await self._perform_status_check()
                await asyncio.sleep(self.monitor_interval)
                
        except Exception as e:
            print(f"[监控] 监控循环异常: {e}")
            self.is_monitoring = False
    
    async def _perform_status_check(self):
        """执行状态检查"""
        try:
            check_time = time.time()
            self.metrics['total_checks'] += 1
            self.metrics['last_check_time'] = check_time
            
            # 收集状态信息
            status_info = await self._collect_status_info()
            
            # 检查异常情况
            anomalies = await self._detect_anomalies(status_info)
            
            # 记录状态历史
            status_record = {
                'timestamp': check_time,
                'datetime': datetime.now(),
                'status': status_info,
                'anomalies': anomalies
            }
            
            self.status_history.append(status_record)
            
            # 保持历史记录数量限制
            if len(self.status_history) > 100:
                self.status_history.pop(0)
            
            # 触发回调函数
            await self._trigger_callbacks(status_record)
            
            # 记录异常情况
            if anomalies:
                await self._log_anomalies(anomalies)
            
            print(f"[监控] 状态检查完成 - 检查次数: {self.metrics['total_checks']}")
            
        except Exception as e:
            print(f"[监控] 状态检查失败: {e}")
            self.metrics['error_count'] += 1
    
    async def _collect_status_info(self) -> Dict[str, Any]:
        """
        收集状态信息
        
        Returns:
            Dict[str, Any]: 状态信息
        """
        try:
            status_info = {
                'page_url': self.page.url,
                'page_title': await self.page.title(),
                'is_page_loaded': True,
                'network_status': 'connected',
                'browser_status': 'active'
            }
            
            # 检查页面加载状态
            try:
                await self.page.wait_for_load_state("networkidle", timeout=5000)
                status_info['page_load_state'] = 'networkidle'
            except:
                status_info['page_load_state'] = 'loading'
            
            # 检查关键元素是否存在
            status_info['key_elements'] = await self._check_key_elements()
            
            # 检查JavaScript错误
            status_info['js_errors'] = await self._check_js_errors()
            
            # 检查网络请求状态
            status_info['network_requests'] = await self._check_network_status()
            
            return status_info
            
        except Exception as e:
            print(f"[监控] 收集状态信息失败: {e}")
            return {'error': str(e)}
    
    async def _check_key_elements(self) -> Dict[str, bool]:
        """
        检查关键元素是否存在
        
        Returns:
            Dict[str, bool]: 元素存在状态
        """
        key_selectors = {
            'login_form': '.login-form, .el-form, form[class*="login"]',
            'user_menu': '.user-menu, .user-info, .header-user, [class*="user"]',
            'course_list': '.course-list, .course-item, .course-card, [class*="course"]',
            'video_player': 'video, .video-player, .player, [class*="video"]',
            'navigation': '.navigation, .nav, .menu, .header-nav, [class*="nav"]'
        }
        
        element_status = {}
        
        for name, selector in key_selectors.items():
            try:
                element = await self.page.query_selector(selector)
                element_status[name] = element is not None
            except:
                element_status[name] = False
        
        return element_status
    
    async def _check_js_errors(self) -> List[str]:
        """
        检查JavaScript错误
        
        Returns:
            List[str]: 错误列表
        """
        try:
            # 获取控制台错误
            errors = await self.page.evaluate("""
                () => {
                    const errors = [];
                    const originalError = console.error;
                    console.error = function(...args) {
                        errors.push(args.join(' '));
                        originalError.apply(console, args);
                    };
                    return window.jsErrors || [];
                }
            """)
            
            return errors if isinstance(errors, list) else []
            
        except Exception as e:
            print(f"[监控] 检查JS错误失败: {e}")
            return []
    
    async def _check_network_status(self) -> Dict[str, Any]:
        """
        检查网络请求状态
        
        Returns:
            Dict[str, Any]: 网络状态信息
        """
        try:
            # 检查网络连接状态
            network_status = await self.page.evaluate("""
                () => {
                    return {
                        online: navigator.onLine,
                        connection: navigator.connection ? {
                            effectiveType: navigator.connection.effectiveType,
                            downlink: navigator.connection.downlink,
                            rtt: navigator.connection.rtt
                        } : null
                    };
                }
            """)
            
            return network_status
            
        except Exception as e:
            print(f"[监控] 检查网络状态失败: {e}")
            return {'error': str(e)}
    
    async def _detect_anomalies(self, status_info: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        检测异常情况
        
        Args:
            status_info: 状态信息
            
        Returns:
            List[Dict[str, Any]]: 异常列表
        """
        anomalies = []
        
        try:
            # 检查页面加载异常
            if status_info.get('page_load_state') == 'loading':
                anomalies.append({
                    'type': 'page_load_timeout',
                    'message': '页面加载超时',
                    'severity': 'warning'
                })
            
            # 检查关键元素缺失（只有当所有元素都缺失时才报告为错误）
            key_elements = status_info.get('key_elements', {})
            missing_elements = [name for name, exists in key_elements.items() if not exists]
            if missing_elements and len(missing_elements) == len(key_elements):
                anomalies.append({
                    'type': 'missing_elements',
                    'message': f'关键元素缺失: {", ".join(missing_elements)}',
                    'severity': 'warning'  # 降低严重性
                })
            elif missing_elements:
                # 部分元素缺失，仅记录信息
                print(f"[监控] 部分关键元素缺失: {', '.join(missing_elements)}")
            
            # 检查JavaScript错误
            js_errors = status_info.get('js_errors', [])
            if js_errors:
                anomalies.append({
                    'type': 'javascript_errors',
                    'message': f'JavaScript错误: {len(js_errors)}个',
                    'details': js_errors,
                    'severity': 'warning'
                })
            
            # 检查网络连接
            network_status = status_info.get('network_requests', {})
            if not network_status.get('online', True):
                anomalies.append({
                    'type': 'network_offline',
                    'message': '网络连接断开',
                    'severity': 'critical'
                })
            
            return anomalies
            
        except Exception as e:
            print(f"[监控] 异常检测失败: {e}")
            return []
    
    async def _trigger_callbacks(self, status_record: Dict[str, Any]):
        """
        触发回调函数
        
        Args:
            status_record: 状态记录
        """
        for callback in self.callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(status_record)
                else:
                    callback(status_record)
            except Exception as e:
                print(f"[监控] 回调函数执行失败: {e}")
    
    async def _log_anomalies(self, anomalies: List[Dict[str, Any]]):
        """
        记录异常情况
        
        Args:
            anomalies: 异常列表
        """
        try:
            for anomaly in anomalies:
                log_data = {
                    'user_phone': '',  # 需要从上下文获取
                    'level': anomaly.get('severity', 'INFO').upper(),
                    'message': f"[状态监控] {anomaly['message']}",
                    'module': 'StatusMonitor',
                    'timestamp': datetime.now()
                }
                
                self.log_dao.create(log_data)
                
        except Exception as e:
            print(f"[监控] 记录异常日志失败: {e}")
    
    def get_status_history(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        获取状态历史
        
        Args:
            limit: 返回记录数量限制
            
        Returns:
            List[Dict[str, Any]]: 状态历史
        """
        return self.status_history[-limit:] if self.status_history else []
    
    def get_metrics(self) -> Dict[str, Any]:
        """
        获取监控指标
        
        Returns:
            Dict[str, Any]: 监控指标
        """
        current_time = time.time()
        uptime = current_time - self.metrics['start_time'] if self.metrics['start_time'] else 0
        
        return {
            'is_monitoring': self.is_monitoring,
            'uptime': uptime,
            'total_checks': self.metrics['total_checks'],
            'error_count': self.metrics['error_count'],
            'error_rate': self.metrics['error_count'] / max(self.metrics['total_checks'], 1),
            'last_check_time': self.metrics['last_check_time'],
            'monitor_interval': self.monitor_interval,
            'status_history_count': len(self.status_history)
        }
    
    def get_current_status(self) -> Dict[str, Any]:
        """
        获取当前状态
        
        Returns:
            Dict[str, Any]: 当前状态
        """
        if not self.status_history:
            return {}
        
        return self.status_history[-1]
    
    def set_monitor_interval(self, interval: int):
        """
        设置监控间隔
        
        Args:
            interval: 监控间隔（秒）
        """
        self.monitor_interval = max(1, interval)
        print(f"[监控] 监控间隔已设置为: {self.monitor_interval}秒")
    
    def clear_history(self):
        """清空状态历史"""
        self.status_history.clear()
        print("[监控] 状态历史已清空")
    
    def get_anomaly_summary(self) -> Dict[str, Any]:
        """
        获取异常情况汇总
        
        Returns:
            Dict[str, Any]: 异常汇总
        """
        anomaly_types = {}
        total_anomalies = 0
        
        for record in self.status_history:
            anomalies = record.get('anomalies', [])
            total_anomalies += len(anomalies)
            
            for anomaly in anomalies:
                anomaly_type = anomaly.get('type', 'unknown')
                if anomaly_type not in anomaly_types:
                    anomaly_types[anomaly_type] = 0
                anomaly_types[anomaly_type] += 1
        
        return {
            'total_anomalies': total_anomalies,
            'anomaly_types': anomaly_types,
            'anomaly_rate': total_anomalies / max(len(self.status_history), 1)
        }
