# coding:utf-8
"""
学习设置界面

该模块定义了学习设置界面类 StudySettingInterface，
使用 QFluentWidgets 的官方设置卡片组件，简洁高效。

主要功能：
- 系统设置（并发数、超时时间、重试次数等）
- 浏览器设置（浏览器类型、无头模式、页面超时等）
- OCR设置（主引擎、备用引擎、API密钥等）
- API设置（捕获启用、请求超时等）
- 网站设置（基础URL、登录URL、学习URL等）

类说明：
- StudySettingInterface: 学习设置界面类，使用官方设置卡片组件
"""

from qfluentwidgets import (SettingCardGroup, SwitchSettingCard, OptionsSettingCard,
                            RangeSettingCard, SettingCard, ScrollArea,
                            ExpandLayout, FluentIcon as FIF, LineEdit)
from PySide6.QtCore import Qt
from PySide6.QtWidgets import QWidget, QLabel

from ..common.config_loader import get_study_config


class StudySettingInterface(ScrollArea):
    """学习设置界面类 - 使用官方设置卡片组件"""
    
    def __init__(self, parent=None):
        """初始化学习设置界面"""
        super().__init__(parent)
        self.setObjectName("StudySettingInterface")

        # 创建滚动容器
        self.scrollWidget = QWidget()
        self.expandLayout = ExpandLayout(self.scrollWidget)

        # 获取配置实例
        self.cfg = get_study_config()

        # 设置标签
        self.settingLabel = QLabel("学习工具设置", self)

        # 创建所有设置组和卡片
        self._createSettingCards()

        # 初始化界面
        self.__initWidget()

    def _createSettingCards(self):
        """创建所有设置卡片"""

        # 系统设置组
        self.systemGroup = SettingCardGroup("系统设置", self.scrollWidget)

        self.maxConcurrentCard = RangeSettingCard(
            self.cfg.maxConcurrentUsers,
            FIF.PEOPLE,
            "最大并发用户数",
            "同时运行的最大用户数量",
            parent=self.systemGroup
        )

        self.taskTimeoutCard = RangeSettingCard(
            self.cfg.defaultTimeout,
            FIF.SPEED_OFF,
            "默认超时时间",
            "任务执行的超时时间（秒）",
            parent=self.systemGroup
        )

        self.retryCountCard = RangeSettingCard(
            self.cfg.retryCount,
            FIF.SYNC,
            "重试次数",
            "失败后的重试次数",
            parent=self.systemGroup
        )

        # 浏览器设置组
        self.browserGroup = SettingCardGroup("浏览器设置", self.scrollWidget)

        self.browserTypeCard = OptionsSettingCard(
            self.cfg.browserType,
            FIF.GLOBE,
            "浏览器类型",
            "选择要使用的浏览器",
            texts=["chrome", "firefox", "edge"],
            parent=self.browserGroup
        )

        self.headlessCard = SwitchSettingCard(
            FIF.TRANSPARENT,
            "无头模式",
            "在后台运行浏览器，不显示界面",
            self.cfg.headless,
            parent=self.browserGroup
        )

        self.pageTimeoutCard = RangeSettingCard(
            self.cfg.pageLoadTimeout,
            FIF.SPEED_OFF,
            "页面加载超时",
            "页面加载的超时时间（秒）",
            parent=self.browserGroup
        )
        
        # OCR设置组
        self.ocrGroup = SettingCardGroup("OCR设置", self.scrollWidget)
        
        self.primaryEngineCard = OptionsSettingCard(
            self.cfg.primaryEngine,
            FIF.CAMERA,
            "主OCR引擎",
            "主要使用的OCR识别引擎",
            texts=["baidu", "tesseract", "easyocr"],
            parent=self.ocrGroup
        )

        self.fallbackEngineCard = OptionsSettingCard(
            self.cfg.fallbackEngine,
            FIF.SYNC,
            "备用OCR引擎",
            "主引擎失败时使用的备用引擎",
            texts=["baidu", "tesseract", "easyocr"],
            parent=self.ocrGroup
        )

        # 创建百度API密钥设置卡片
        self.baiduApiKeyCard = SettingCard(
            FIF.CERTIFICATE,
            "百度API密钥",
            "百度OCR服务的API密钥",
            parent=self.ocrGroup
        )
        self.baiduApiKeyEdit = LineEdit()
        self.baiduApiKeyEdit.setText(self.cfg.get(self.cfg.baiduApiKey))
        self.baiduApiKeyEdit.setFixedWidth(200)  # 设置固定宽度
        self.baiduApiKeyEdit.setContentsMargins(0, 0, 16, 0)  # 给输入框本身设置右边距
        self.baiduApiKeyEdit.textChanged.connect(
            lambda text: self.cfg.set(self.cfg.baiduApiKey, text)
        )
        self.baiduApiKeyCard.hBoxLayout.addWidget(self.baiduApiKeyEdit)

        # 创建百度密钥设置卡片
        self.baiduSecretKeyCard = SettingCard(
            FIF.FINGERPRINT,
            "百度密钥",
            "百度OCR服务的密钥",
            parent=self.ocrGroup
        )
        self.baiduSecretKeyEdit = LineEdit()
        self.baiduSecretKeyEdit.setText(self.cfg.get(self.cfg.baiduSecretKey))
        self.baiduSecretKeyEdit.setFixedWidth(200)  # 设置固定宽度
        self.baiduSecretKeyEdit.setContentsMargins(0, 0, 16, 0)  # 给输入框本身设置右边距
        self.baiduSecretKeyEdit.textChanged.connect(
            lambda text: self.cfg.set(self.cfg.baiduSecretKey, text)
        )
        self.baiduSecretKeyCard.hBoxLayout.addWidget(self.baiduSecretKeyEdit)
        
        # API设置组
        self.apiGroup = SettingCardGroup("API设置", self.scrollWidget)
        
        self.apiCaptureCard = SwitchSettingCard(
            FIF.SAVE,
            "API捕获启用",
            "启用API请求和响应的捕获功能",
            self.cfg.apiCaptureEnabled,
            parent=self.apiGroup
        )

        self.apiTimeoutCard = RangeSettingCard(
            self.cfg.apiTimeout,
            FIF.SPEED_OFF,
            "API超时时间",
            "API请求的超时时间（秒）",
            parent=self.apiGroup
        )

        # 网站设置组
        self.websiteGroup = SettingCardGroup("网站设置", self.scrollWidget)

        # 创建基础URL设置卡片
        self.baseUrlCard = SettingCard(
            FIF.LINK,
            "基础URL",
            "网站的基础地址",
            parent=self.websiteGroup
        )
        self.baseUrlEdit = LineEdit()
        self.baseUrlEdit.setText(self.cfg.get(self.cfg.baseUrl))
        self.baseUrlEdit.setFixedWidth(360)  # URL较长，设置更大宽度
        self.baseUrlEdit.setContentsMargins(0, 0, 20, 0)  # 给输入框本身设置右边距
        self.baseUrlEdit.textChanged.connect(
            lambda text: self.cfg.set(self.cfg.baseUrl, text)
        )
        self.baseUrlCard.hBoxLayout.addWidget(self.baseUrlEdit)

        # 创建登录URL设置卡片
        self.loginUrlCard = SettingCard(
            FIF.PEOPLE,
            "登录URL",
            "用户登录页面的地址",
            parent=self.websiteGroup
        )
        self.loginUrlEdit = LineEdit()
        self.loginUrlEdit.setText(self.cfg.get(self.cfg.loginUrl))
        self.loginUrlEdit.setFixedWidth(360)  # URL较长，设置更大宽度
        self.loginUrlEdit.setContentsMargins(0, 0, 20, 0)  # 给输入框本身设置右边距
        self.loginUrlEdit.textChanged.connect(
            lambda text: self.cfg.set(self.cfg.loginUrl, text)
        )
        self.loginUrlCard.hBoxLayout.addWidget(self.loginUrlEdit)

        # 创建学习URL设置卡片
        self.studyUrlCard = SettingCard(
            FIF.EDUCATION,
            "学习URL",
            "学习页面的地址",
            parent=self.websiteGroup
        )
        self.studyUrlEdit = LineEdit()
        self.studyUrlEdit.setText(self.cfg.get(self.cfg.studyUrl))
        self.studyUrlEdit.setFixedWidth(360)  # URL较长，设置更大宽度
        self.studyUrlEdit.setContentsMargins(0, 0, 20, 0)  # 给输入框本身设置右边距
        self.studyUrlEdit.textChanged.connect(
            lambda text: self.cfg.set(self.cfg.studyUrl, text)
        )
        self.studyUrlCard.hBoxLayout.addWidget(self.studyUrlEdit)

    def __initWidget(self):
        """初始化界面组件"""
        self.resize(1000, 800)
        self.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        self.setViewportMargins(0, 80, 0, 20)  # 关键：设置视口边距
        self.setWidget(self.scrollWidget)
        self.setWidgetResizable(True)
        self.setObjectName('studySettingInterface')

        # 设置样式
        self.scrollWidget.setObjectName('scrollWidget')
        self.settingLabel.setObjectName('settingLabel')

        # 设置标签位置
        self.settingLabel.move(36, 30)

        # 初始化布局
        self.__initLayout()

    def __initLayout(self):
        """初始化布局"""
        self.settingLabel.move(36, 30)

        # 将卡片添加到组中（按照官方脚手架方式）
        self.systemGroup.addSettingCard(self.maxConcurrentCard)
        self.systemGroup.addSettingCard(self.taskTimeoutCard)
        self.systemGroup.addSettingCard(self.retryCountCard)

        self.browserGroup.addSettingCard(self.browserTypeCard)
        self.browserGroup.addSettingCard(self.headlessCard)
        self.browserGroup.addSettingCard(self.pageTimeoutCard)

        self.ocrGroup.addSettingCard(self.primaryEngineCard)
        self.ocrGroup.addSettingCard(self.fallbackEngineCard)
        self.ocrGroup.addSettingCard(self.baiduApiKeyCard)
        self.ocrGroup.addSettingCard(self.baiduSecretKeyCard)

        self.apiGroup.addSettingCard(self.apiCaptureCard)
        self.apiGroup.addSettingCard(self.apiTimeoutCard)

        self.websiteGroup.addSettingCard(self.baseUrlCard)
        self.websiteGroup.addSettingCard(self.loginUrlCard)
        self.websiteGroup.addSettingCard(self.studyUrlCard)

        # 将设置卡片组添加到布局中
        self.expandLayout.setSpacing(28)
        self.expandLayout.setContentsMargins(36, 10, 36, 0)
        self.expandLayout.addWidget(self.systemGroup)
        self.expandLayout.addWidget(self.browserGroup)
        self.expandLayout.addWidget(self.ocrGroup)
        self.expandLayout.addWidget(self.apiGroup)
        self.expandLayout.addWidget(self.websiteGroup)

    def showInfoBar(self, message, type_="info"):
        """显示信息栏"""
        from qfluentwidgets import InfoBar
        if type_ == "success":
            InfoBar.success("", message, parent=self)
        elif type_ == "error":
            InfoBar.error("", message, parent=self)
        else:
            InfoBar.info("", message, parent=self)
