# coding:utf-8
"""
数据访问层(DAO)

该模块提供数据库操作的抽象接口，实现CRUD操作。
为各个数据模型提供标准化的数据访问方法。

主要功能：
- 用户数据CRUD操作
- 课程数据CRUD操作
- 日志数据CRUD操作
- API数据CRUD操作
- 批量操作支持

类说明：
- BaseDAO: 基础DAO类
- UserDAO: 用户数据访问对象
- CourseDAO: 课程数据访问对象
- LogDAO: 日志数据访问对象
- APIDataDAO: API数据访问对象
"""

from abc import ABC, abstractmethod
from typing import List, Optional, Dict, Any
from datetime import datetime

from .models import User, Course, Log, APIData
from .manager import db_manager


class BaseDAO(ABC):
    """基础DAO类"""
    
    def __init__(self):
        self.db_manager = db_manager
    
    @abstractmethod
    def create(self, obj: Any) -> bool:
        """创建记录"""
        pass
    
    @abstractmethod
    def get_by_id(self, obj_id: int) -> Optional[Any]:
        """根据ID获取记录"""
        pass
    
    @abstractmethod
    def update(self, obj: Any) -> bool:
        """更新记录"""
        pass
    
    @abstractmethod
    def delete(self, obj_id: int) -> bool:
        """删除记录"""
        pass
    
    @abstractmethod
    def get_all(self) -> List[Any]:
        """获取所有记录"""
        pass


class UserDAO(BaseDAO):
    """用户数据访问对象"""
    
    def create(self, user: User) -> bool:
        """创建用户"""
        try:
            sql = """
            INSERT INTO users (phone, password, name, status, complete_status,
                             online_total_credit, compulsory_credit, electives_credit,
                             last_login_time, error_message, progress)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            params = (
                user.phone, user.password, user.name, user.status, user.complete_status,
                user.online_total_credit, user.compulsory_credit, user.electives_credit,
                user.last_login_time, user.error_message, user.progress
            )
            
            with self.db_manager.get_transaction() as conn:
                conn.execute(sql, params)
            return True
            
        except Exception as e:
            print(f"[DAO] 创建用户失败: {e}")
            return False
    
    def get_by_id(self, user_id: int) -> Optional[User]:
        """根据ID获取用户"""
        try:
            sql = "SELECT * FROM users WHERE id = ?"
            with self.db_manager.get_connection() as conn:
                row = conn.execute(sql, (user_id,)).fetchone()
                if row:
                    return self._row_to_user(row)
            return None
        except Exception as e:
            print(f"[DAO] 获取用户失败: {e}")
            return None
    
    def get_by_phone(self, phone: str) -> Optional[User]:
        """根据手机号获取用户"""
        try:
            sql = "SELECT * FROM users WHERE phone = ?"
            with self.db_manager.get_connection() as conn:
                row = conn.execute(sql, (phone,)).fetchone()
                if row:
                    return self._row_to_user(row)
            return None
        except Exception as e:
            print(f"[DAO] 根据手机号获取用户失败: {e}")
            return None
    
    def update(self, user: User) -> bool:
        """更新用户"""
        try:
            sql = """
            UPDATE users SET password=?, name=?, status=?, complete_status=?,
                           online_total_credit=?, compulsory_credit=?, electives_credit=?,
                           last_login_time=?, error_message=?, progress=?, updated_at=CURRENT_TIMESTAMP
            WHERE id=?
            """
            params = (
                user.password, user.name, user.status, user.complete_status,
                user.online_total_credit, user.compulsory_credit, user.electives_credit,
                user.last_login_time, user.error_message, user.progress, user.id
            )
            
            with self.db_manager.get_transaction() as conn:
                conn.execute(sql, params)
            return True
            
        except Exception as e:
            print(f"[DAO] 更新用户失败: {e}")
            return False
    
    def update_status(self, phone: str, status: str, error_message: str = None) -> bool:
        """更新用户状态"""
        try:
            sql = "UPDATE users SET status=?, error_message=?, updated_at=CURRENT_TIMESTAMP WHERE phone=?"
            params = (status, error_message, phone)
            
            with self.db_manager.get_transaction() as conn:
                conn.execute(sql, params)
            return True
            
        except Exception as e:
            print(f"[DAO] 更新用户状态失败: {e}")
            return False
    
    def update_from_api(self, phone: str, api_data: dict) -> bool:
        """从API数据更新用户信息"""
        try:
            sql = """
            UPDATE users SET online_total_credit=?, compulsory_credit=?, electives_credit=?,
                           complete_status=?, updated_at=CURRENT_TIMESTAMP
            WHERE phone=?
            """
            params = (
                float(api_data.get('onlineTotalCredit', 0)),
                float(api_data.get('compulsoryCredit', 0)),
                float(api_data.get('electivesCredit', 0)),
                api_data.get('completeStatus', '1'),
                phone
            )
            
            with self.db_manager.get_transaction() as conn:
                conn.execute(sql, params)
            return True
            
        except Exception as e:
            print(f"[DAO] 从API数据更新用户失败: {e}")
            return False
    
    def delete(self, user_id: int) -> bool:
        """删除用户"""
        try:
            sql = "DELETE FROM users WHERE id = ?"
            with self.db_manager.get_transaction() as conn:
                conn.execute(sql, (user_id,))
            return True
        except Exception as e:
            print(f"[DAO] 删除用户失败: {e}")
            return False
    
    def get_all(self) -> List[User]:
        """获取所有用户"""
        try:
            sql = "SELECT * FROM users ORDER BY created_at DESC"
            with self.db_manager.get_connection() as conn:
                rows = conn.execute(sql).fetchall()
                return [self._row_to_user(row) for row in rows]
        except Exception as e:
            print(f"[DAO] 获取所有用户失败: {e}")
            return []
    
    def get_by_status(self, status: str) -> List[User]:
        """根据状态获取用户"""
        try:
            sql = "SELECT * FROM users WHERE status = ? ORDER BY created_at DESC"
            with self.db_manager.get_connection() as conn:
                rows = conn.execute(sql, (status,)).fetchall()
                return [self._row_to_user(row) for row in rows]
        except Exception as e:
            print(f"[DAO] 根据状态获取用户失败: {e}")
            return []
    
    def batch_create(self, users: List[User]) -> bool:
        """批量创建用户"""
        try:
            sql = """
            INSERT OR IGNORE INTO users (phone, password, name, status, complete_status,
                                       online_total_credit, compulsory_credit, electives_credit,
                                       last_login_time, error_message, progress)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            with self.db_manager.get_transaction() as conn:
                for user in users:
                    params = (
                        user.phone, user.password, user.name, user.status, user.complete_status,
                        user.online_total_credit, user.compulsory_credit, user.electives_credit,
                        user.last_login_time, user.error_message, user.progress
                    )
                    conn.execute(sql, params)
            return True
            
        except Exception as e:
            print(f"[DAO] 批量创建用户失败: {e}")
            return False
    
    def _row_to_user(self, row) -> User:
        """将数据库行转换为User对象"""
        return User(
            id=row["id"],
            phone=row["phone"],
            password=row["password"],
            name=row["name"],
            status=row["status"],
            complete_status=row["complete_status"],
            online_total_credit=row["online_total_credit"],
            compulsory_credit=row["compulsory_credit"],
            electives_credit=row["electives_credit"],
            last_login_time=datetime.fromisoformat(row["last_login_time"]) if row["last_login_time"] else None,
            error_message=row["error_message"],
            progress=row["progress"],
            created_at=datetime.fromisoformat(row["created_at"]) if row["created_at"] else None,
            updated_at=datetime.fromisoformat(row["updated_at"]) if row["updated_at"] else None
        )


class CourseDAO(BaseDAO):
    """课程数据访问对象"""

    def create(self, course: Course) -> bool:
        """创建课程"""
        try:
            sql = """
            INSERT INTO courses (user_phone, course_type, name, course_id, completed,
                               credit, percentage, courseware_id, video_url, completed_date)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            params = (
                course.user_phone, course.course_type, course.name, course.course_id,
                course.completed, course.credit, course.percentage, course.courseware_id,
                course.video_url, course.completed_date
            )

            with self.db_manager.get_transaction() as conn:
                conn.execute(sql, params)
            return True

        except Exception as e:
            print(f"[DAO] 创建课程失败: {e}")
            return False

    def get_by_id(self, course_id: int) -> Optional[Course]:
        """根据ID获取课程"""
        try:
            sql = "SELECT * FROM courses WHERE id = ?"
            with self.db_manager.get_connection() as conn:
                row = conn.execute(sql, (course_id,)).fetchone()
                if row:
                    return self._row_to_course(row)
            return None
        except Exception as e:
            print(f"[DAO] 获取课程失败: {e}")
            return None

    def get_by_user_phone(self, user_phone: str) -> List[Course]:
        """根据用户手机号获取课程"""
        try:
            sql = "SELECT * FROM courses WHERE user_phone = ? ORDER BY created_at DESC"
            with self.db_manager.get_connection() as conn:
                rows = conn.execute(sql, (user_phone,)).fetchall()
                return [self._row_to_course(row) for row in rows]
        except Exception as e:
            print(f"[DAO] 根据用户获取课程失败: {e}")
            return []

    def update(self, course: Course) -> bool:
        """更新课程"""
        try:
            sql = """
            UPDATE courses SET user_phone=?, course_type=?, name=?, course_id=?,
                             completed=?, credit=?, percentage=?, courseware_id=?,
                             video_url=?, completed_date=?, updated_at=CURRENT_TIMESTAMP
            WHERE id=?
            """
            params = (
                course.user_phone, course.course_type, course.name, course.course_id,
                course.completed, course.credit, course.percentage, course.courseware_id,
                course.video_url, course.completed_date, course.id
            )

            with self.db_manager.get_transaction() as conn:
                conn.execute(sql, params)
            return True

        except Exception as e:
            print(f"[DAO] 更新课程失败: {e}")
            return False

    def delete(self, course_id: int) -> bool:
        """删除课程"""
        try:
            sql = "DELETE FROM courses WHERE id = ?"
            with self.db_manager.get_transaction() as conn:
                conn.execute(sql, (course_id,))
            return True
        except Exception as e:
            print(f"[DAO] 删除课程失败: {e}")
            return False

    def get_all(self) -> List[Course]:
        """获取所有课程"""
        try:
            sql = "SELECT * FROM courses ORDER BY created_at DESC"
            with self.db_manager.get_connection() as conn:
                rows = conn.execute(sql).fetchall()
                return [self._row_to_course(row) for row in rows]
        except Exception as e:
            print(f"[DAO] 获取所有课程失败: {e}")
            return []

    def _row_to_course(self, row) -> Course:
        """将数据库行转换为Course对象"""
        return Course(
            id=row["id"],
            user_phone=row["user_phone"],
            course_type=row["course_type"],
            name=row["name"],
            course_id=row["course_id"],
            completed=row["completed"],
            credit=row["credit"],
            percentage=row["percentage"],
            courseware_id=row["courseware_id"],
            video_url=row["video_url"],
            completed_date=datetime.fromisoformat(row["completed_date"]) if row["completed_date"] else None,
            created_at=datetime.fromisoformat(row["created_at"]) if row["created_at"] else None,
            updated_at=datetime.fromisoformat(row["updated_at"]) if row["updated_at"] else None
        )


class LogDAO(BaseDAO):
    """日志数据访问对象"""

    def create(self, log_data: Dict[str, Any]) -> bool:
        """创建日志"""
        try:
            # 支持字典和Log对象两种输入
            if isinstance(log_data, dict):
                sql = "INSERT INTO logs (level, message, module, user_phone, created_at) VALUES (?, ?, ?, ?, ?)"
                params = (
                    log_data.get('level', 'INFO'),
                    log_data.get('message', ''),
                    log_data.get('module', 'unknown'),
                    log_data.get('user_phone', 'system'),
                    log_data.get('timestamp', datetime.now()).isoformat()
                )
            else:
                # Log对象
                sql = "INSERT INTO logs (level, message, module, user_phone) VALUES (?, ?, ?, ?)"
                params = (log_data.level, log_data.message, log_data.module, log_data.user_phone)

            with self.db_manager.get_transaction() as conn:
                conn.execute(sql, params)
            return True

        except Exception as e:
            print(f"[DAO] 创建日志失败: {e}")
            return False

    def get_by_id(self, log_id: int) -> Optional[Log]:
        """根据ID获取日志"""
        try:
            sql = "SELECT * FROM logs WHERE id = ?"
            with self.db_manager.get_connection() as conn:
                row = conn.execute(sql, (log_id,)).fetchone()
                if row:
                    return self._row_to_log(row)
            return None
        except Exception as e:
            print(f"[DAO] 获取日志失败: {e}")
            return None

    def update(self, log: Log) -> bool:
        """更新日志（通常不需要）"""
        return False

    def delete(self, log_id: int) -> bool:
        """删除日志"""
        try:
            sql = "DELETE FROM logs WHERE id = ?"
            with self.db_manager.get_transaction() as conn:
                conn.execute(sql, (log_id,))
            return True
        except Exception as e:
            print(f"[DAO] 删除日志失败: {e}")
            return False

    def get_all(self) -> List[Log]:
        """获取所有日志"""
        try:
            sql = "SELECT * FROM logs ORDER BY created_at DESC LIMIT 1000"
            with self.db_manager.get_connection() as conn:
                rows = conn.execute(sql).fetchall()
                return [self._row_to_log(row) for row in rows]
        except Exception as e:
            print(f"[DAO] 获取所有日志失败: {e}")
            return []

    def get_by_level(self, level: str, limit: int = 1000) -> List[Log]:
        """根据级别获取日志"""
        try:
            sql = "SELECT * FROM logs WHERE level = ? ORDER BY created_at DESC LIMIT ?"
            with self.db_manager.get_connection() as conn:
                rows = conn.execute(sql, (level, limit)).fetchall()
                return [self._row_to_log(row) for row in rows]
        except Exception as e:
            print(f"[DAO] 根据级别获取日志失败: {e}")
            return []

    def get_by_user_phone(self, user_phone: str, limit: int = 1000) -> List[Log]:
        """根据用户手机号获取日志"""
        try:
            sql = "SELECT * FROM logs WHERE user_phone = ? ORDER BY created_at DESC LIMIT ?"
            with self.db_manager.get_connection() as conn:
                rows = conn.execute(sql, (user_phone, limit)).fetchall()
                return [self._row_to_log(row) for row in rows]
        except Exception as e:
            print(f"[DAO] 根据用户获取日志失败: {e}")
            return []

    def get_logs(self, conditions: Dict[str, Any], limit: int = 1000) -> List[Dict[str, Any]]:
        """根据条件获取日志"""
        try:
            # 构建SQL查询
            sql_parts = ["SELECT * FROM logs WHERE 1=1"]
            params = []

            # 添加条件
            if 'level' in conditions:
                sql_parts.append("AND level = ?")
                params.append(conditions['level'])

            if 'user_phone' in conditions:
                sql_parts.append("AND user_phone = ?")
                params.append(conditions['user_phone'])

            if 'module' in conditions:
                sql_parts.append("AND module = ?")
                params.append(conditions['module'])

            if 'start_time' in conditions:
                sql_parts.append("AND created_at >= ?")
                params.append(conditions['start_time'].isoformat())

            if 'end_time' in conditions:
                sql_parts.append("AND created_at <= ?")
                params.append(conditions['end_time'].isoformat())

            # 排序和限制
            sql_parts.append("ORDER BY created_at DESC LIMIT ?")
            params.append(limit)

            sql = " ".join(sql_parts)

            with self.db_manager.get_connection() as conn:
                rows = conn.execute(sql, params).fetchall()

                # 转换为字典格式
                logs = []
                for row in rows:
                    log_dict = {
                        'id': row['id'],
                        'level': row['level'],
                        'message': row['message'],
                        'module': row['module'],
                        'user_phone': row['user_phone'],
                        'timestamp': datetime.fromisoformat(row['created_at']) if row['created_at'] else None
                    }
                    logs.append(log_dict)

                return logs

        except Exception as e:
            print(f"[DAO] 获取日志失败: {e}")
            return []

    def delete_logs(self, conditions: Dict[str, Any]) -> bool:
        """根据条件删除日志"""
        try:
            # 构建SQL删除语句
            sql_parts = ["DELETE FROM logs WHERE 1=1"]
            params = []

            # 添加条件
            if 'older_than' in conditions:
                sql_parts.append("AND created_at < ?")
                params.append(conditions['older_than'].isoformat())

            if 'level' in conditions:
                sql_parts.append("AND level = ?")
                params.append(conditions['level'])

            if 'user_phone' in conditions:
                sql_parts.append("AND user_phone = ?")
                params.append(conditions['user_phone'])

            sql = " ".join(sql_parts)

            with self.db_manager.get_transaction() as conn:
                cursor = conn.execute(sql, params)
                deleted_count = cursor.rowcount
                print(f"[DAO] 已删除 {deleted_count} 条日志")

            return True

        except Exception as e:
            print(f"[DAO] 删除日志失败: {e}")
            return False

    def get_stats(self) -> Dict[str, Any]:
        """获取日志统计信息"""
        try:
            stats = {}

            with self.db_manager.get_connection() as conn:
                # 总数统计
                total_count = conn.execute("SELECT COUNT(*) FROM logs").fetchone()[0]
                stats['total_count'] = total_count

                # 按级别统计
                level_stats = conn.execute("""
                    SELECT level, COUNT(*) as count
                    FROM logs
                    GROUP BY level
                """).fetchall()
                stats['level_counts'] = {row['level']: row['count'] for row in level_stats}

                # 按模块统计
                module_stats = conn.execute("""
                    SELECT module, COUNT(*) as count
                    FROM logs
                    GROUP BY module
                    ORDER BY count DESC
                    LIMIT 10
                """).fetchall()
                stats['module_counts'] = {row['module']: row['count'] for row in module_stats}

                # 按用户统计
                user_stats = conn.execute("""
                    SELECT user_phone, COUNT(*) as count
                    FROM logs
                    GROUP BY user_phone
                    ORDER BY count DESC
                    LIMIT 10
                """).fetchall()
                stats['user_counts'] = {row['user_phone']: row['count'] for row in user_stats}

                # 时间范围统计
                time_range = conn.execute("""
                    SELECT
                        MIN(created_at) as earliest,
                        MAX(created_at) as latest
                    FROM logs
                """).fetchone()

                if time_range['earliest']:
                    stats['earliest_log'] = time_range['earliest']
                    stats['latest_log'] = time_range['latest']

            return stats

        except Exception as e:
            print(f"[DAO] 获取日志统计失败: {e}")
            return {}

    def clean_old_logs(self, days: int = 30) -> bool:
        """清理旧日志"""
        try:
            # 计算截止时间
            from datetime import datetime, timedelta
            cutoff_time = datetime.now() - timedelta(days=days)
            cutoff_iso = cutoff_time.isoformat()

            # 使用ISO格式时间进行比较
            sql = "DELETE FROM logs WHERE created_at < ?"
            with self.db_manager.get_transaction() as conn:
                cursor = conn.execute(sql, (cutoff_iso,))
                deleted_count = cursor.rowcount
                print(f"[DAO] 已清理 {deleted_count} 条旧日志")
            return True
        except Exception as e:
            print(f"[DAO] 清理旧日志失败: {e}")
            return False

    def _row_to_log(self, row) -> Log:
        """将数据库行转换为Log对象"""
        return Log(
            id=row["id"],
            level=row["level"],
            message=row["message"],
            module=row["module"],
            user_phone=row["user_phone"],
            created_at=datetime.fromisoformat(row["created_at"]) if row["created_at"] else None
        )


class APIDataDAO(BaseDAO):
    """API数据访问对象"""

    def create(self, api_data: APIData) -> bool:
        """创建API数据"""
        try:
            sql = """
            INSERT INTO api_data (user_phone, api_url, request_method, response_status,
                                response_data, capture_time, is_processed, error_message)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """
            params = (
                api_data.user_phone, api_data.api_url, api_data.request_method,
                api_data.response_status, api_data.response_data, api_data.capture_time,
                api_data.is_processed, api_data.error_message
            )

            with self.db_manager.get_transaction() as conn:
                conn.execute(sql, params)
            return True

        except Exception as e:
            print(f"[DAO] 创建API数据失败: {e}")
            return False

    def get_by_id(self, api_data_id: int) -> Optional[APIData]:
        """根据ID获取API数据"""
        try:
            sql = "SELECT * FROM api_data WHERE id = ?"
            with self.db_manager.get_connection() as conn:
                row = conn.execute(sql, (api_data_id,)).fetchone()
                if row:
                    return self._row_to_api_data(row)
            return None
        except Exception as e:
            print(f"[DAO] 获取API数据失败: {e}")
            return None

    def update(self, api_data: APIData) -> bool:
        """更新API数据"""
        try:
            sql = """
            UPDATE api_data SET user_phone=?, api_url=?, request_method=?,
                              response_status=?, response_data=?, capture_time=?,
                              is_processed=?, error_message=?
            WHERE id=?
            """
            params = (
                api_data.user_phone, api_data.api_url, api_data.request_method,
                api_data.response_status, api_data.response_data, api_data.capture_time,
                api_data.is_processed, api_data.error_message, api_data.id
            )

            with self.db_manager.get_transaction() as conn:
                conn.execute(sql, params)
            return True

        except Exception as e:
            print(f"[DAO] 更新API数据失败: {e}")
            return False

    def mark_as_processed(self, api_data_id: int) -> bool:
        """标记API数据为已处理"""
        try:
            sql = "UPDATE api_data SET is_processed = TRUE WHERE id = ?"
            with self.db_manager.get_transaction() as conn:
                conn.execute(sql, (api_data_id,))
            return True
        except Exception as e:
            print(f"[DAO] 标记API数据为已处理失败: {e}")
            return False

    def delete(self, api_data_id: int) -> bool:
        """删除API数据"""
        try:
            sql = "DELETE FROM api_data WHERE id = ?"
            with self.db_manager.get_transaction() as conn:
                conn.execute(sql, (api_data_id,))
            return True
        except Exception as e:
            print(f"[DAO] 删除API数据失败: {e}")
            return False

    def get_all(self) -> List[APIData]:
        """获取所有API数据"""
        try:
            sql = "SELECT * FROM api_data ORDER BY capture_time DESC LIMIT 1000"
            with self.db_manager.get_connection() as conn:
                rows = conn.execute(sql).fetchall()
                return [self._row_to_api_data(row) for row in rows]
        except Exception as e:
            print(f"[DAO] 获取所有API数据失败: {e}")
            return []

    def get_unprocessed(self) -> List[APIData]:
        """获取未处理的API数据"""
        try:
            sql = "SELECT * FROM api_data WHERE is_processed = FALSE ORDER BY capture_time ASC"
            with self.db_manager.get_connection() as conn:
                rows = conn.execute(sql).fetchall()
                return [self._row_to_api_data(row) for row in rows]
        except Exception as e:
            print(f"[DAO] 获取未处理API数据失败: {e}")
            return []

    def _row_to_api_data(self, row) -> APIData:
        """将数据库行转换为APIData对象"""
        return APIData(
            id=row["id"],
            user_phone=row["user_phone"],
            api_url=row["api_url"],
            request_method=row["request_method"],
            response_status=row["response_status"],
            response_data=row["response_data"],
            capture_time=datetime.fromisoformat(row["capture_time"]) if row["capture_time"] else None,
            is_processed=bool(row["is_processed"]),
            error_message=row["error_message"]
        )


# DAO工厂类
class DAOFactory:
    """DAO工厂类"""

    _instances = {}

    @classmethod
    def get_user_dao(cls) -> UserDAO:
        """获取用户DAO实例"""
        if 'user' not in cls._instances:
            cls._instances['user'] = UserDAO()
        return cls._instances['user']

    @classmethod
    def get_course_dao(cls) -> CourseDAO:
        """获取课程DAO实例"""
        if 'course' not in cls._instances:
            cls._instances['course'] = CourseDAO()
        return cls._instances['course']

    @classmethod
    def get_log_dao(cls) -> LogDAO:
        """获取日志DAO实例"""
        if 'log' not in cls._instances:
            cls._instances['log'] = LogDAO()
        return cls._instances['log']

    @classmethod
    def get_api_data_dao(cls) -> APIDataDAO:
        """获取API数据DAO实例"""
        if 'api_data' not in cls._instances:
            cls._instances['api_data'] = APIDataDAO()
        return cls._instances['api_data']
