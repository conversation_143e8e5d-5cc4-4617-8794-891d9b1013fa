# coding:utf-8
"""
日志系统单元测试

测试日志管理器、日志查看器和日志导出器的功能。

测试内容：
- 日志记录
- 日志级别过滤
- 日志格式化
- 日志文件轮转
- 日志查看
- 日志导出
- 日志统计
"""

import pytest
import tempfile
import json
from pathlib import Path
from datetime import datetime, timedelta
from unittest.mock import patch, MagicMock

from app.xueyuan.logging.manager import LogManager
from app.xueyuan.logging.viewer import LogViewer
from app.xueyuan.logging.exporter import LogExporter


class TestLogManager:
    """日志管理器测试"""
    
    def test_log_manager_initialization(self, temp_dir):
        """测试日志管理器初始化"""
        log_dir = temp_dir / "logs"
        manager = LogManager()
        manager.log_dir = str(log_dir)
        
        # 测试初始化
        manager._setup_file_handler()
        
        # 验证日志目录创建
        assert log_dir.exists()
        
        # 验证日志文件创建
        log_files = list(log_dir.glob("*.log"))
        assert len(log_files) > 0
    
    def test_log_levels(self, log_manager):
        """测试日志级别"""
        # 测试不同级别的日志
        log_manager.debug("调试信息", module="TestModule")
        log_manager.info("信息日志", module="TestModule")
        log_manager.warning("警告日志", module="TestModule")
        log_manager.error("错误日志", module="TestModule")
        log_manager.critical("严重错误", module="TestModule")
        
        # 验证日志记录（这里需要检查实际的日志输出）
        # 由于日志是异步的，可能需要等待一段时间
        import time
        time.sleep(0.1)
    
    def test_log_with_user_context(self, log_manager):
        """测试带用户上下文的日志"""
        log_manager.info(
            "用户登录成功",
            module="UserModule",
            user_phone="13800000001",
            extra_data={"login_time": datetime.now().isoformat()}
        )
        
        log_manager.error(
            "用户登录失败",
            module="UserModule", 
            user_phone="13800000002",
            extra_data={"error_code": "INVALID_PASSWORD"}
        )
    
    def test_log_formatting(self, log_manager):
        """测试日志格式化"""
        # 测试带额外数据的日志
        extra_data = {
            "request_id": "req_123456",
            "duration": 1.5,
            "status": "success"
        }
        
        log_manager.info(
            "API请求完成",
            module="APIModule",
            user_phone="13800000001",
            extra_data=extra_data
        )
    
    def test_log_file_rotation(self, temp_dir):
        """测试日志文件轮转"""
        log_dir = temp_dir / "logs"
        manager = LogManager()
        manager.log_dir = str(log_dir)
        manager.max_file_size = 1024  # 1KB，便于测试
        
        # 生成大量日志以触发轮转
        for i in range(100):
            manager.info(f"测试日志消息 {i} " + "x" * 50, module="TestModule")
        
        # 等待日志写入
        import time
        time.sleep(0.5)
        
        # 检查是否有多个日志文件
        log_files = list(log_dir.glob("*.log*"))
        # 注意：实际的轮转行为取决于具体实现
    
    def test_database_logging(self, test_db, log_manager):
        """测试数据库日志记录"""
        # 启用数据库日志
        log_manager.enable_database_log = True
        
        # 记录日志
        log_manager.info("数据库测试日志", module="TestModule", user_phone="13800000001")
        log_manager.error("数据库错误日志", module="TestModule", user_phone="13800000002")
        
        # 等待日志写入
        import time
        time.sleep(0.1)
        
        # 验证数据库中的日志
        from app.xueyuan.database.dao import LogDAO
        log_dao = LogDAO()
        
        logs = log_dao.get_logs({}, limit=10)
        assert len(logs) >= 2
        
        # 验证日志内容
        log_messages = [log["message"] for log in logs]
        assert "数据库测试日志" in log_messages
        assert "数据库错误日志" in log_messages
    
    def test_log_filtering(self, log_manager):
        """测试日志过滤"""
        # 设置日志级别为WARNING
        log_manager.set_level("WARNING")
        
        # 记录不同级别的日志
        log_manager.debug("调试信息")  # 应该被过滤
        log_manager.info("信息日志")   # 应该被过滤
        log_manager.warning("警告日志")  # 应该记录
        log_manager.error("错误日志")    # 应该记录
        
        # 验证过滤效果（需要检查实际输出）
    
    def test_concurrent_logging(self, log_manager):
        """测试并发日志记录"""
        import threading
        import time
        
        def log_worker(worker_id):
            for i in range(10):
                log_manager.info(
                    f"工作线程 {worker_id} 日志 {i}",
                    module="ConcurrentTest",
                    extra_data={"worker_id": worker_id, "iteration": i}
                )
                time.sleep(0.01)
        
        # 创建多个线程同时记录日志
        threads = []
        for i in range(5):
            thread = threading.Thread(target=log_worker, args=(i,))
            threads.append(thread)
        
        # 启动所有线程
        for thread in threads:
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 验证日志完整性（所有日志都应该被记录）


class TestLogViewer:
    """日志查看器测试"""
    
    def test_log_viewer_initialization(self, populated_test_db):
        """测试日志查看器初始化"""
        viewer = LogViewer()
        
        # 测试界面组件是否正确创建
        assert viewer.log_table is not None
        assert viewer.filter_widget is not None
        assert viewer.search_widget is not None
    
    def test_load_logs(self, populated_test_db):
        """测试加载日志"""
        viewer = LogViewer()
        
        # 加载日志
        viewer.load_logs()
        
        # 验证日志是否加载到表格中
        row_count = viewer.log_table.rowCount()
        assert row_count > 0
    
    def test_filter_logs_by_level(self, populated_test_db):
        """测试按级别过滤日志"""
        viewer = LogViewer()
        viewer.load_logs()
        
        # 设置级别过滤
        viewer.set_level_filter("ERROR")
        
        # 验证过滤结果
        # 这里需要检查表格中只显示ERROR级别的日志
    
    def test_filter_logs_by_time_range(self, populated_test_db):
        """测试按时间范围过滤日志"""
        viewer = LogViewer()
        viewer.load_logs()
        
        # 设置时间范围过滤
        start_time = datetime.now() - timedelta(hours=2)
        end_time = datetime.now()
        
        viewer.set_time_filter(start_time, end_time)
        
        # 验证过滤结果
    
    def test_search_logs(self, populated_test_db):
        """测试搜索日志"""
        viewer = LogViewer()
        viewer.load_logs()
        
        # 搜索包含特定关键词的日志
        viewer.search_logs("测试")
        
        # 验证搜索结果
    
    def test_export_filtered_logs(self, populated_test_db, temp_dir):
        """测试导出过滤后的日志"""
        viewer = LogViewer()
        viewer.load_logs()
        
        # 应用过滤
        viewer.set_level_filter("INFO")
        
        # 导出日志
        export_file = temp_dir / "filtered_logs.xlsx"
        result = viewer.export_logs(str(export_file), format="excel")
        
        assert result is True
        assert export_file.exists()
    
    def test_real_time_log_updates(self, log_manager):
        """测试实时日志更新"""
        viewer = LogViewer()
        viewer.load_logs()
        
        initial_count = viewer.log_table.rowCount()
        
        # 记录新日志
        log_manager.info("新的测试日志", module="TestModule")
        
        # 等待更新
        import time
        time.sleep(0.5)
        
        # 刷新查看器
        viewer.refresh()
        
        # 验证新日志是否显示
        new_count = viewer.log_table.rowCount()
        assert new_count > initial_count


class TestLogExporter:
    """日志导出器测试"""
    
    def test_export_to_excel(self, populated_test_db, temp_dir):
        """测试导出到Excel"""
        exporter = LogExporter()
        
        export_file = temp_dir / "logs.xlsx"
        result = exporter.export_logs(
            filename=str(export_file),
            format="excel"
        )
        
        assert result is True
        assert export_file.exists()
        
        # 验证文件大小
        assert export_file.stat().st_size > 0
    
    def test_export_to_csv(self, populated_test_db, temp_dir):
        """测试导出到CSV"""
        exporter = LogExporter()
        
        export_file = temp_dir / "logs.csv"
        result = exporter.export_logs(
            filename=str(export_file),
            format="csv"
        )
        
        assert result is True
        assert export_file.exists()
        
        # 验证CSV内容
        import csv
        with open(export_file, 'r', encoding='utf-8') as f:
            reader = csv.reader(f)
            rows = list(reader)
            
            # 应该有标题行和数据行
            assert len(rows) > 1
            
            # 验证标题行
            headers = rows[0]
            expected_headers = ["时间", "级别", "模块", "用户", "消息"]
            for header in expected_headers:
                assert header in headers
    
    def test_export_to_json(self, populated_test_db, temp_dir):
        """测试导出到JSON"""
        exporter = LogExporter()
        
        export_file = temp_dir / "logs.json"
        result = exporter.export_logs(
            filename=str(export_file),
            format="json"
        )
        
        assert result is True
        assert export_file.exists()
        
        # 验证JSON内容
        with open(export_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
            
            assert isinstance(data, list)
            assert len(data) > 0
            
            # 验证数据结构
            log_entry = data[0]
            required_fields = ["timestamp", "level", "module", "user_phone", "message"]
            for field in required_fields:
                assert field in log_entry
    
    def test_export_with_filters(self, populated_test_db, temp_dir):
        """测试带过滤条件的导出"""
        exporter = LogExporter()
        
        # 设置过滤条件
        filters = {
            "level": "ERROR",
            "start_time": datetime.now() - timedelta(hours=3),
            "end_time": datetime.now()
        }
        
        export_file = temp_dir / "filtered_logs.xlsx"
        result = exporter.export_logs(
            filename=str(export_file),
            format="excel",
            filters=filters
        )
        
        assert result is True
        assert export_file.exists()
    
    def test_export_specific_fields(self, populated_test_db, temp_dir):
        """测试导出指定字段"""
        exporter = LogExporter()
        
        # 只导出特定字段
        fields = ["timestamp", "level", "message"]
        
        export_file = temp_dir / "partial_logs.csv"
        result = exporter.export_logs(
            filename=str(export_file),
            format="csv",
            fields=fields
        )
        
        assert result is True
        assert export_file.exists()
        
        # 验证只包含指定字段
        import csv
        with open(export_file, 'r', encoding='utf-8') as f:
            reader = csv.reader(f)
            headers = next(reader)
            
            # 验证标题行只包含指定字段
            assert len(headers) == len(fields)
            for field in fields:
                # 字段名可能经过翻译，这里需要根据实际情况调整
                pass
    
    def test_export_with_compression(self, populated_test_db, temp_dir):
        """测试压缩导出"""
        exporter = LogExporter()
        
        export_file = temp_dir / "logs.xlsx"
        result = exporter.export_logs(
            filename=str(export_file),
            format="excel",
            compress=True
        )
        
        assert result is True
        
        # 如果启用压缩，应该生成.zip文件
        zip_file = temp_dir / "logs.xlsx.zip"
        if zip_file.exists():
            assert zip_file.stat().st_size > 0
    
    def test_export_large_dataset(self, test_db, temp_dir):
        """测试导出大数据集"""
        from app.xueyuan.database.dao import LogDAO
        
        # 创建大量测试日志
        log_dao = LogDAO()
        for i in range(1000):
            log_data = {
                "level": "INFO",
                "message": f"大数据集测试日志 {i}",
                "module": "TestModule",
                "user_phone": f"138000{i:05d}",
                "timestamp": datetime.now() - timedelta(seconds=i)
            }
            log_dao.create(log_data)
        
        # 导出大数据集
        exporter = LogExporter()
        export_file = temp_dir / "large_logs.xlsx"
        
        result = exporter.export_logs(
            filename=str(export_file),
            format="excel"
        )
        
        assert result is True
        assert export_file.exists()
        assert export_file.stat().st_size > 10000  # 应该是一个相当大的文件
    
    def test_export_error_handling(self, populated_test_db, temp_dir):
        """测试导出错误处理"""
        exporter = LogExporter()
        
        # 测试无效路径
        invalid_path = "/invalid/path/logs.xlsx"
        result = exporter.export_logs(
            filename=invalid_path,
            format="excel"
        )
        assert result is False
        
        # 测试无效格式
        export_file = temp_dir / "logs.invalid"
        result = exporter.export_logs(
            filename=str(export_file),
            format="invalid_format"
        )
        assert result is False


@pytest.mark.unit
class TestLoggingIntegration:
    """日志系统集成测试"""
    
    def test_end_to_end_logging_workflow(self, test_db, temp_dir):
        """测试端到端日志工作流"""
        # 1. 初始化日志管理器
        log_dir = temp_dir / "logs"
        manager = LogManager()
        manager.log_dir = str(log_dir)
        
        # 2. 记录各种类型的日志
        manager.info("用户登录", module="AuthModule", user_phone="13800000001")
        manager.warning("密码错误", module="AuthModule", user_phone="13800000002")
        manager.error("系统异常", module="SystemModule")
        
        # 等待日志写入
        import time
        time.sleep(0.2)
        
        # 3. 使用查看器查看日志
        viewer = LogViewer()
        viewer.load_logs()
        
        # 验证日志已加载
        assert viewer.log_table.rowCount() >= 3
        
        # 4. 应用过滤
        viewer.set_level_filter("ERROR")
        
        # 5. 导出过滤后的日志
        exporter = LogExporter()
        export_file = temp_dir / "error_logs.xlsx"
        
        result = exporter.export_logs(
            filename=str(export_file),
            format="excel",
            filters={"level": "ERROR"}
        )
        
        assert result is True
        assert export_file.exists()
    
    def test_logging_performance(self, log_manager):
        """测试日志性能"""
        import time
        
        # 记录开始时间
        start_time = time.time()
        
        # 记录大量日志
        for i in range(1000):
            log_manager.info(f"性能测试日志 {i}", module="PerformanceTest")
        
        # 记录结束时间
        end_time = time.time()
        
        # 验证性能（1000条日志应该在合理时间内完成）
        duration = end_time - start_time
        assert duration < 5.0  # 应该在5秒内完成
        
        print(f"记录1000条日志耗时: {duration:.2f}秒")
