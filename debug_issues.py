#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试问题脚本
"""

import asyncio
import sys
import os
from pathlib import Path
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.xueyuan.database.dao import DAOFactory
from app.xueyuan.database.manager import db_manager
from app.xueyuan.automation.engine import study_engine


def debug_log_cleanup():
    """调试日志清理问题"""
    print("=" * 50)
    print("调试日志清理问题")
    print("=" * 50)
    
    try:
        log_dao = DAOFactory.get_log_dao()
        
        # 1. 检查数据库连接
        print("1. 检查数据库连接...")
        with db_manager.get_connection() as conn:
            print("   ✓ 数据库连接正常")
            
            # 2. 检查logs表结构
            print("2. 检查logs表结构...")
            cursor = conn.execute("PRAGMA table_info(logs)")
            columns = cursor.fetchall()
            print("   表结构:")
            for col in columns:
                print(f"     - {col['name']}: {col['type']} (默认: {col['dflt_value']})")
            
            # 3. 检查现有日志数据
            print("3. 检查现有日志数据...")
            cursor = conn.execute("SELECT COUNT(*) as count FROM logs")
            total_count = cursor.fetchone()['count']
            print(f"   总日志数量: {total_count}")
            
            # 4. 检查日志的created_at字段格式
            print("4. 检查日志的created_at字段格式...")
            cursor = conn.execute("SELECT created_at FROM logs ORDER BY id DESC LIMIT 5")
            recent_logs = cursor.fetchall()
            print("   最近5条日志的时间戳:")
            for i, log in enumerate(recent_logs):
                print(f"     {i+1}. {log['created_at']}")
            
            # 5. 测试时间比较查询
            print("5. 测试时间比较查询...")
            test_date = datetime.now() - timedelta(days=30)
            print(f"   30天前的时间: {test_date}")
            
            # 测试不同的时间格式查询
            queries = [
                "SELECT COUNT(*) as count FROM logs WHERE created_at < datetime('now', '-30 days')",
                "SELECT COUNT(*) as count FROM logs WHERE created_at < datetime('now', '-1 days')",
                "SELECT COUNT(*) as count FROM logs WHERE created_at < datetime('now', '-0 days')"
            ]
            
            for query in queries:
                try:
                    cursor = conn.execute(query)
                    count = cursor.fetchone()['count']
                    print(f"   查询: {query}")
                    print(f"   结果: {count} 条记录")
                except Exception as e:
                    print(f"   查询失败: {e}")
            
            # 6. 创建一些旧日志用于测试
            print("6. 创建测试旧日志...")
            old_date = (datetime.now() - timedelta(days=35)).isoformat()
            test_log_sql = "INSERT INTO logs (level, message, module, user_phone, created_at) VALUES (?, ?, ?, ?, ?)"
            conn.execute(test_log_sql, ('INFO', '测试旧日志', 'test', 'test', old_date))
            conn.commit()
            print("   ✓ 已创建测试旧日志")
            
            # 7. 再次测试清理查询
            print("7. 测试清理查询...")
            cursor = conn.execute("SELECT COUNT(*) as count FROM logs WHERE created_at < datetime('now', '-30 days')")
            old_count = cursor.fetchone()['count']
            print(f"   30天前的日志数量: {old_count}")
            
    except Exception as e:
        print(f"✗ 调试失败: {e}")


async def debug_study_engine():
    """调试学习引擎问题"""
    print("=" * 50)
    print("调试学习引擎问题")
    print("=" * 50)
    
    try:
        # 1. 检查引擎状态
        print("1. 检查引擎状态...")
        print(f"   引擎运行状态: {study_engine.is_running}")
        print(f"   引擎状态: {study_engine.engine_status}")
        print(f"   当前用户: {study_engine.current_user}")
        
        # 2. 启动引擎
        print("2. 启动学习引擎...")
        success = await study_engine.start()
        print(f"   启动结果: {success}")
        
        if success:
            print(f"   引擎运行状态: {study_engine.is_running}")
            print(f"   引擎状态: {study_engine.engine_status}")
            
            # 3. 检查浏览器状态
            print("3. 检查浏览器状态...")
            browser_manager = study_engine.browser_manager
            print(f"   浏览器运行状态: {browser_manager.is_running}")
            print(f"   页面对象: {browser_manager.page is not None}")
            
            # 4. 测试学习流程
            print("4. 测试学习流程...")
            study_success = await study_engine.start_study("13800138000")
            print(f"   学习流程结果: {study_success}")
            
            # 5. 停止引擎
            print("5. 停止学习引擎...")
            await study_engine.stop()
            print(f"   引擎运行状态: {study_engine.is_running}")
        
    except Exception as e:
        print(f"✗ 调试失败: {e}")


def fix_log_cleanup():
    """修复日志清理问题"""
    print("=" * 50)
    print("修复日志清理问题")
    print("=" * 50)

    try:
        log_dao = DAOFactory.get_log_dao()

        # 使用修复后的清理方法
        print("1. 执行日志清理...")
        result = log_dao.clean_old_logs(30)
        print(f"   清理结果: {'成功' if result else '失败'}")

        return result

    except Exception as e:
        print(f"✗ 修复失败: {e}")
        return False


async def test_gui_integration():
    """测试GUI集成"""
    print("=" * 50)
    print("测试GUI集成")
    print("=" * 50)
    
    try:
        # 模拟GUI按钮点击
        from app.xueyuan.view.main_window import MainWindow
        from app.xueyuan.logging.manager import log_manager
        
        print("1. 创建主窗口实例...")
        # 注意：这里不能真正创建GUI，只能测试逻辑
        
        print("2. 模拟启动学习信号...")
        
        # 直接调用学习引擎
        if not study_engine.is_running:
            print("   启动学习引擎...")
            success = await study_engine.start()
            if not success:
                print("   ✗ 学习引擎启动失败")
                return False
        
        print("   开始学习流程...")
        success = await study_engine.start_study("13800138000")
        
        if success:
            print("   ✓ 学习流程启动成功")
        else:
            print("   ✗ 学习流程启动失败")
        
        # 停止引擎
        await study_engine.stop()
        
        return success
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False


async def main():
    """主函数"""
    print("开始调试问题...")
    print()
    
    # 调试日志清理
    debug_log_cleanup()
    print()
    
    # 修复日志清理
    fix_success = fix_log_cleanup()
    print()
    
    # 调试学习引擎
    await debug_study_engine()
    print()
    
    # 测试GUI集成
    gui_success = await test_gui_integration()
    print()
    
    # 总结
    print("=" * 50)
    print("调试结果总结")
    print("=" * 50)
    print(f"日志清理修复: {'✓ 成功' if fix_success else '✗ 失败'}")
    print(f"GUI集成测试: {'✓ 成功' if gui_success else '✗ 失败'}")


if __name__ == "__main__":
    asyncio.run(main())
