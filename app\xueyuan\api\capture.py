# coding:utf-8
"""
简化的API捕获类

该模块提供基本的API捕获功能。

主要功能：
- 简单的网络请求监听
- 基本的数据记录

类说明：
- APICapture: 简化的API捕获类
"""

import json
from typing import Optional, Any
from datetime import datetime
from playwright.async_api import Page, Response

from ..database.dao import DAOFactory


class APICapture:
    """
    简化的API捕获类

    提供基本的API捕获功能
    """

    def __init__(self, page: Optional[Page] = None):
        """
        初始化API捕获器

        Args:
            page: Playwright页面对象
        """
        self.page = page
        self.is_capturing = False

        # 数据库DAO
        self.log_dao = DAOFactory.get_log_dao()

        print("[API捕获] 简化API捕获器初始化完成")

    def set_page(self, page: Page):
        """
        设置Playwright页面对象

        Args:
            page: Playwright页面对象
        """
        self.page = page
        print("[API捕获] 页面对象已设置")

    async def start_capture(self):
        """开始API捕获 - 简化版本"""
        if not self.page:
            print("[API捕获] 页面对象未设置，无法开始捕获")
            return

        if self.is_capturing:
            print("[API捕获] 捕获已在进行中")
            return

        try:
            self.is_capturing = True
            print("[API捕获] 开始简单监听")

        except Exception as e:
            print(f"[API捕获] 启动捕获失败: {e}")
            self.is_capturing = False

    async def stop_capture(self):
        """停止API捕获 - 简化版本"""
        if not self.is_capturing:
            print("[API捕获] 捕获未在进行中")
            return

        try:
            self.is_capturing = False
            print("[API捕获] 停止监听")

        except Exception as e:
            print(f"[API捕获] 停止捕获失败: {e}")

    async def log_api_activity(self, user_phone: str, message: str) -> bool:
        """
        记录API活动 - 简化版本

        Args:
            user_phone: 用户手机号
            message: 活动消息

        Returns:
            bool: 是否记录成功
        """
        try:
            from ..database.models import Log
            from ..common.constants import LogLevel

            log = Log(
                level=LogLevel.INFO,
                message=f"[API] {message}",
                module="api.capture",
                user_phone=user_phone
            )
            self.log_dao.create(log)

            print(f"[API捕获] {message}")
            return True

        except Exception as e:
            print(f"[API捕获] 记录API活动失败: {e}")
            return False

    def get_status(self) -> str:
        """
        获取捕获状态 - 简化版本

        Returns:
            str: 状态信息
        """
        return "运行中" if self.is_capturing else "已停止"
