# coding:utf-8
"""
空白页面模块

该模块定义了一个简单的空白页面界面，可以作为新功能开发的起始模板。
页面采用简洁的设计，只包含基本的滚动区域和布局结构。

主要功能：
- 提供空白的页面模板
- 支持滚动显示
- 预留内容扩展空间

类说明：
- BlankInterface: 空白页面界面，继承自ScrollArea
"""

from PySide6.QtCore import Qt
from PySide6.QtWidgets import QWidget, QVBoxLayout, QLabel

from qfluentwidgets import ScrollArea, TitleLabel, BodyLabel
from ..common.style_sheet import StyleSheet


class BlankInterface(ScrollArea):
    """
    空白页面界面
    
    提供一个简洁的空白页面，可以用作新功能开发的起始模板。
    页面包含基本的标题和描述，支持后续内容扩展。
    """

    def __init__(self, parent=None):
        """
        初始化空白页面界面

        Args:
            parent: 父组件
        """
        super().__init__(parent=parent)
        self.view = QWidget(self)
        self.vBoxLayout = QVBoxLayout(self.view)
        
        # 创建页面内容
        self.titleLabel = TitleLabel(self.tr('空白页面'), self)
        self.descriptionLabel = BodyLabel(
            self.tr('这是一个空白页面，您可以在这里添加自己的内容。'), self)
        
        self.__initWidget()
        self.__initLayout()

    def __initWidget(self):
        """初始化组件"""
        self.setObjectName('blankInterface')
        
        # 设置滚动区域属性
        self.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.setWidget(self.view)
        self.setWidgetResizable(True)
        
        # 设置组件对象名称
        self.view.setObjectName('view')
        self.titleLabel.setObjectName('titleLabel')
        self.descriptionLabel.setObjectName('descriptionLabel')

        # 应用样式表
        StyleSheet.BLANK_INTERFACE.apply(self)

    def __initLayout(self):
        """初始化布局"""
        # 设置主布局
        self.vBoxLayout.setSpacing(20)
        self.vBoxLayout.setAlignment(Qt.AlignTop)
        self.vBoxLayout.setContentsMargins(36, 36, 36, 36)
        
        # 添加组件到布局
        self.vBoxLayout.addWidget(self.titleLabel)
        self.vBoxLayout.addWidget(self.descriptionLabel)
        
        # 添加弹性空间，使内容顶部对齐
        self.vBoxLayout.addStretch(1)
