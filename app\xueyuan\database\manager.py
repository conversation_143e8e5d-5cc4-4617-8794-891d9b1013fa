# coding:utf-8
"""
数据库管理器

该模块提供数据库连接管理、初始化、迁移等功能。
负责管理SQLite数据库的生命周期和连接池。

主要功能：
- 数据库连接管理
- 数据库初始化
- 表结构创建
- 数据库迁移
- 连接池管理

类说明：
- DatabaseManager: 数据库管理器类
"""

import sqlite3
import threading
from pathlib import Path
from contextlib import contextmanager
from typing import Optional, Generator

from .models import CREATE_TABLES_SQL, CREATE_INDEXES_SQL


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, db_path: str = "data/database/study.db"):
        """
        初始化数据库管理器
        
        Args:
            db_path: 数据库文件路径
        """
        self.db_path = Path(db_path)
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        self._local = threading.local()
        self._initialized = False
        
    def _get_connection(self) -> sqlite3.Connection:
        """获取线程本地数据库连接"""
        if not hasattr(self._local, 'connection'):
            self._local.connection = sqlite3.connect(
                str(self.db_path),
                check_same_thread=False,
                timeout=30.0
            )
            # 启用外键约束
            self._local.connection.execute("PRAGMA foreign_keys = ON")
            # 设置行工厂，使查询结果可以通过列名访问
            self._local.connection.row_factory = sqlite3.Row
        return self._local.connection
    
    @contextmanager
    def get_connection(self) -> Generator[sqlite3.Connection, None, None]:
        """获取数据库连接上下文管理器"""
        conn = self._get_connection()
        try:
            yield conn
        except Exception:
            conn.rollback()
            raise
        finally:
            # 不关闭连接，保持线程本地连接
            pass
    
    @contextmanager
    def get_transaction(self) -> Generator[sqlite3.Connection, None, None]:
        """获取事务上下文管理器"""
        conn = self._get_connection()
        try:
            conn.execute("BEGIN")
            yield conn
            conn.commit()
        except Exception:
            conn.rollback()
            raise
    
    def initialize_database(self) -> bool:
        """初始化数据库"""
        try:
            if self._initialized:
                return True
                
            with self.get_connection() as conn:
                # 创建表
                for table_name, sql in CREATE_TABLES_SQL.items():
                    conn.execute(sql)
                    print(f"[数据库] 已创建表: {table_name}")
                
                # 创建索引
                for sql in CREATE_INDEXES_SQL:
                    conn.execute(sql)
                
                conn.commit()
                print("[数据库] 数据库初始化完成")
                
            self._initialized = True
            return True
            
        except Exception as e:
            print(f"[数据库] 初始化失败: {e}")
            return False
    
    def check_database_exists(self) -> bool:
        """检查数据库文件是否存在"""
        return self.db_path.exists()
    
    def backup_database(self, backup_path: Optional[str] = None) -> bool:
        """备份数据库"""
        try:
            if backup_path is None:
                from datetime import datetime
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_path = f"data/database/study_backup_{timestamp}.db"
            
            backup_path = Path(backup_path)
            backup_path.parent.mkdir(parents=True, exist_ok=True)
            
            with self.get_connection() as conn:
                backup_conn = sqlite3.connect(str(backup_path))
                conn.backup(backup_conn)
                backup_conn.close()
                
            print(f"[数据库] 已备份到: {backup_path}")
            return True
            
        except Exception as e:
            print(f"[数据库] 备份失败: {e}")
            return False
    
    def restore_database(self, backup_path: str) -> bool:
        """恢复数据库"""
        try:
            backup_path = Path(backup_path)
            if not backup_path.exists():
                print(f"[数据库] 备份文件不存在: {backup_path}")
                return False
            
            # 关闭当前连接
            self.close_connections()
            
            # 复制备份文件
            import shutil
            shutil.copy2(backup_path, self.db_path)
            
            # 重新初始化
            self._initialized = False
            return self.initialize_database()
            
        except Exception as e:
            print(f"[数据库] 恢复失败: {e}")
            return False
    
    def get_database_info(self) -> dict:
        """获取数据库信息"""
        try:
            with self.get_connection() as conn:
                # 获取表信息
                tables = conn.execute(
                    "SELECT name FROM sqlite_master WHERE type='table'"
                ).fetchall()
                
                info = {
                    "database_path": str(self.db_path),
                    "database_size": self.db_path.stat().st_size if self.db_path.exists() else 0,
                    "tables": []
                }
                
                for table in tables:
                    table_name = table["name"]
                    count = conn.execute(f"SELECT COUNT(*) as count FROM {table_name}").fetchone()
                    info["tables"].append({
                        "name": table_name,
                        "count": count["count"]
                    })
                
                return info
                
        except Exception as e:
            print(f"[数据库] 获取数据库信息失败: {e}")
            return {}
    
    def execute_sql(self, sql: str, params: tuple = ()) -> bool:
        """执行SQL语句"""
        try:
            with self.get_transaction() as conn:
                conn.execute(sql, params)
            return True
        except Exception as e:
            print(f"[数据库] 执行SQL失败: {e}")
            return False
    
    def close_connections(self):
        """关闭所有连接"""
        if hasattr(self._local, 'connection'):
            self._local.connection.close()
            delattr(self._local, 'connection')


# 全局数据库管理器实例
db_manager = DatabaseManager()
