#!/usr/bin/env python
# coding:utf-8
"""
学习工具构建和部署脚本

该脚本用于构建学习工具的发布版本，包括：
- 依赖检查和安装
- 代码打包
- 资源文件处理
- 安装包生成
- 版本管理

使用方法：
    python deploy/build.py --help
    python deploy/build.py build
    python deploy/build.py package
    python deploy/build.py release
"""

import argparse
import os
import sys
import shutil
import subprocess
import json
import zipfile
from pathlib import Path
from datetime import datetime


class BuildManager:
    """构建管理器"""
    
    def __init__(self):
        self.root_dir = Path(__file__).parent.parent
        self.build_dir = self.root_dir / "build"
        self.dist_dir = self.root_dir / "dist"
        self.version_file = self.root_dir / "version.json"
        
        # 版本信息
        self.version_info = self.load_version_info()
    
    def load_version_info(self):
        """加载版本信息"""
        if self.version_file.exists():
            with open(self.version_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        else:
            return {
                "version": "1.0.0",
                "build": 1,
                "release_date": datetime.now().strftime("%Y-%m-%d"),
                "description": "学习工具首个版本"
            }
    
    def save_version_info(self):
        """保存版本信息"""
        with open(self.version_file, 'w', encoding='utf-8') as f:
            json.dump(self.version_info, f, ensure_ascii=False, indent=2)
    
    def check_environment(self):
        """检查构建环境"""
        print("检查构建环境...")
        
        # 检查Python版本
        python_version = sys.version_info
        if python_version < (3, 8):
            raise RuntimeError("需要Python 3.8或更高版本")
        
        print(f"✅ Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
        
        # 检查必要的包
        required_packages = [
            "PySide6",
            "qfluentwidgets", 
            "playwright",
            "ddddocr",
            "requests",
            "psutil"
        ]
        
        missing_packages = []
        for package in required_packages:
            try:
                __import__(package)
                print(f"✅ {package}")
            except ImportError:
                print(f"❌ {package} (未安装)")
                missing_packages.append(package)
        
        if missing_packages:
            print(f"\n需要安装缺失的包:")
            print(f"pip install {' '.join(missing_packages)}")
            return False
        
        # 检查构建工具
        try:
            subprocess.run(["pyinstaller", "--version"], 
                         capture_output=True, check=True)
            print("✅ PyInstaller")
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("❌ PyInstaller (未安装)")
            print("安装命令: pip install pyinstaller")
            return False
        
        print("✅ 构建环境检查通过")
        return True
    
    def clean_build(self):
        """清理构建目录"""
        print("清理构建目录...")
        
        if self.build_dir.exists():
            shutil.rmtree(self.build_dir)
            print(f"✅ 已清理 {self.build_dir}")
        
        if self.dist_dir.exists():
            shutil.rmtree(self.dist_dir)
            print(f"✅ 已清理 {self.dist_dir}")
        
        # 清理临时文件
        temp_files = [
            self.root_dir / "*.spec",
            self.root_dir / "__pycache__"
        ]
        
        for pattern in temp_files:
            for file_path in self.root_dir.glob(str(pattern)):
                if file_path.is_file():
                    file_path.unlink()
                elif file_path.is_dir():
                    shutil.rmtree(file_path)
                print(f"✅ 已清理 {file_path}")
    
    def prepare_build_dir(self):
        """准备构建目录"""
        print("准备构建目录...")
        
        # 创建构建目录
        self.build_dir.mkdir(exist_ok=True)
        self.dist_dir.mkdir(exist_ok=True)
        
        # 复制源代码
        source_dirs = ["app", "data", "docs"]
        for dir_name in source_dirs:
            source_dir = self.root_dir / dir_name
            if source_dir.exists():
                target_dir = self.build_dir / dir_name
                shutil.copytree(source_dir, target_dir, 
                              ignore=shutil.ignore_patterns("__pycache__", "*.pyc"))
                print(f"✅ 已复制 {dir_name}")
        
        # 复制主文件
        main_files = ["main.py", "requirements.txt", "README.md"]
        for file_name in main_files:
            source_file = self.root_dir / file_name
            if source_file.exists():
                target_file = self.build_dir / file_name
                shutil.copy2(source_file, target_file)
                print(f"✅ 已复制 {file_name}")
        
        # 生成版本文件
        version_target = self.build_dir / "version.json"
        shutil.copy2(self.version_file, version_target)
        print(f"✅ 已复制版本文件")
    
    def build_executable(self):
        """构建可执行文件"""
        print("构建可执行文件...")
        
        # PyInstaller配置
        spec_content = f'''
# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=['{self.build_dir}'],
    binaries=[],
    datas=[
        ('app', 'app'),
        ('data', 'data'),
        ('docs', 'docs'),
        ('version.json', '.'),
    ],
    hiddenimports=[
        'PySide6.QtCore',
        'PySide6.QtWidgets', 
        'PySide6.QtGui',
        'qfluentwidgets',
        'playwright',
        'ddddocr',
        'requests',
        'sqlite3',
        'threading',
        'concurrent.futures',
    ],
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='学习工具',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='app/resource/images/logo.ico',
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='学习工具',
)
'''
        
        # 写入spec文件
        spec_file = self.build_dir / "学习工具.spec"
        with open(spec_file, 'w', encoding='utf-8') as f:
            f.write(spec_content)
        
        # 运行PyInstaller
        cmd = [
            "pyinstaller",
            "--clean",
            "--noconfirm",
            str(spec_file)
        ]
        
        try:
            subprocess.run(cmd, cwd=self.build_dir, check=True)
            print("✅ 可执行文件构建完成")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ 构建失败: {e}")
            return False
    
    def create_installer(self):
        """创建安装包"""
        print("创建安装包...")
        
        # 查找构建的可执行文件
        exe_dir = self.build_dir / "dist" / "学习工具"
        if not exe_dir.exists():
            print("❌ 找不到构建的可执行文件")
            return False
        
        # 创建安装包目录结构
        installer_dir = self.dist_dir / f"学习工具_v{self.version_info['version']}"
        installer_dir.mkdir(exist_ok=True)
        
        # 复制可执行文件
        shutil.copytree(exe_dir, installer_dir / "程序文件")
        
        # 创建启动脚本
        start_script = installer_dir / "启动学习工具.bat"
        with open(start_script, 'w', encoding='gbk') as f:
            f.write(f'''@echo off
cd /d "%~dp0程序文件"
"学习工具.exe"
pause
''')
        
        # 复制文档
        docs_target = installer_dir / "文档"
        docs_target.mkdir(exist_ok=True)
        
        doc_files = [
            ("docs/用户手册.md", "用户手册.md"),
            ("README.md", "README.md"),
            ("requirements.txt", "依赖列表.txt")
        ]
        
        for source, target in doc_files:
            source_path = self.root_dir / source
            if source_path.exists():
                shutil.copy2(source_path, docs_target / target)
        
        # 创建安装说明
        install_readme = installer_dir / "安装说明.txt"
        with open(install_readme, 'w', encoding='utf-8') as f:
            f.write(f'''学习工具 v{self.version_info['version']} 安装说明

1. 系统要求：
   - Windows 10 (64位) 或更高版本
   - 4GB RAM 或更多
   - 500MB 可用磁盘空间
   - 稳定的网络连接

2. 安装步骤：
   - 将整个文件夹复制到任意位置（推荐：C:\\学习工具\\）
   - 双击"启动学习工具.bat"运行程序
   - 首次运行会自动创建配置文件和数据库

3. 注意事项：
   - 请确保有管理员权限
   - 杀毒软件可能会误报，请添加到白名单
   - 如遇问题请查看"文档"文件夹中的用户手册

4. 技术支持：
   - 邮箱：<EMAIL>
   - QQ群：123456789

发布日期：{self.version_info['release_date']}
''')
        
        print(f"✅ 安装包已创建: {installer_dir}")
        return True
    
    def create_zip_package(self):
        """创建ZIP压缩包"""
        print("创建ZIP压缩包...")
        
        installer_dir = self.dist_dir / f"学习工具_v{self.version_info['version']}"
        if not installer_dir.exists():
            print("❌ 找不到安装包目录")
            return False
        
        zip_file = self.dist_dir / f"学习工具_v{self.version_info['version']}.zip"
        
        with zipfile.ZipFile(zip_file, 'w', zipfile.ZIP_DEFLATED) as zf:
            for file_path in installer_dir.rglob('*'):
                if file_path.is_file():
                    arc_name = file_path.relative_to(installer_dir.parent)
                    zf.write(file_path, arc_name)
        
        print(f"✅ ZIP包已创建: {zip_file}")
        return True
    
    def update_version(self, version_type="patch"):
        """更新版本号"""
        current_version = self.version_info["version"].split(".")
        major, minor, patch = map(int, current_version)
        
        if version_type == "major":
            major += 1
            minor = 0
            patch = 0
        elif version_type == "minor":
            minor += 1
            patch = 0
        elif version_type == "patch":
            patch += 1
        
        self.version_info["version"] = f"{major}.{minor}.{patch}"
        self.version_info["build"] += 1
        self.version_info["release_date"] = datetime.now().strftime("%Y-%m-%d")
        
        self.save_version_info()
        print(f"✅ 版本已更新为: {self.version_info['version']}")
    
    def build(self):
        """执行完整构建流程"""
        print(f"开始构建学习工具 v{self.version_info['version']}")
        
        if not self.check_environment():
            return False
        
        self.clean_build()
        self.prepare_build_dir()
        
        if not self.build_executable():
            return False
        
        if not self.create_installer():
            return False
        
        if not self.create_zip_package():
            return False
        
        print(f"\n🎉 构建完成!")
        print(f"版本: {self.version_info['version']}")
        print(f"构建号: {self.version_info['build']}")
        print(f"输出目录: {self.dist_dir}")
        
        return True


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="学习工具构建和部署脚本",
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    parser.add_argument(
        "action",
        choices=["check", "clean", "build", "package", "release", "version"],
        help="要执行的操作"
    )
    
    parser.add_argument(
        "--version-type",
        choices=["major", "minor", "patch"],
        default="patch",
        help="版本更新类型"
    )
    
    args = parser.parse_args()
    
    builder = BuildManager()
    
    try:
        if args.action == "check":
            success = builder.check_environment()
        elif args.action == "clean":
            builder.clean_build()
            success = True
        elif args.action == "build":
            success = builder.build()
        elif args.action == "package":
            builder.prepare_build_dir()
            success = builder.create_installer() and builder.create_zip_package()
        elif args.action == "release":
            builder.update_version(args.version_type)
            success = builder.build()
        elif args.action == "version":
            builder.update_version(args.version_type)
            success = True
        
        sys.exit(0 if success else 1)
        
    except Exception as e:
        print(f"❌ 构建失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
