@echo off
chcp 65001 >nul
title 学习工具

echo ========================================
echo           学习工具启动脚本
echo ========================================
echo.

echo 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误：未找到Python环境
    echo 请确保已安装Python 3.8或更高版本
    echo 下载地址：https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python环境检查通过

echo.
echo 正在检查依赖包...
python -c "import PySide6, qfluentwidgets, playwright, ddddocr" >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误：缺少必要的依赖包
    echo 正在自动安装依赖包...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ❌ 依赖包安装失败
        echo 请手动运行：pip install -r requirements.txt
        pause
        exit /b 1
    )
)

echo ✅ 依赖包检查通过

echo.
echo 正在启动学习工具...
python main.py

if errorlevel 1 (
    echo.
    echo ❌ 程序运行出错
    echo 请检查错误信息或联系技术支持
    pause
)

echo.
echo 程序已退出
pause
