# coding:utf-8
"""
用户学习流程集成测试

测试完整的用户学习流程，包括用户管理、课程学习、
进度跟踪、数据存储等各个环节的集成。

测试场景：
- 用户注册和登录流程
- 课程学习完整流程
- 进度跟踪和更新
- 异常处理和恢复
- 并发用户学习
"""

import pytest
import asyncio
from datetime import datetime, timedelta
from unittest.mock import MagicMock, patch

from app.xueyuan.user.manager import UserManager
from app.xueyuan.course.manager import CourseManager
from app.xueyuan.automation.browser import BrowserManager
from app.xueyuan.automation.workflow import LearningWorkflow
from app.xueyuan.database.dao import UserDAO, CourseDAO, LogDAO
from app.xueyuan.logging import log_manager
from tests.conftest import create_test_user, create_test_course


@pytest.mark.integration
class TestUserLearningFlow:
    """用户学习流程集成测试"""
    
    @pytest.fixture
    def user_manager(self, test_db):
        """用户管理器夹具"""
        return UserManager()
    
    @pytest.fixture
    def course_manager(self, test_db):
        """课程管理器夹具"""
        return CourseManager()
    
    @pytest.fixture
    def mock_browser_manager(self):
        """模拟浏览器管理器"""
        manager = MagicMock(spec=BrowserManager)
        
        # 模拟浏览器启动
        manager.start_browser.return_value = True
        manager.stop_browser.return_value = True
        
        # 模拟页面操作
        manager.goto_page.return_value = True
        manager.login.return_value = True
        manager.start_course.return_value = True
        manager.complete_course.return_value = True
        
        return manager
    
    @pytest.fixture
    def learning_workflow(self, mock_browser_manager):
        """学习工作流夹具"""
        workflow = LearningWorkflow()
        workflow.browser_manager = mock_browser_manager
        return workflow
    
    def test_complete_user_registration_flow(self, user_manager):
        """测试完整的用户注册流程"""
        # 1. 创建新用户
        user_data = {
            "phone": "13800000100",
            "password": "test_password_123",
            "name": "集成测试用户",
            "status": "inactive"
        }
        
        result = user_manager.create_user(user_data)
        assert result is True
        
        # 2. 验证用户已创建
        user = user_manager.get_user("13800000100")
        assert user is not None
        assert user.name == "集成测试用户"
        assert user.status == "inactive"
        
        # 3. 激活用户
        result = user_manager.activate_user("13800000100")
        assert result is True
        
        # 4. 验证用户状态
        user = user_manager.get_user("13800000100")
        assert user.status == "active"
        
        # 5. 验证日志记录
        # 这里需要检查相关的日志是否被正确记录
    
    def test_complete_course_learning_flow(self, user_manager, course_manager, learning_workflow):
        """测试完整的课程学习流程"""
        # 1. 准备用户和课程数据
        user_data = {
            "phone": "13800000101",
            "password": "test_password",
            "name": "学习测试用户",
            "status": "active"
        }
        user_manager.create_user(user_data)
        
        course_data = {
            "title": "集成测试课程",
            "course_type": "compulsory",
            "duration": 3600,
            "status": "available",
            "url": "https://example.com/course/test"
        }
        course_manager.create_course(course_data)
        
        # 2. 开始学习流程
        user = user_manager.get_user("13800000101")
        courses = course_manager.get_available_courses("compulsory")
        
        assert len(courses) >= 1
        course = courses[0]
        
        # 3. 执行学习工作流
        result = learning_workflow.start_learning(user, course)
        assert result is True
        
        # 4. 验证学习进度更新
        updated_user = user_manager.get_user("13800000101")
        assert updated_user.compulsory_progress > user.compulsory_progress
        
        # 5. 验证课程状态更新
        updated_course = course_manager.get_course(course.id)
        # 根据具体实现验证课程状态
    
    def test_learning_progress_tracking(self, user_manager, course_manager, learning_workflow):
        """测试学习进度跟踪"""
        # 1. 创建用户
        user_data = {
            "phone": "13800000102",
            "password": "test_password",
            "name": "进度测试用户",
            "status": "active",
            "compulsory_progress": 0,
            "elective_progress": 0
        }
        user_manager.create_user(user_data)
        
        # 2. 创建多个课程
        courses_data = [
            {
                "title": "必修课程1",
                "course_type": "compulsory",
                "duration": 1800,
                "status": "available",
                "url": "https://example.com/course/comp1"
            },
            {
                "title": "必修课程2", 
                "course_type": "compulsory",
                "duration": 1800,
                "status": "available",
                "url": "https://example.com/course/comp2"
            },
            {
                "title": "选修课程1",
                "course_type": "elective",
                "duration": 1200,
                "status": "available",
                "url": "https://example.com/course/elec1"
            }
        ]
        
        for course_data in courses_data:
            course_manager.create_course(course_data)
        
        # 3. 模拟学习过程
        user = user_manager.get_user("13800000102")
        
        # 学习第一门必修课
        comp_courses = course_manager.get_available_courses("compulsory")
        result = learning_workflow.start_learning(user, comp_courses[0])
        assert result is True
        
        # 验证进度更新
        user = user_manager.get_user("13800000102")
        assert user.compulsory_progress > 0
        
        # 学习选修课
        elec_courses = course_manager.get_available_courses("elective")
        result = learning_workflow.start_learning(user, elec_courses[0])
        assert result is True
        
        # 验证选修课进度更新
        user = user_manager.get_user("13800000102")
        assert user.elective_progress > 0
    
    def test_learning_error_handling_and_recovery(self, user_manager, learning_workflow):
        """测试学习过程中的错误处理和恢复"""
        # 1. 创建用户
        user_data = {
            "phone": "13800000103",
            "password": "test_password",
            "name": "错误处理测试用户",
            "status": "active"
        }
        user_manager.create_user(user_data)
        user = user_manager.get_user("13800000103")
        
        # 2. 模拟网络错误
        with patch.object(learning_workflow.browser_manager, 'goto_page', side_effect=Exception("网络连接失败")):
            course = create_test_course(title="错误测试课程")
            result = learning_workflow.start_learning(user, course)
            
            # 应该处理错误并返回False
            assert result is False
        
        # 3. 验证用户状态未被错误影响
        user_after_error = user_manager.get_user("13800000103")
        assert user_after_error.status == "active"
        
        # 4. 验证错误日志记录
        # 检查是否记录了相应的错误日志
    
    def test_concurrent_user_learning(self, user_manager, course_manager, learning_workflow):
        """测试并发用户学习"""
        import threading
        import time
        
        # 1. 创建多个用户
        users_data = [
            {
                "phone": f"1380000010{i}",
                "password": "test_password",
                "name": f"并发测试用户{i}",
                "status": "active"
            }
            for i in range(5)
        ]
        
        for user_data in users_data:
            user_manager.create_user(user_data)
        
        # 2. 创建课程
        course_data = {
            "title": "并发测试课程",
            "course_type": "compulsory",
            "duration": 1800,
            "status": "available",
            "url": "https://example.com/course/concurrent"
        }
        course_manager.create_course(course_data)
        
        # 3. 并发学习
        results = []
        
        def learning_worker(phone):
            try:
                user = user_manager.get_user(phone)
                courses = course_manager.get_available_courses("compulsory")
                if courses:
                    result = learning_workflow.start_learning(user, courses[0])
                    results.append(result)
                else:
                    results.append(False)
            except Exception as e:
                results.append(False)
        
        # 创建并启动线程
        threads = []
        for i in range(5):
            phone = f"1380000010{i}"
            thread = threading.Thread(target=learning_worker, args=(phone,))
            threads.append(thread)
        
        for thread in threads:
            thread.start()
        
        for thread in threads:
            thread.join()
        
        # 4. 验证结果
        assert len(results) == 5
        # 根据并发控制策略，可能不是所有学习都会成功
        successful_count = sum(1 for r in results if r)
        assert successful_count > 0  # 至少有一些成功
    
    def test_learning_data_persistence(self, user_manager, course_manager, learning_workflow):
        """测试学习数据持久化"""
        # 1. 创建用户和课程
        user_data = {
            "phone": "13800000105",
            "password": "test_password",
            "name": "持久化测试用户",
            "status": "active"
        }
        user_manager.create_user(user_data)
        
        course_data = {
            "title": "持久化测试课程",
            "course_type": "compulsory",
            "duration": 3600,
            "status": "available",
            "url": "https://example.com/course/persistence"
        }
        course_manager.create_course(course_data)
        
        # 2. 执行学习
        user = user_manager.get_user("13800000105")
        courses = course_manager.get_available_courses("compulsory")
        course = courses[0]
        
        original_progress = user.compulsory_progress
        result = learning_workflow.start_learning(user, course)
        assert result is True
        
        # 3. 验证数据已持久化到数据库
        # 重新从数据库获取用户信息
        user_dao = UserDAO()
        persisted_user = user_dao.get_by_phone("13800000105")
        
        assert persisted_user is not None
        assert persisted_user.compulsory_progress > original_progress
        
        # 4. 验证学习记录
        log_dao = LogDAO()
        learning_logs = log_dao.get_logs({
            "user_phone": "13800000105",
            "module": "LearningWorkflow"
        }, limit=10)
        
        assert len(learning_logs) > 0
        
        # 验证日志内容
        log_messages = [log["message"] for log in learning_logs]
        assert any("开始学习" in msg for msg in log_messages)
    
    def test_learning_workflow_with_api_capture(self, user_manager, course_manager, learning_workflow, mock_api_capture):
        """测试带API捕获的学习工作流"""
        # 1. 设置API捕获
        learning_workflow.api_capture = mock_api_capture
        
        # 2. 创建用户和课程
        user_data = {
            "phone": "13800000106",
            "password": "test_password",
            "name": "API捕获测试用户",
            "status": "active"
        }
        user_manager.create_user(user_data)
        
        course_data = {
            "title": "API捕获测试课程",
            "course_type": "compulsory",
            "duration": 1800,
            "status": "available",
            "url": "https://example.com/course/api_capture"
        }
        course_manager.create_course(course_data)
        
        # 3. 执行学习工作流
        user = user_manager.get_user("13800000106")
        courses = course_manager.get_available_courses("compulsory")
        course = courses[0]
        
        result = learning_workflow.start_learning(user, course)
        assert result is True
        
        # 4. 验证API捕获被调用
        mock_api_capture.start_capture.assert_called()
        mock_api_capture.stop_capture.assert_called()
        
        # 5. 验证捕获的数据被处理
        captured_data = mock_api_capture.get_captured_data()
        assert len(captured_data) > 0
    
    def test_learning_workflow_with_ocr(self, user_manager, course_manager, learning_workflow, mock_ocr_engine):
        """测试带OCR识别的学习工作流"""
        # 1. 设置OCR引擎
        learning_workflow.ocr_engine = mock_ocr_engine
        
        # 2. 创建用户和课程
        user_data = {
            "phone": "13800000107",
            "password": "test_password",
            "name": "OCR测试用户",
            "status": "active"
        }
        user_manager.create_user(user_data)
        
        course_data = {
            "title": "OCR测试课程",
            "course_type": "compulsory",
            "duration": 1800,
            "status": "available",
            "url": "https://example.com/course/ocr_test"
        }
        course_manager.create_course(course_data)
        
        # 3. 模拟需要验证码的场景
        with patch.object(learning_workflow.browser_manager, 'login') as mock_login:
            # 模拟登录需要验证码
            mock_login.side_effect = [False, True]  # 第一次失败，第二次成功
            
            user = user_manager.get_user("13800000107")
            courses = course_manager.get_available_courses("compulsory")
            course = courses[0]
            
            result = learning_workflow.start_learning(user, course)
            assert result is True
            
            # 验证OCR被调用
            mock_ocr_engine.classification.assert_called()
    
    def test_learning_completion_and_achievement(self, user_manager, course_manager, learning_workflow):
        """测试学习完成和成就系统"""
        # 1. 创建用户
        user_data = {
            "phone": "13800000108",
            "password": "test_password",
            "name": "成就测试用户",
            "status": "active",
            "compulsory_progress": 90,  # 接近完成
            "elective_progress": 80
        }
        user_manager.create_user(user_data)
        
        # 2. 创建最后一门课程
        course_data = {
            "title": "最后一门必修课",
            "course_type": "compulsory",
            "duration": 1800,
            "status": "available",
            "url": "https://example.com/course/final"
        }
        course_manager.create_course(course_data)
        
        # 3. 完成最后的学习
        user = user_manager.get_user("13800000108")
        courses = course_manager.get_available_courses("compulsory")
        course = courses[0]
        
        result = learning_workflow.start_learning(user, course)
        assert result is True
        
        # 4. 验证用户状态更新为已完成
        completed_user = user_manager.get_user("13800000108")
        if completed_user.compulsory_progress >= 100:
            # 验证成就记录
            # 这里需要根据具体的成就系统实现来验证
            pass
    
    def test_learning_workflow_error_recovery(self, user_manager, course_manager, learning_workflow):
        """测试学习工作流的错误恢复"""
        # 1. 创建用户和课程
        user_data = {
            "phone": "13800000109",
            "password": "test_password",
            "name": "错误恢复测试用户",
            "status": "active"
        }
        user_manager.create_user(user_data)
        
        course_data = {
            "title": "错误恢复测试课程",
            "course_type": "compulsory",
            "duration": 3600,
            "status": "available",
            "url": "https://example.com/course/error_recovery"
        }
        course_manager.create_course(course_data)
        
        # 2. 模拟中途失败的学习过程
        user = user_manager.get_user("13800000109")
        courses = course_manager.get_available_courses("compulsory")
        course = courses[0]
        
        # 模拟浏览器崩溃
        with patch.object(learning_workflow.browser_manager, 'complete_course', side_effect=Exception("浏览器崩溃")):
            result = learning_workflow.start_learning(user, course)
            assert result is False
        
        # 3. 验证系统状态一致性
        user_after_error = user_manager.get_user("13800000109")
        assert user_after_error.status == "active"  # 用户状态应该保持一致
        
        # 4. 验证可以重新开始学习
        # 修复浏览器问题后重新尝试
        result = learning_workflow.start_learning(user_after_error, course)
        assert result is True
