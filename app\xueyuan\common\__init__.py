# coding:utf-8
"""
学习工具公共模块

该模块包含学习工具的公共组件和工具类：
- config_loader: 配置管理
- utils: 工具函数
- thread_pool: 线程池管理器
- concurrency: 并发控制器
- scheduler: 任务调度器

使用示例：
    from app.xueyuan.common import get_study_config, task_scheduler

    cfg = get_study_config()
    base_url = cfg.get(cfg.baseUrl)

    # 调度任务
    await task_scheduler.start()
"""

from .config_loader import get_study_config, StudyConfig
from .utils import generate_id, format_time, validate_phone
from .thread_pool import ThreadPoolManager, thread_pool_manager, TaskStatus, TaskResult
from .concurrency import ConcurrencyController, concurrency_controller, RateLimiter, RateLimitConfig
from .scheduler import TaskScheduler, task_scheduler, ScheduledTask, TaskPriority, TaskType

__all__ = [
    'get_study_config', 'StudyConfig',
    'generate_id', 'format_time', 'validate_phone',
    'ThreadPoolManager', 'thread_pool_manager', 'TaskStatus', 'TaskResult',
    'ConcurrencyController', 'concurrency_controller', 'RateLimiter', 'RateLimitConfig',
    'TaskScheduler', 'task_scheduler', 'ScheduledTask', 'TaskPriority', 'TaskType'
]
