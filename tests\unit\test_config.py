# coding:utf-8
"""
配置管理单元测试

测试配置加载器、配置验证和配置持久化功能。

测试内容：
- 配置文件加载
- 配置验证
- 配置保存
- 默认配置
- 配置更新
- 错误处理
"""

import pytest
import json
import tempfile
from pathlib import Path
from unittest.mock import patch, mock_open

from app.xueyuan.common.config import StudyConfig
from app.xueyuan.common.config_loader import StudyConfigLoader


class TestStudyConfig:
    """配置类测试"""
    
    def test_default_config(self):
        """测试默认配置"""
        config = StudyConfig()
        
        # 测试系统配置默认值
        assert config.get(config.asyncLogin) is True
        assert config.get(config.compulsoryCourses) == 10
        assert config.get(config.electiveCourses) == 5
        assert config.get(config.delayTime) == 2
        assert config.get(config.retryCount) == 3
        
        # 测试浏览器配置默认值
        assert config.get(config.browserType) == "Chrome"
        assert config.get(config.headless) is False
        assert config.get(config.disableImages) is False
        
        # 测试OCR配置默认值
        assert config.get(config.primaryEngine) == "Ddddocr"
        assert config.get(config.ocrTimeout) == 30
        
        # 测试日志配置默认值
        assert config.get(config.logLevel) == "INFO"
        assert config.get(config.enableConsoleLog) is True
        assert config.get(config.enableFileLog) is True
        assert config.get(config.enableDatabaseLog) is True
    
    def test_config_validation(self):
        """测试配置验证"""
        config = StudyConfig()
        
        # 测试有效值
        config.set(config.logLevel, "DEBUG")
        assert config.get(config.logLevel) == "DEBUG"
        
        config.set(config.browserType, "Firefox")
        assert config.get(config.browserType) == "Firefox"
        
        # 测试范围验证
        config.set(config.compulsoryCourses, 15)
        assert config.get(config.compulsoryCourses) == 15
        
        config.set(config.delayTime, 5)
        assert config.get(config.delayTime) == 5
    
    def test_config_change_signal(self):
        """测试配置变化信号"""
        config = StudyConfig()
        
        # 记录信号
        signals_received = []
        
        def on_config_changed(key, value):
            signals_received.append((key, value))
        
        config.configChanged.connect(on_config_changed)
        
        # 修改配置
        config.set(config.asyncLogin, False)
        config.set(config.compulsoryCourses, 20)
        
        # 验证信号
        assert len(signals_received) == 2
        assert ("System.AsyncLogin", False) in signals_received
        assert ("System.CompulsoryCourses", 20) in signals_received
    
    def test_config_groups(self):
        """测试配置分组"""
        config = StudyConfig()
        
        # 测试系统组
        system_keys = [
            config.asyncLogin.key,
            config.compulsoryCourses.key,
            config.electiveCourses.key,
            config.delayTime.key,
            config.retryCount.key
        ]
        
        for key in system_keys:
            assert key.startswith("System.")
        
        # 测试浏览器组
        browser_keys = [
            config.browserType.key,
            config.headless.key,
            config.disableImages.key,
            config.userAgent.key
        ]
        
        for key in browser_keys:
            assert key.startswith("Browser.")
        
        # 测试OCR组
        ocr_keys = [
            config.primaryEngine.key,
            config.baiduApiKey.key,
            config.baiduSecretKey.key,
            config.ocrTimeout.key
        ]
        
        for key in ocr_keys:
            assert key.startswith("OCR.")


class TestStudyConfigLoader:
    """配置加载器测试"""
    
    def test_load_default_config(self, temp_dir):
        """测试加载默认配置"""
        config_file = temp_dir / "config.json"
        loader = StudyConfigLoader(str(config_file))
        
        # 配置文件不存在时应该加载默认配置
        result = loader.load_config()
        assert result is True
        
        config = loader.get_config()
        assert config is not None
        assert config.get(config.asyncLogin) is True
        assert config.get(config.compulsoryCourses) == 10
    
    def test_load_existing_config(self, temp_dir):
        """测试加载现有配置"""
        config_file = temp_dir / "config.json"
        
        # 创建配置文件
        config_data = {
            "System": {
                "AsyncLogin": False,
                "CompulsoryCourses": 15,
                "ElectiveCourses": 8
            },
            "Browser": {
                "BrowserType": "Firefox",
                "Headless": True
            },
            "OCR": {
                "PrimaryEngine": "百度OCR",
                "OcrTimeout": 60
            }
        }
        
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, ensure_ascii=False, indent=2)
        
        # 加载配置
        loader = StudyConfigLoader(str(config_file))
        result = loader.load_config()
        assert result is True
        
        config = loader.get_config()
        assert config.get(config.asyncLogin) is False
        assert config.get(config.compulsoryCourses) == 15
        assert config.get(config.electiveCourses) == 8
        assert config.get(config.browserType) == "Firefox"
        assert config.get(config.headless) is True
        assert config.get(config.primaryEngine) == "百度OCR"
        assert config.get(config.ocrTimeout) == 60
    
    def test_save_config(self, temp_dir):
        """测试保存配置"""
        config_file = temp_dir / "config.json"
        loader = StudyConfigLoader(str(config_file))
        
        # 加载默认配置
        loader.load_config()
        config = loader.get_config()
        
        # 修改配置
        config.set(config.asyncLogin, False)
        config.set(config.compulsoryCourses, 25)
        config.set(config.browserType, "Edge")
        
        # 保存配置
        result = loader.save_config()
        assert result is True
        
        # 验证文件已创建
        assert config_file.exists()
        
        # 验证文件内容
        with open(config_file, 'r', encoding='utf-8') as f:
            saved_data = json.load(f)
        
        assert saved_data["System"]["AsyncLogin"] is False
        assert saved_data["System"]["CompulsoryCourses"] == 25
        assert saved_data["Browser"]["BrowserType"] == "Edge"
    
    def test_load_invalid_config(self, temp_dir):
        """测试加载无效配置"""
        config_file = temp_dir / "config.json"
        
        # 创建无效的JSON文件
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write("invalid json content")
        
        loader = StudyConfigLoader(str(config_file))
        result = loader.load_config()
        
        # 应该回退到默认配置
        assert result is True
        config = loader.get_config()
        assert config.get(config.asyncLogin) is True  # 默认值
    
    def test_load_partial_config(self, temp_dir):
        """测试加载部分配置"""
        config_file = temp_dir / "config.json"
        
        # 创建部分配置文件
        config_data = {
            "System": {
                "AsyncLogin": False,
                "CompulsoryCourses": 20
                # 缺少其他配置项
            }
            # 缺少其他配置组
        }
        
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, ensure_ascii=False, indent=2)
        
        loader = StudyConfigLoader(str(config_file))
        result = loader.load_config()
        assert result is True
        
        config = loader.get_config()
        
        # 已配置的项应该使用文件中的值
        assert config.get(config.asyncLogin) is False
        assert config.get(config.compulsoryCourses) == 20
        
        # 未配置的项应该使用默认值
        assert config.get(config.electiveCourses) == 5  # 默认值
        assert config.get(config.browserType) == "Chrome"  # 默认值
    
    def test_config_file_permissions(self, temp_dir):
        """测试配置文件权限"""
        config_file = temp_dir / "config.json"
        loader = StudyConfigLoader(str(config_file))
        
        # 创建只读目录（模拟权限问题）
        import os
        import stat
        
        # 在Windows上，这个测试可能不会按预期工作
        if os.name != 'nt':
            temp_dir.chmod(stat.S_IRUSR | stat.S_IXUSR)  # 只读权限
            
            result = loader.save_config()
            assert result is False  # 保存应该失败
            
            # 恢复权限
            temp_dir.chmod(stat.S_IRWXU)
    
    def test_config_backup(self, temp_dir):
        """测试配置备份"""
        config_file = temp_dir / "config.json"
        backup_file = temp_dir / "config.json.backup"
        
        # 创建原始配置
        config_data = {
            "System": {
                "AsyncLogin": True,
                "CompulsoryCourses": 10
            }
        }
        
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, ensure_ascii=False, indent=2)
        
        loader = StudyConfigLoader(str(config_file))
        loader.load_config()
        
        # 修改并保存配置
        config = loader.get_config()
        config.set(config.asyncLogin, False)
        
        result = loader.save_config()
        assert result is True
        
        # 验证备份文件是否创建
        # 注意：这取决于具体的实现，可能需要在ConfigLoader中添加备份功能
    
    @patch('builtins.open', side_effect=PermissionError("Permission denied"))
    def test_save_config_permission_error(self, mock_file):
        """测试保存配置时的权限错误"""
        loader = StudyConfigLoader("test_config.json")
        loader.load_config()
        
        result = loader.save_config()
        assert result is False
    
    @patch('json.load', side_effect=json.JSONDecodeError("Invalid JSON", "", 0))
    def test_load_config_json_error(self, mock_json_load):
        """测试JSON解析错误"""
        with patch('builtins.open', mock_open(read_data='invalid json')):
            loader = StudyConfigLoader("test_config.json")
            result = loader.load_config()
            
            # 应该回退到默认配置
            assert result is True
            config = loader.get_config()
            assert config is not None


@pytest.mark.unit
class TestConfigIntegration:
    """配置集成测试"""
    
    def test_config_lifecycle(self, temp_dir):
        """测试配置的完整生命周期"""
        config_file = temp_dir / "config.json"
        
        # 1. 创建加载器并加载默认配置
        loader = StudyConfigLoader(str(config_file))
        assert loader.load_config() is True
        
        config = loader.get_config()
        original_value = config.get(config.asyncLogin)
        
        # 2. 修改配置
        new_value = not original_value
        config.set(config.asyncLogin, new_value)
        
        # 3. 保存配置
        assert loader.save_config() is True
        
        # 4. 创建新的加载器并重新加载
        new_loader = StudyConfigLoader(str(config_file))
        assert new_loader.load_config() is True
        
        new_config = new_loader.get_config()
        
        # 5. 验证配置已持久化
        assert new_config.get(new_config.asyncLogin) == new_value
    
    def test_multiple_config_instances(self, temp_dir):
        """测试多个配置实例"""
        config_file = temp_dir / "config.json"
        
        # 创建两个加载器
        loader1 = StudyConfigLoader(str(config_file))
        loader2 = StudyConfigLoader(str(config_file))
        
        loader1.load_config()
        loader2.load_config()
        
        config1 = loader1.get_config()
        config2 = loader2.get_config()
        
        # 修改第一个配置
        config1.set(config1.asyncLogin, False)
        loader1.save_config()
        
        # 重新加载第二个配置
        loader2.load_config()
        config2_reloaded = loader2.get_config()
        
        # 验证第二个配置也反映了变化
        assert config2_reloaded.get(config2_reloaded.asyncLogin) is False
