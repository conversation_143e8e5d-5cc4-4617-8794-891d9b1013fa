# coding:utf-8
"""
Ddddocr引擎实现

该模块实现了基于ddddocr库的OCR引擎。
ddddocr是一个免费的OCR库，无需API密钥，适合作为主要OCR引擎。

主要功能：
- 文字识别
- 验证码识别
- 目标检测

类说明：
- DdddOCREngine: Ddddocr引擎实现类
"""

import time
from typing import Union, Optional, Dict, Any
from pathlib import Path

from .base import BaseOCREngine, OCRResult


class DdddOCREngine(BaseOCREngine):
    """
    Ddddocr引擎实现类
    
    基于ddddocr库实现的OCR引擎，支持文字识别和验证码识别。
    无需API密钥，可以离线使用。
    """
    
    def __init__(self, **kwargs):
        """
        初始化Ddddocr引擎
        
        Args:
            **kwargs: 配置参数
                - use_gpu: 是否使用GPU (默认False)
                - beta: 是否使用beta版本 (默认False)
                - show_ad: 是否显示广告 (默认False)
        """
        super().__init__("ddddocr", **kwargs)
        
        # 配置参数
        self.use_gpu = kwargs.get('use_gpu', False)
        self.beta = kwargs.get('beta', False)
        self.show_ad = kwargs.get('show_ad', False)
        
        # OCR实例
        self.ocr_instance = None
        self.det_instance = None  # 目标检测实例
    
    def initialize(self) -> bool:
        """
        初始化OCR引擎
        
        Returns:
            bool: 初始化是否成功
        """
        try:
            # 尝试导入ddddocr
            import ddddocr
            
            # 创建OCR实例
            self.ocr_instance = ddddocr.DdddOcr(
                use_gpu=self.use_gpu,
                beta=self.beta,
                show_ad=self.show_ad
            )
            
            # 创建目标检测实例（可选）
            try:
                self.det_instance = ddddocr.DdddOcr(det=True, show_ad=self.show_ad)
            except Exception as e:
                print(f"[OCR] 目标检测实例创建失败: {e}")
                self.det_instance = None
            
            self.is_initialized = True
            self.error_message = ""
            print(f"[OCR] {self.name} 引擎初始化成功")
            return True
            
        except ImportError as e:
            self.error_message = f"ddddocr库未安装: {e}"
            print(f"[OCR] {self.error_message}")
            return False
        except Exception as e:
            self.error_message = f"初始化失败: {e}"
            print(f"[OCR] {self.error_message}")
            return False
    
    def recognize_text(self, image_data: Union[bytes, str, Path]) -> OCRResult:
        """
        识别图片中的文字
        
        Args:
            image_data: 图片数据
            
        Returns:
            OCRResult: 识别结果
        """
        if not self.is_initialized:
            return self._create_error_result("引擎未初始化")
        
        if not self.ocr_instance:
            return self._create_error_result("OCR实例不可用")
        
        try:
            start_time = time.time()
            
            # 加载图片数据
            image_bytes = self._load_image_data(image_data)
            
            # 执行OCR识别
            result_text = self.ocr_instance.classification(image_bytes)
            
            processing_time = time.time() - start_time
            
            # 清理识别结果
            if result_text:
                result_text = result_text.strip()
            
            # 创建成功结果
            return self._create_success_result(
                text=result_text or "",
                confidence=0.9,  # ddddocr不提供置信度，使用默认值
                processing_time=processing_time,
                raw_result={"original_text": result_text}
            )
            
        except FileNotFoundError as e:
            return self._create_error_result(f"图片文件不存在: {e}")
        except Exception as e:
            return self._create_error_result(f"识别失败: {e}")
    
    def recognize_captcha(self, image_data: Union[bytes, str, Path]) -> OCRResult:
        """
        识别验证码
        
        Args:
            image_data: 验证码图片数据
            
        Returns:
            OCRResult: 识别结果
        """
        # 验证码识别与普通文字识别使用相同的方法
        return self.recognize_text(image_data)
    
    def detect_objects(self, image_data: Union[bytes, str, Path]) -> OCRResult:
        """
        目标检测
        
        Args:
            image_data: 图片数据
            
        Returns:
            OCRResult: 检测结果
        """
        if not self.is_initialized:
            return self._create_error_result("引擎未初始化")
        
        if not self.det_instance:
            return self._create_error_result("目标检测实例不可用")
        
        try:
            start_time = time.time()
            
            # 加载图片数据
            image_bytes = self._load_image_data(image_data)
            
            # 执行目标检测
            bboxes = self.det_instance.detection(image_bytes)
            
            processing_time = time.time() - start_time
            
            # 处理检测结果
            positions = []
            if bboxes:
                for bbox in bboxes:
                    positions.append({
                        'x1': bbox[0],
                        'y1': bbox[1], 
                        'x2': bbox[2],
                        'y2': bbox[3]
                    })
            
            return self._create_success_result(
                text=f"检测到 {len(positions)} 个目标",
                confidence=0.9,
                processing_time=processing_time,
                raw_result={"bboxes": bboxes},
                positions=positions
            )
            
        except FileNotFoundError as e:
            return self._create_error_result(f"图片文件不存在: {e}")
        except Exception as e:
            return self._create_error_result(f"检测失败: {e}")
    
    def is_available(self) -> bool:
        """
        检查引擎是否可用
        
        Returns:
            bool: 引擎是否可用
        """
        try:
            import ddddocr
            return True
        except ImportError:
            return False
    
    def cleanup(self):
        """清理资源"""
        self.ocr_instance = None
        self.det_instance = None
        self.is_initialized = False
        print(f"[OCR] {self.name} 引擎资源已清理")
    
    def get_engine_info(self) -> Dict[str, Any]:
        """
        获取引擎信息
        
        Returns:
            Dict[str, Any]: 引擎信息
        """
        info = {
            "name": self.name,
            "version": "unknown",
            "initialized": self.is_initialized,
            "use_gpu": self.use_gpu,
            "beta": self.beta,
            "features": ["text_recognition", "captcha_recognition"],
            "requires_api_key": False,
            "offline_capable": True
        }
        
        # 尝试获取版本信息
        try:
            import ddddocr
            if hasattr(ddddocr, '__version__'):
                info["version"] = ddddocr.__version__
        except:
            pass
        
        # 检查目标检测功能
        if self.det_instance:
            info["features"].append("object_detection")
        
        return info
