#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复结果
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.xueyuan.database.dao import DAOFactory


def test_log_cleanup():
    """测试日志清理功能"""
    print("=" * 50)
    print("测试日志清理功能")
    print("=" * 50)
    
    try:
        log_dao = DAOFactory.get_log_dao()
        
        # 测试清理功能
        print("1. 执行日志清理测试...")
        result = log_dao.clean_old_logs(30)
        print(f"   清理结果: {'✓ 成功' if result else '✗ 失败'}")
        
        # 测试不同天数的清理
        print("2. 测试不同天数的清理...")
        result_7 = log_dao.clean_old_logs(7)
        print(f"   7天前日志清理: {'✓ 成功' if result_7 else '✗ 失败'}")
        
        result_1 = log_dao.clean_old_logs(1)
        print(f"   1天前日志清理: {'✓ 成功' if result_1 else '✗ 失败'}")
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False


async def test_study_engine_basic():
    """测试学习引擎基础功能"""
    print("=" * 50)
    print("测试学习引擎基础功能")
    print("=" * 50)
    
    try:
        from app.xueyuan.automation.engine import study_engine
        
        # 1. 测试引擎启动
        print("1. 测试引擎启动...")
        success = await study_engine.start()
        print(f"   启动结果: {'✓ 成功' if success else '✗ 失败'}")
        
        if success:
            print(f"   引擎状态: {study_engine.engine_status}")
            print(f"   浏览器状态: {study_engine.browser_manager.is_running}")
            
            # 2. 测试登录状态检查
            print("2. 测试登录状态检查...")
            study_handler = study_engine.study_handler
            login_status = await study_handler._check_login_status()
            print(f"   登录状态: {'✓ 已登录' if login_status else '✗ 未登录'}")
            
            # 3. 如果未登录，提示用户
            if not login_status:
                print("   提示: 请先通过GUI界面进行登录，然后再测试学习功能")
            else:
                print("3. 测试课程获取...")
                courses = await study_handler._get_course_list()
                total_courses = len(courses.get("必修课", [])) + len(courses.get("选修课", []))
                print(f"   课程获取结果: 必修课 {len(courses.get('必修课', []))} 门, 选修课 {len(courses.get('选修课', []))} 门")
                print(f"   总课程数: {total_courses}")
            
            # 4. 停止引擎
            print("4. 停止引擎...")
            await study_engine.stop()
            print(f"   引擎状态: {study_engine.engine_status}")
        
        return success
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False


def test_gui_signal_connection():
    """测试GUI信号连接"""
    print("=" * 50)
    print("测试GUI信号连接")
    print("=" * 50)
    
    try:
        # 测试信号连接逻辑
        from app.xueyuan.view.study_control_interface import StudyControlInterface
        from PySide6.QtWidgets import QApplication
        import sys
        
        # 创建应用程序实例（如果不存在）
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建控制界面
        interface = StudyControlInterface()
        
        # 检查信号是否存在
        has_start_signal = hasattr(interface, 'startStudySignal')
        has_stop_signal = hasattr(interface, 'stopStudySignal')
        
        print(f"   启动学习信号: {'✓ 存在' if has_start_signal else '✗ 不存在'}")
        print(f"   停止学习信号: {'✓ 存在' if has_stop_signal else '✗ 不存在'}")
        
        # 测试信号发射
        if has_start_signal:
            print("   测试信号发射...")
            interface.startStudySignal.emit()
            print("   ✓ 信号发射成功")
        
        return has_start_signal and has_stop_signal
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False


async def main():
    """主测试函数"""
    print("开始测试修复结果...")
    print()
    
    # 测试日志清理
    log_test = test_log_cleanup()
    print()
    
    # 测试学习引擎
    engine_test = await test_study_engine_basic()
    print()
    
    # 测试GUI信号
    gui_test = test_gui_signal_connection()
    print()
    
    # 总结
    print("=" * 50)
    print("测试结果总结")
    print("=" * 50)
    print(f"日志清理功能: {'✓ 正常' if log_test else '✗ 异常'}")
    print(f"学习引擎功能: {'✓ 正常' if engine_test else '✗ 异常'}")
    print(f"GUI信号连接: {'✓ 正常' if gui_test else '✗ 异常'}")
    print()
    
    if log_test and engine_test and gui_test:
        print("🎉 所有功能测试通过！")
        print()
        print("使用说明:")
        print("1. 日志清理功能已修复，现在可以正确清理旧日志")
        print("2. 学习引擎已按照开发要求文档重新实现")
        print("3. 点击'启动学习'按钮前，请先通过登录功能登录到学习网站")
        print("4. 登录成功后，点击'启动学习'按钮即可开始自动学习")
    else:
        print("❌ 部分功能仍有问题，需要进一步调试")


if __name__ == "__main__":
    asyncio.run(main())
