/* 学习工具界面样式表 */

/* 主界面样式 */
StudyMainInterface {
    background-color: transparent;
}

/* 卡片样式 */
HeaderCardWidget {
    background-color: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    margin: 5px;
}

HeaderCardWidget > QLabel {
    font-size: 16px;
    font-weight: bold;
    color: #323130;
    padding: 10px;
}

/* 控制面板样式 */
StudyControlInterface {
    background-color: transparent;
}

/* 进度环样式 */
ProgressRing {
    background-color: transparent;
}

/* 按钮样式 */
PrimaryPushButton {
    background-color: #0078d4;
    border: none;
    border-radius: 4px;
    color: white;
    font-size: 14px;
    font-weight: 500;
    padding: 8px 16px;
}

PrimaryPushButton:hover {
    background-color: #106ebe;
}

PrimaryPushButton:pressed {
    background-color: #005a9e;
}

PrimaryPushButton:disabled {
    background-color: #f3f2f1;
    color: #a19f9d;
}

PushButton {
    background-color: #ffffff;
    border: 1px solid #8a8886;
    border-radius: 4px;
    color: #323130;
    font-size: 14px;
    padding: 8px 16px;
}

PushButton:hover {
    background-color: #f3f2f1;
    border-color: #323130;
}

PushButton:pressed {
    background-color: #edebe9;
    border-color: #201f1e;
}

PushButton:disabled {
    background-color: #f3f2f1;
    border-color: #c8c6c4;
    color: #a19f9d;
}

/* 表格样式 */
TableWidget {
    background-color: white;
    border: 1px solid #e1dfdd;
    border-radius: 4px;
    gridline-color: #e1dfdd;
    selection-background-color: #deecf9;
}

TableWidget::item {
    padding: 8px;
    border: none;
}

TableWidget::item:selected {
    background-color: #deecf9;
    color: #323130;
}

TableWidget QHeaderView::section {
    background-color: #f3f2f1;
    border: none;
    border-bottom: 1px solid #e1dfdd;
    border-right: 1px solid #e1dfdd;
    padding: 8px;
    font-weight: 600;
    color: #323130;
}

/* 输入框样式 */
LineEdit {
    background-color: white;
    border: 1px solid #8a8886;
    border-radius: 4px;
    padding: 8px 12px;
    font-size: 14px;
    color: #323130;
}

LineEdit:focus {
    border-color: #0078d4;
    border-width: 2px;
}

LineEdit:disabled {
    background-color: #f3f2f1;
    border-color: #c8c6c4;
    color: #a19f9d;
}

/* 下拉框样式 */
ComboBox {
    background-color: white;
    border: 1px solid #8a8886;
    border-radius: 4px;
    padding: 8px 12px;
    font-size: 14px;
    color: #323130;
}

ComboBox:focus {
    border-color: #0078d4;
    border-width: 2px;
}

ComboBox:disabled {
    background-color: #f3f2f1;
    border-color: #c8c6c4;
    color: #a19f9d;
}

/* 数字输入框样式 */
SpinBox {
    background-color: white;
    border: 1px solid #8a8886;
    border-radius: 4px;
    padding: 8px 12px;
    font-size: 14px;
    color: #323130;
}

SpinBox:focus {
    border-color: #0078d4;
    border-width: 2px;
}

SpinBox:disabled {
    background-color: #f3f2f1;
    border-color: #c8c6c4;
    color: #a19f9d;
}

/* 开关按钮样式 */
SwitchButton {
    background-color: transparent;
}

/* 进度条样式 */
ProgressBar {
    background-color: #f3f2f1;
    border: none;
    border-radius: 4px;
    text-align: center;
}

ProgressBar::chunk {
    background-color: #0078d4;
    border-radius: 4px;
}

/* 标签样式 */
BodyLabel {
    color: #323130;
    font-size: 14px;
}

CaptionLabel {
    color: #605e5c;
    font-size: 12px;
}

/* Pivot导航样式 */
Pivot {
    background-color: transparent;
}

Pivot > QPushButton {
    background-color: transparent;
    border: none;
    border-bottom: 2px solid transparent;
    color: #605e5c;
    font-size: 14px;
    font-weight: 500;
    padding: 12px 16px;
    margin: 0 4px;
}

Pivot > QPushButton:hover {
    color: #323130;
    background-color: rgba(0, 0, 0, 0.05);
}

Pivot > QPushButton:checked {
    color: #0078d4;
    border-bottom-color: #0078d4;
    background-color: transparent;
}

/* 滚动区域样式 */
ScrollArea {
    background-color: transparent;
    border: none;
}

ScrollArea > QScrollBar:vertical {
    background-color: #f3f2f1;
    width: 12px;
    border-radius: 6px;
}

ScrollArea > QScrollBar::handle:vertical {
    background-color: #c8c6c4;
    border-radius: 6px;
    min-height: 20px;
}

ScrollArea > QScrollBar::handle:vertical:hover {
    background-color: #a19f9d;
}

ScrollArea > QScrollBar::add-line:vertical,
ScrollArea > QScrollBar::sub-line:vertical {
    border: none;
    background: none;
}

/* 信息栏样式 */
InfoBar {
    border-radius: 4px;
    font-size: 14px;
}

InfoBar[type="success"] {
    background-color: #dff6dd;
    border: 1px solid #0e700e;
    color: #0e700e;
}

InfoBar[type="warning"] {
    background-color: #fff4ce;
    border: 1px solid #8a8000;
    color: #8a8000;
}

InfoBar[type="error"] {
    background-color: #fde7e9;
    border: 1px solid #a4262c;
    color: #a4262c;
}

InfoBar[type="info"] {
    background-color: #deecf9;
    border: 1px solid #0078d4;
    color: #0078d4;
}

/* 对话框样式 */
Dialog {
    background-color: white;
    border: 1px solid #e1dfdd;
    border-radius: 8px;
}

Dialog > QLabel {
    color: #323130;
    font-size: 16px;
    font-weight: 600;
}

/* 表单布局样式 */
QFormLayout > QLabel {
    color: #323130;
    font-size: 14px;
    font-weight: 500;
    padding: 4px 0;
}

/* 统计数值样式 */
.stats-value {
    font-size: 24px;
    font-weight: bold;
    color: #0078d4;
}

.stats-label {
    font-size: 14px;
    color: #605e5c;
}

/* 状态指示器样式 */
.status-ready {
    color: #0078d4;
}

.status-running {
    color: #32cd32;
}

.status-paused {
    color: #ff8c00;
}

.status-stopped {
    color: #dc143c;
}

.status-error {
    color: #a4262c;
}

/* 动画效果 */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}
