# 学习工具 (Learning Tool)

一款基于PySide6开发的智能化学习辅助工具，支持自动化学习、进度跟踪、用户管理等功能。

## 🌟 主要特性

### 核心功能
- **🤖 自动化学习**：支持自动登录、课程学习、进度跟踪
- **👁️ 智能识别**：集成双OCR引擎（Ddddocr + 百度OCR），自动识别验证码
- **📡 API捕获**：实时捕获学习平台API数据，支持数据分析
- **👥 用户管理**：支持多用户批量管理，用户状态监控
- **📊 进度监控**：实时监控学习进度，支持进度统计和报告
- **📝 日志系统**：完整的操作日志记录，支持多种格式导出

### 技术特点
- **🎨 现代化界面**：基于qfluentwidgets的现代化UI设计，支持亮色/暗色主题
- **⚡ 高性能并发**：支持多任务同时执行，智能任务调度
- **🛡️ 异常处理**：完善的异常处理和自动重试机制
- **💾 数据安全**：完整的数据备份和恢复功能
- **🔧 高度可配置**：丰富的配置选项，满足不同使用需求

## 📋 系统要求

### 最低配置
- **操作系统**：Windows 10 (64位) 或更高版本
- **处理器**：Intel Core i3 或 AMD 同等级别
- **内存**：4GB RAM
- **存储空间**：500MB 可用空间
- **网络**：稳定的互联网连接

### 推荐配置
- **操作系统**：Windows 11 (64位)
- **处理器**：Intel Core i5 或 AMD 同等级别
- **内存**：8GB RAM 或更多
- **存储空间**：2GB 可用空间

## 🚀 快速开始

### 安装依赖

```bash
# 克隆项目
git clone https://github.com/your-repo/learning-tool.git
cd learning-tool

# 安装Python依赖
pip install -r requirements.txt

# 安装浏览器驱动
playwright install chromium
```

### 运行程序

```bash
python main.py
```

### 首次使用

1. 启动软件后，点击设置按钮进行基础配置
2. 在用户管理中添加学习用户
3. 点击开始学习按钮启动自动化流程

## 📁 项目结构

```
learning-tool/
├── app/                    # 应用程序主目录
│   ├── xueyuan/           # 学习工具模块
│   │   ├── automation/    # 自动化模块
│   │   ├── common/        # 公共模块
│   │   ├── components/    # UI组件
│   │   ├── course/        # 课程管理
│   │   ├── database/      # 数据库模块
│   │   ├── logging/       # 日志系统
│   │   ├── user/          # 用户管理
│   │   └── view/          # 界面视图
│   └── ocr/               # OCR识别模块
├── data/                  # 数据目录
│   ├── config/           # 配置文件
│   ├── database/         # 数据库文件
│   └── logs/             # 日志文件
├── docs/                 # 文档目录
├── tests/                # 测试目录
│   ├── unit/            # 单元测试
│   ├── integration/     # 集成测试
│   └── performance/     # 性能测试
├── deploy/              # 部署脚本
├── main.py              # 程序入口
├── requirements.txt     # 依赖列表
└── README.md           # 项目说明
```

## 🔧 配置说明

### 系统配置
- 异步登录模式
- 课程数量设置
- 延迟和重试参数

### 浏览器配置
- 浏览器类型选择
- 无头模式设置
- 性能优化选项

### OCR配置
- 主引擎选择
- 百度OCR API配置
- 识别参数调整

### 日志配置
- 日志级别设置
- 输出目标配置
- 文件轮转设置

## 🧪 测试

### 运行测试

```bash
# 检查测试环境
python run_tests.py check

# 运行单元测试
python run_tests.py unit

# 运行集成测试
python run_tests.py integration

# 运行性能测试
python run_tests.py performance

# 运行所有测试
python run_tests.py all

# 生成覆盖率报告
python run_tests.py coverage
```

### 测试覆盖
- **单元测试**：测试单个模块功能
- **集成测试**：测试模块间协作
- **性能测试**：测试系统性能表现

## 📦 构建和部署

### 构建可执行文件

```bash
# 检查构建环境
python deploy/build.py check

# 清理构建目录
python deploy/build.py clean

# 构建程序
python deploy/build.py build

# 创建发布包
python deploy/build.py release
```

### 部署选项
- **开发模式**：直接运行Python脚本
- **打包模式**：使用PyInstaller打包为可执行文件
- **安装包**：创建完整的安装包

## 📖 文档

- [用户手册](docs/用户手册.md) - 详细的使用说明
- [测试文档](tests/README.md) - 测试架构和运行方法
- [API文档](docs/API文档.md) - 开发者接口文档

## 🤝 贡献指南

我们欢迎任何形式的贡献！

### 贡献方式
1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

### 开发规范
- 遵循PEP 8代码风格
- 为新功能编写测试
- 更新相关文档
- 保持代码注释清晰

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🆘 技术支持

### 获取帮助
- **邮箱**：<EMAIL>
- **QQ群**：123456789
- **Issues**：[GitHub Issues](https://github.com/your-repo/learning-tool/issues)

### 常见问题
查看 [用户手册](docs/用户手册.md) 中的常见问题部分。

### 反馈问题
提交问题时请包含：
- 软件版本号
- 操作系统信息
- 详细的错误描述
- 重现步骤
- 相关日志信息

## 📊 项目状态

![Python Version](https://img.shields.io/badge/python-3.8+-blue.svg)
![License](https://img.shields.io/badge/license-MIT-green.svg)
![Build Status](https://img.shields.io/badge/build-passing-brightgreen.svg)
![Coverage](https://img.shields.io/badge/coverage-90%25-brightgreen.svg)

## 🔄 更新日志

### v1.0.0 (2025-01-14)
- 🎉 首个正式版本发布
- ✨ 完整的自动化学习功能
- 👥 用户管理和进度跟踪
- 👁️ 双OCR引擎集成
- 📡 API捕获和数据存储
- 📝 完善的日志系统
- 🎨 现代化用户界面
- 🧪 完整的测试覆盖

## 🙏 致谢

感谢以下开源项目的支持：
- [PySide6](https://www.qt.io/qt-for-python) - 跨平台GUI框架
- [qfluentwidgets](https://github.com/zhiyiYo/PyQt-Fluent-Widgets) - 现代化UI组件库
- [Playwright](https://playwright.dev/) - 浏览器自动化框架
- [ddddocr](https://github.com/sml2h3/ddddocr) - OCR识别引擎

---

**⭐ 如果这个项目对你有帮助，请给我们一个星标！**
