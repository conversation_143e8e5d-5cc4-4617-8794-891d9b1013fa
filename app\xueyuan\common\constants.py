# coding:utf-8
"""
学习工具常量定义

该模块定义了学习工具中使用的各种常量，包括：
- API地址常量
- 状态常量
- 配置常量
- 错误代码常量

主要常量：
- TARGET_APIS: 目标API列表
- USER_STATUS: 用户状态枚举
- COURSE_STATUS: 课程状态枚举
- LOG_LEVELS: 日志级别
"""

# API地址常量
TARGET_APIS = [
    "https://study.jxgbwlxy.gov.cn/api/report/myData/online",      # 用户学习状态API
    "https://study.jxgbwlxy.gov.cn/api/study/student/studentArchives/student"  # 学生档案API
]

# 网站地址常量
BASE_URL = "https://study.jxgbwlxy.gov.cn"
LOGIN_URL = "https://study.jxgbwlxy.gov.cn/index"
STUDY_DATA_URL = "https://study.jxgbwlxy.gov.cn/study/data"

# 用户状态常量
class UserStatus:
    NOT_STARTED = "未开始"
    LOGGING_IN = "登录中"
    LOGGED_IN = "已登录"
    STUDYING = "学习中"
    COMPLETED = "已完成"
    ERROR = "错误"
    PAUSED = "已暂停"

# 课程状态常量
class CourseStatus:
    NOT_STARTED = "0"
    IN_PROGRESS = "1"
    COMPLETED = "2"

# 课程类型常量
class CourseType:
    COMPULSORY = "必修课"
    ELECTIVE = "选修课"

# 完成状态常量
class CompleteStatus:
    INCOMPLETE = "0"
    COMPLETE = "1"

# 日志级别常量
class LogLevel:
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"

# 浏览器类型常量
class BrowserType:
    CHROME = "chrome"
    FIREFOX = "firefox"
    EDGE = "edge"

# OCR引擎类型常量
class OCREngine:
    DDDDOCR = "ddddocr"
    BAIDU = "baidu"

# 任务状态常量
class TaskStatus:
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

# 错误代码常量
class ErrorCode:
    SUCCESS = 0
    LOGIN_FAILED = 1001
    CAPTCHA_FAILED = 1002
    NETWORK_ERROR = 1003
    OCR_ERROR = 1004
    DATABASE_ERROR = 1005
    CONFIG_ERROR = 1006
    UNKNOWN_ERROR = 9999

# 默认配置常量
class DefaultConfig:
    CONCURRENT_COUNT = 2
    DELAY_TIME = 1
    RETRY_COUNT = 3
    API_TIMEOUT = 30
    OCR_TIMEOUT = 10
    LOG_MAX_DAYS = 30
    LOG_MAX_SIZE_MB = 100

# 数据库表名常量
class TableName:
    USERS = "users"
    COURSES = "courses"
    LOGS = "logs"
    API_DATA = "api_data"

# 选择器常量（用于网页元素定位）
class Selector:
    PHONE_INPUT = '.el-input__inner[placeholder="您的手机号"]'
    PASSWORD_INPUT = '.el-input__inner[placeholder="请输入密码"]'
    CAPTCHA_INPUT = '.el-input__inner[placeholder="验证码"]'
    LOGIN_BUTTON = '.loginBtn.el-button'
    CAPTCHA_IMAGE = '.captcha-img'
