# coding:utf-8
"""
关于界面组件

该模块提供应用程序的关于界面，显示版本信息、
开发团队、许可证等信息。

主要功能：
- 版本信息显示
- 开发团队信息
- 许可证信息
- 系统信息
- 更新日志

类说明：
- AboutInterface: 关于界面类
"""

from PySide6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QLabel, QTextEdit
from PySide6.QtCore import Qt, QUrl
from PySide6.QtGui import QPixmap, QFont, QDesktopServices
from qfluentwidgets import (ScrollArea, CardWidget, IconWidget, BodyLabel, CaptionLabel,
                           StrongBodyLabel, SubtitleLabel, TitleLabel, HyperlinkButton,
                           PushButton, FluentIcon as FIF, InfoBar, InfoBarPosition,
                           TextEdit, PlainTextEdit)
import platform
import sys
from datetime import datetime
from typing import Dict, Any

from ..logging import log_manager


class AboutInterface(ScrollArea):
    """
    关于界面类
    
    显示应用程序的详细信息
    """
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self.parent_window = parent
        
        self._init_ui()
    
    def _init_ui(self):
        """初始化界面"""
        # 设置滚动区域
        self.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.setViewportMargins(0, 20, 0, 20)
        self.setWidget(self._create_content_widget())
        self.setWidgetResizable(True)
        
        # 设置样式
        self.setObjectName('aboutInterface')
        self.setStyleSheet("""
            QScrollArea {
                background-color: transparent;
                border: none;
            }
        """)
    
    def _create_content_widget(self) -> QWidget:
        """创建内容组件"""
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setSpacing(20)
        content_layout.setContentsMargins(40, 0, 40, 0)
        
        # 应用信息卡片
        content_layout.addWidget(self._create_app_info_card())
        
        # 功能特性卡片
        content_layout.addWidget(self._create_features_card())
        
        # 开发团队卡片
        content_layout.addWidget(self._create_team_card())
        
        # 技术栈卡片
        content_layout.addWidget(self._create_tech_stack_card())
        
        # 系统信息卡片
        content_layout.addWidget(self._create_system_info_card())
        
        # 许可证卡片
        content_layout.addWidget(self._create_license_card())
        
        # 更新日志卡片
        content_layout.addWidget(self._create_changelog_card())
        
        # 添加弹性空间
        content_layout.addStretch()
        
        return content_widget
    
    def _create_app_info_card(self) -> CardWidget:
        """创建应用信息卡片"""
        card = CardWidget()
        card_layout = QVBoxLayout(card)
        card_layout.setSpacing(15)
        card_layout.setContentsMargins(30, 25, 30, 25)
        
        # 应用图标和标题
        header_layout = QHBoxLayout()
        
        # 应用图标
        app_icon = IconWidget(FIF.EDUCATION)
        app_icon.setFixedSize(64, 64)
        header_layout.addWidget(app_icon)
        
        # 标题信息
        title_layout = QVBoxLayout()
        title_layout.setSpacing(5)
        
        app_title = TitleLabel("学习工具")
        app_title.setFont(QFont("Microsoft YaHei", 24, QFont.Bold))
        title_layout.addWidget(app_title)
        
        app_subtitle = SubtitleLabel("自动化学习辅助系统")
        app_subtitle.setStyleSheet("color: rgba(0, 0, 0, 0.6);")
        title_layout.addWidget(app_subtitle)
        
        version_label = BodyLabel("版本 1.0.0")
        version_label.setStyleSheet("color: rgba(0, 0, 0, 0.5);")
        title_layout.addWidget(version_label)
        
        header_layout.addLayout(title_layout)
        header_layout.addStretch()
        
        card_layout.addLayout(header_layout)
        
        # 应用描述
        description = BodyLabel(
            "学习工具是一个基于PySide6和qfluentwidgets开发的自动化学习辅助系统，"
            "集成了OCR识别、浏览器自动化、API捕获等功能，为用户提供高效的学习体验。"
        )
        description.setWordWrap(True)
        description.setStyleSheet("color: rgba(0, 0, 0, 0.7); line-height: 1.5;")
        card_layout.addWidget(description)
        
        # 按钮组
        button_layout = QHBoxLayout()
        
        github_btn = HyperlinkButton(
            "https://github.com/your-repo",
            "GitHub 仓库",
            FIF.GITHUB
        )
        button_layout.addWidget(github_btn)
        
        docs_btn = HyperlinkButton(
            "https://your-docs-site.com",
            "使用文档",
            FIF.DOCUMENT
        )
        button_layout.addWidget(docs_btn)
        
        feedback_btn = PushButton("反馈建议", FIF.FEEDBACK)
        feedback_btn.clicked.connect(self._open_feedback)
        button_layout.addWidget(feedback_btn)
        
        button_layout.addStretch()
        card_layout.addLayout(button_layout)
        
        return card
    
    def _create_features_card(self) -> CardWidget:
        """创建功能特性卡片"""
        card = CardWidget()
        card_layout = QVBoxLayout(card)
        card_layout.setSpacing(15)
        card_layout.setContentsMargins(30, 25, 30, 25)
        
        # 标题
        title = StrongBodyLabel("主要功能")
        card_layout.addWidget(title)
        
        # 功能列表
        features = [
            ("用户管理", "支持批量用户管理，用户状态跟踪", FIF.PEOPLE),
            ("自动化学习", "基于Playwright的浏览器自动化", FIF.PLAY),
            ("OCR识别", "集成Ddddocr和百度OCR双引擎", FIF.CAMERA),
            ("API捕获", "智能API请求捕获和数据提取", FIF.CAPTURE),
            ("课程管理", "课程进度跟踪和学习监控", FIF.BOOK_SHELF),
            ("并发控制", "多任务并发执行和资源管理", FIF.DEVELOPER_TOOLS),
            ("日志系统", "多级别日志记录和实时查看", FIF.HISTORY),
            ("数据导出", "支持多种格式的数据导出", FIF.SAVE)
        ]
        
        for name, desc, icon in features:
            feature_layout = QHBoxLayout()
            
            feature_icon = IconWidget(icon)
            feature_icon.setFixedSize(20, 20)
            feature_layout.addWidget(feature_icon)
            
            feature_text_layout = QVBoxLayout()
            feature_text_layout.setSpacing(2)
            
            feature_name = BodyLabel(name)
            feature_name.setStyleSheet("font-weight: bold;")
            feature_text_layout.addWidget(feature_name)
            
            feature_desc = CaptionLabel(desc)
            feature_desc.setStyleSheet("color: rgba(0, 0, 0, 0.6);")
            feature_text_layout.addWidget(feature_desc)
            
            feature_layout.addLayout(feature_text_layout)
            feature_layout.addStretch()
            
            card_layout.addLayout(feature_layout)
        
        return card
    
    def _create_team_card(self) -> CardWidget:
        """创建开发团队卡片"""
        card = CardWidget()
        card_layout = QVBoxLayout(card)
        card_layout.setSpacing(15)
        card_layout.setContentsMargins(30, 25, 30, 25)
        
        # 标题
        title = StrongBodyLabel("开发团队")
        card_layout.addWidget(title)
        
        # 团队信息
        team_info = BodyLabel(
            "本项目由学习工具开发团队开发和维护，致力于为用户提供高质量的自动化学习解决方案。"
        )
        team_info.setWordWrap(True)
        team_info.setStyleSheet("color: rgba(0, 0, 0, 0.7);")
        card_layout.addWidget(team_info)
        
        # 联系方式
        contact_layout = QHBoxLayout()
        
        email_btn = HyperlinkButton(
            "mailto:<EMAIL>",
            "联系我们",
            FIF.MAIL
        )
        contact_layout.addWidget(email_btn)
        
        contact_layout.addStretch()
        card_layout.addLayout(contact_layout)
        
        return card
    
    def _create_tech_stack_card(self) -> CardWidget:
        """创建技术栈卡片"""
        card = CardWidget()
        card_layout = QVBoxLayout(card)
        card_layout.setSpacing(15)
        card_layout.setContentsMargins(30, 25, 30, 25)
        
        # 标题
        title = StrongBodyLabel("技术栈")
        card_layout.addWidget(title)
        
        # 技术栈列表
        tech_stack = [
            "PySide6 - 现代化的Python GUI框架",
            "qfluentwidgets - Fluent Design风格的UI组件库",
            "Playwright - 现代化的浏览器自动化工具",
            "SQLite3 - 轻量级的嵌入式数据库",
            "Ddddocr - 高效的OCR识别引擎",
            "百度OCR - 云端OCR识别服务",
            "asyncio - 异步编程支持",
            "threading - 多线程并发处理"
        ]
        
        for tech in tech_stack:
            tech_label = BodyLabel(f"• {tech}")
            tech_label.setStyleSheet("color: rgba(0, 0, 0, 0.7);")
            card_layout.addWidget(tech_label)
        
        return card
    
    def _create_system_info_card(self) -> CardWidget:
        """创建系统信息卡片"""
        card = CardWidget()
        card_layout = QVBoxLayout(card)
        card_layout.setSpacing(15)
        card_layout.setContentsMargins(30, 25, 30, 25)
        
        # 标题
        title = StrongBodyLabel("系统信息")
        card_layout.addWidget(title)
        
        # 系统信息
        system_info = self._get_system_info()
        
        for key, value in system_info.items():
            info_layout = QHBoxLayout()
            
            key_label = BodyLabel(f"{key}:")
            key_label.setFixedWidth(120)
            key_label.setStyleSheet("font-weight: bold;")
            info_layout.addWidget(key_label)
            
            value_label = BodyLabel(str(value))
            value_label.setStyleSheet("color: rgba(0, 0, 0, 0.7);")
            info_layout.addWidget(value_label)
            
            info_layout.addStretch()
            card_layout.addLayout(info_layout)
        
        return card
    
    def _create_license_card(self) -> CardWidget:
        """创建许可证卡片"""
        card = CardWidget()
        card_layout = QVBoxLayout(card)
        card_layout.setSpacing(15)
        card_layout.setContentsMargins(30, 25, 30, 25)
        
        # 标题
        title = StrongBodyLabel("许可证")
        card_layout.addWidget(title)
        
        # 许可证信息
        license_text = BodyLabel(
            "本软件基于MIT许可证发布，允许自由使用、修改和分发。"
            "详细的许可证条款请参阅项目根目录下的LICENSE文件。"
        )
        license_text.setWordWrap(True)
        license_text.setStyleSheet("color: rgba(0, 0, 0, 0.7);")
        card_layout.addWidget(license_text)
        
        # 许可证链接
        license_btn = HyperlinkButton(
            "https://opensource.org/licenses/MIT",
            "查看MIT许可证",
            FIF.CERTIFICATE
        )
        card_layout.addWidget(license_btn)
        
        return card
    
    def _create_changelog_card(self) -> CardWidget:
        """创建更新日志卡片"""
        card = CardWidget()
        card_layout = QVBoxLayout(card)
        card_layout.setSpacing(15)
        card_layout.setContentsMargins(30, 25, 30, 25)
        
        # 标题
        title = StrongBodyLabel("更新日志")
        card_layout.addWidget(title)
        
        # 更新日志内容
        changelog_text = PlainTextEdit()
        changelog_text.setPlainText(self._get_changelog())
        changelog_text.setMaximumHeight(200)
        changelog_text.setReadOnly(True)
        changelog_text.setStyleSheet("""
            QPlainTextEdit {
                background-color: rgba(0, 0, 0, 0.05);
                border: 1px solid rgba(0, 0, 0, 0.1);
                border-radius: 6px;
                padding: 10px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 12px;
            }
        """)
        card_layout.addWidget(changelog_text)
        
        return card
    
    def _get_system_info(self) -> Dict[str, Any]:
        """获取系统信息"""
        return {
            "操作系统": f"{platform.system()} {platform.release()}",
            "Python版本": f"{sys.version.split()[0]}",
            "架构": platform.machine(),
            "处理器": platform.processor() or "未知",
            "启动时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
    
    def _get_changelog(self) -> str:
        """获取更新日志"""
        return """v1.0.0 (2025-01-14)
• 初始版本发布
• 实现用户管理功能
• 集成OCR识别引擎
• 添加浏览器自动化支持
• 实现API捕获系统
• 添加课程学习流程
• 实现线程池和并发控制
• 添加多级别日志系统
• 完善界面设计和用户体验

v0.9.0 (2025-01-10)
• Beta版本发布
• 基础功能实现
• 界面框架搭建
• 数据库系统设计

v0.1.0 (2025-01-01)
• 项目启动
• 技术栈选型
• 架构设计"""
    
    def _open_feedback(self):
        """打开反馈页面"""
        QDesktopServices.openUrl(QUrl("https://github.com/your-repo/issues"))
    
    def refresh(self):
        """刷新界面"""
        # 重新获取系统信息等
        pass
