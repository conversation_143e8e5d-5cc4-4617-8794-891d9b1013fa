# coding:utf-8
"""
学习工具配置加载器

该模块负责加载和管理学习工具的配置文件，
将学习工具配置集成到现有的配置系统中。

主要功能：
- 加载学习工具配置文件
- 初始化配置目录和文件
- 提供配置访问接口
- 配置文件备份和恢复

类和函数说明：
- StudyConfigLoader: 配置加载器类
- init_study_config(): 初始化学习工具配置
- get_study_config(): 获取学习工具配置实例
"""

import os
import json
import shutil
from pathlib import Path
from qfluentwidgets import qconfig

from .config import StudyConfig, study_cfg


class StudyConfigLoader:
    """学习工具配置加载器"""
    
    def __init__(self):
        self.config_dir = Path("data/config")
        self.config_file = self.config_dir / "study_config.json"
        self.template_file = Path("app/xueyuan/config/study_config.json")
        
    def init_config_dir(self):
        """初始化配置目录"""
        # 确保配置目录存在
        self.config_dir.mkdir(parents=True, exist_ok=True)
        
        # 如果配置文件不存在，从模板复制
        if not self.config_file.exists() and self.template_file.exists():
            shutil.copy2(self.template_file, self.config_file)
            print(f"[配置] 已创建学习工具配置文件: {self.config_file}")
    
    def load_config(self):
        """加载学习工具配置"""
        try:
            self.init_config_dir()
            
            # 使用qconfig加载配置文件
            qconfig.load(str(self.config_file), study_cfg)
            print(f"[配置] 已加载学习工具配置: {self.config_file}")
            
            return True
        except Exception as e:
            print(f"[配置] 加载学习工具配置失败: {e}")
            return False
    
    def save_config(self):
        """保存学习工具配置"""
        try:
            qconfig.save(str(self.config_file), study_cfg)
            print(f"[配置] 已保存学习工具配置: {self.config_file}")
            return True
        except Exception as e:
            print(f"[配置] 保存学习工具配置失败: {e}")
            return False
    
    def backup_config(self, backup_name=None):
        """备份配置文件"""
        try:
            if not self.config_file.exists():
                return False
                
            if backup_name is None:
                from datetime import datetime
                backup_name = f"study_config_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            
            backup_file = self.config_dir / backup_name
            shutil.copy2(self.config_file, backup_file)
            print(f"[配置] 已备份配置文件: {backup_file}")
            return True
        except Exception as e:
            print(f"[配置] 备份配置文件失败: {e}")
            return False
    
    def restore_config(self, backup_file):
        """恢复配置文件"""
        try:
            backup_path = Path(backup_file)
            if not backup_path.exists():
                backup_path = self.config_dir / backup_file
            
            if backup_path.exists():
                shutil.copy2(backup_path, self.config_file)
                print(f"[配置] 已恢复配置文件: {backup_path}")
                return True
            else:
                print(f"[配置] 备份文件不存在: {backup_path}")
                return False
        except Exception as e:
            print(f"[配置] 恢复配置文件失败: {e}")
            return False


# 全局配置加载器实例
_config_loader = StudyConfigLoader()


def init_study_config():
    """初始化学习工具配置"""
    return _config_loader.load_config()


def get_study_config():
    """获取学习工具配置实例"""
    return study_cfg


def save_study_config():
    """保存学习工具配置"""
    return _config_loader.save_config()


def backup_study_config(backup_name=None):
    """备份学习工具配置"""
    return _config_loader.backup_config(backup_name)


def restore_study_config(backup_file):
    """恢复学习工具配置"""
    return _config_loader.restore_config(backup_file)
