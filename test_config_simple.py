# coding:utf-8
"""
简化的配置系统测试脚本

测试学习工具配置系统的基本功能，包括：
- 配置项的创建和访问
- 配置值的读取和设置
- 配置验证器的工作
- 配置序列化器的工作

使用方法：
    python test_config_simple.py
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_basic_config():
    """测试基本配置功能"""
    print("=" * 50)
    print("测试基本配置功能...")
    
    try:
        from app.xueyuan.common.config import study_cfg
        
        print("✓ 学习工具配置导入成功")
        
        # 测试基本配置项访问
        print(f"  - 异步登录: {study_cfg.asyncLogin.value}")
        print(f"  - 并发数量: {study_cfg.concurrentCount.value}")
        print(f"  - 浏览器类型: {study_cfg.browserType.value}")
        print(f"  - OCR引擎: {study_cfg.primaryEngine.value}")
        
        # 测试配置项设置
        original_value = study_cfg.concurrentCount.value
        study_cfg.concurrentCount.value = 3
        print(f"  - 设置并发数量为3: {study_cfg.concurrentCount.value}")
        
        # 恢复原值
        study_cfg.concurrentCount.value = original_value
        print(f"  - 恢复并发数量: {study_cfg.concurrentCount.value}")
        
        return True
        
    except Exception as e:
        print(f"✗ 基本配置测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_validation():
    """测试配置验证"""
    print("=" * 50)
    print("测试配置验证...")
    
    try:
        from app.xueyuan.common.config import study_cfg
        
        # 测试范围验证器
        original_value = study_cfg.concurrentCount.value
        
        # 尝试设置有效值
        study_cfg.concurrentCount.value = 5
        print(f"✓ 设置有效值5: {study_cfg.concurrentCount.value}")
        
        # 尝试设置无效值（应该被验证器修正）
        try:
            study_cfg.concurrentCount.value = 15  # 超出范围1-10
            print(f"  - 设置无效值15后的实际值: {study_cfg.concurrentCount.value}")
        except Exception as e:
            print(f"  - 设置无效值被拒绝: {e}")
        
        # 恢复原值
        study_cfg.concurrentCount.value = original_value
        
        return True
        
    except Exception as e:
        print(f"✗ 配置验证测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_utils():
    """测试配置工具"""
    print("=" * 50)
    print("测试配置工具...")
    
    try:
        from app.xueyuan.common.config_utils import get_config_summary, is_config_valid
        
        # 测试配置摘要
        summary = get_config_summary()
        print("✓ 配置摘要获取成功:")
        for section, data in summary.items():
            print(f"  - {section}: {data}")
        
        # 测试配置验证
        is_valid = is_config_valid()
        print(f"✓ 配置有效性检查: {'有效' if is_valid else '无效'}")
        
        return True
        
    except Exception as e:
        print(f"✗ 配置工具测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_color_config():
    """测试颜色配置"""
    print("=" * 50)
    print("测试颜色配置...")
    
    try:
        from app.xueyuan.common.config import study_cfg
        
        # 测试颜色配置项
        print(f"✓ 主题颜色: {study_cfg.themeColor.value}")
        
        # 尝试设置新颜色
        original_color = study_cfg.themeColor.value
        study_cfg.themeColor.value = "#ff0000"  # 红色
        print(f"  - 设置为红色: {study_cfg.themeColor.value}")
        
        # 恢复原色
        study_cfg.themeColor.value = original_color
        print(f"  - 恢复原色: {study_cfg.themeColor.value}")
        
        return True
        
    except Exception as e:
        print(f"✗ 颜色配置测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("学习工具配置系统测试")
    print("=" * 50)
    
    tests = [
        ("基本配置功能", test_basic_config),
        ("配置验证", test_config_validation),
        ("配置工具", test_config_utils),
        ("颜色配置", test_color_config),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 输出测试结果
    print("=" * 50)
    print("测试结果汇总:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！配置系统工作正常！")
        return 0
    else:
        print("⚠ 部分测试失败，请检查配置系统")
        return 1

if __name__ == "__main__":
    sys.exit(main())
