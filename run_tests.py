#!/usr/bin/env python
# coding:utf-8
"""
测试运行脚本

提供便捷的测试运行命令，支持不同类型的测试。

使用方法：
    python run_tests.py --help
    python run_tests.py unit
    python run_tests.py integration
    python run_tests.py performance
    python run_tests.py all
"""

import argparse
import subprocess
import sys
import os
from pathlib import Path


def run_command(cmd, description):
    """运行命令并显示结果"""
    print(f"\n{'='*60}")
    print(f"运行: {description}")
    print(f"命令: {' '.join(cmd)}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=False)
        print(f"\n✅ {description} 完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"\n❌ {description} 失败 (退出码: {e.returncode})")
        return False
    except Exception as e:
        print(f"\n❌ {description} 异常: {e}")
        return False


def run_unit_tests():
    """运行单元测试"""
    cmd = [
        sys.executable, "-m", "pytest",
        "tests/unit/",
        "-m", "unit",
        "--tb=short",
        "-v"
    ]
    return run_command(cmd, "单元测试")


def run_integration_tests():
    """运行集成测试"""
    cmd = [
        sys.executable, "-m", "pytest",
        "tests/integration/",
        "-m", "integration",
        "--tb=short",
        "-v"
    ]
    return run_command(cmd, "集成测试")


def run_performance_tests():
    """运行性能测试"""
    cmd = [
        sys.executable, "-m", "pytest",
        "tests/performance/",
        "-m", "performance",
        "--tb=short",
        "-v",
        "-s"  # 显示print输出
    ]
    return run_command(cmd, "性能测试")


def run_all_tests():
    """运行所有测试"""
    cmd = [
        sys.executable, "-m", "pytest",
        "tests/",
        "--tb=short",
        "-v"
    ]
    return run_command(cmd, "所有测试")


def run_fast_tests():
    """运行快速测试（排除慢速测试）"""
    cmd = [
        sys.executable, "-m", "pytest",
        "tests/",
        "-m", "not slow",
        "--tb=short",
        "-v"
    ]
    return run_command(cmd, "快速测试")


def run_coverage_tests():
    """运行测试并生成覆盖率报告"""
    # 检查是否安装了pytest-cov
    try:
        import pytest_cov
    except ImportError:
        print("❌ 需要安装 pytest-cov: pip install pytest-cov")
        return False
    
    cmd = [
        sys.executable, "-m", "pytest",
        "tests/",
        "--cov=app",
        "--cov-report=html",
        "--cov-report=term-missing",
        "--tb=short",
        "-v"
    ]
    return run_command(cmd, "覆盖率测试")


def check_environment():
    """检查测试环境"""
    print("检查测试环境...")
    
    # 检查Python版本
    python_version = sys.version_info
    print(f"Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version < (3, 8):
        print("❌ 需要Python 3.8或更高版本")
        return False
    
    # 检查必要的包
    required_packages = [
        "pytest",
        "PySide6",
        "qfluentwidgets",
        "playwright",
        "ddddocr",
        "psutil"
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} (未安装)")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n需要安装缺失的包:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    # 检查测试目录
    test_dir = Path("tests")
    if not test_dir.exists():
        print("❌ 测试目录不存在")
        return False
    
    print("✅ 测试环境检查通过")
    return True


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="学习工具测试运行脚本",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
测试类型说明:
  unit        - 单元测试 (快速，测试单个模块)
  integration - 集成测试 (中等速度，测试模块间协作)
  performance - 性能测试 (慢速，测试系统性能)
  fast        - 快速测试 (排除标记为slow的测试)
  coverage    - 覆盖率测试 (生成代码覆盖率报告)
  all         - 所有测试
  check       - 检查测试环境

示例:
  python run_tests.py unit
  python run_tests.py integration
  python run_tests.py performance
  python run_tests.py coverage
        """
    )
    
    parser.add_argument(
        "test_type",
        choices=["unit", "integration", "performance", "fast", "coverage", "all", "check"],
        help="要运行的测试类型"
    )
    
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="详细输出"
    )
    
    parser.add_argument(
        "--failfast", "-x",
        action="store_true",
        help="遇到第一个失败就停止"
    )
    
    args = parser.parse_args()
    
    # 设置工作目录
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    # 检查环境
    if args.test_type == "check":
        success = check_environment()
        sys.exit(0 if success else 1)
    
    if not check_environment():
        print("\n❌ 环境检查失败，请先解决上述问题")
        sys.exit(1)
    
    # 运行测试
    success = False
    
    if args.test_type == "unit":
        success = run_unit_tests()
    elif args.test_type == "integration":
        success = run_integration_tests()
    elif args.test_type == "performance":
        success = run_performance_tests()
    elif args.test_type == "fast":
        success = run_fast_tests()
    elif args.test_type == "coverage":
        success = run_coverage_tests()
    elif args.test_type == "all":
        success = run_all_tests()
    
    # 输出结果
    if success:
        print(f"\n🎉 {args.test_type} 测试全部通过!")
        sys.exit(0)
    else:
        print(f"\n💥 {args.test_type} 测试失败!")
        sys.exit(1)


if __name__ == "__main__":
    main()
