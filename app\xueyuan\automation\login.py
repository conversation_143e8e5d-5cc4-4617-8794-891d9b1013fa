# coding:utf-8
"""
登录处理器

该模块提供自动登录功能，包括表单填写、验证码处理、
登录状态检查等。

主要功能：
- 自动填写登录表单
- 验证码识别和输入
- 登录状态检查
- 登录重试机制

类说明：
- LoginHandler: 登录处理器类
"""

import asyncio
import time
from typing import Optional, Dict, Any
from playwright.async_api import Page

from ..common.config_loader import get_study_config
from ...ocr.manager import ocr_manager


class LoginHandler:
    """
    登录处理器类
    
    提供自动登录功能，支持验证码识别和登录状态检查
    """
    
    def __init__(self, page: Optional[Page] = None):
        """
        初始化登录处理器
        
        Args:
            page: Playwright页面对象
        """
        self.page = page
        self.is_logged_in = False
        self.current_user = {}
        
        # 配置
        cfg = get_study_config()
        self.login_url = cfg.get(cfg.loginUrl)
        self.base_url = cfg.get(cfg.baseUrl)
        self.retry_count = cfg.get(cfg.retryCount)
        
        # 登录选择器
        self.selectors = {
            'phone_input': '.el-input__inner[placeholder="您的手机号"]',
            'password_input': '.el-input__inner[placeholder="请输入密码"]',
            'captcha_input': '.el-input__inner[placeholder="验证码"]',
            'captcha_image': '.captcha-img img',
            'login_button': '.loginBtn.el-button',
            'error_message': '.el-message--error'
        }
        
        print("[登录] 登录处理器初始化完成")
    
    def set_page(self, page: Page):
        """
        设置Playwright页面对象
        
        Args:
            page: Playwright页面对象
        """
        self.page = page
        print("[登录] 页面对象已设置")
    
    async def login(self, phone: str, password: str, auto_captcha: bool = True) -> bool:
        """
        执行登录
        
        Args:
            phone: 手机号
            password: 密码
            auto_captcha: 是否自动识别验证码
            
        Returns:
            bool: 登录是否成功
        """
        if not self.page:
            print("[登录] 页面对象未设置")
            return False
        
        print(f"[登录] 开始登录用户: {phone}")
        
        for attempt in range(self.retry_count):
            try:
                print(f"[登录] 登录尝试 {attempt + 1}/{self.retry_count}")
                
                # 导航到登录页面
                if not await self._navigate_to_login():
                    continue
                
                # 填写登录信息
                if not await self._fill_login_form(phone, password):
                    continue
                
                # 处理验证码
                if not await self._handle_captcha(auto_captcha):
                    continue
                
                # 提交登录
                if not await self._submit_login():
                    continue
                
                # 检查登录结果
                if await self._check_login_success():
                    self.is_logged_in = True
                    self.current_user = {'phone': phone}
                    print(f"[登录] 用户 {phone} 登录成功")
                    return True
                else:
                    print(f"[登录] 登录失败，尝试 {attempt + 1}")
                    await asyncio.sleep(2)
                    
            except Exception as e:
                print(f"[登录] 登录异常: {e}")
                await asyncio.sleep(2)
        
        print(f"[登录] 用户 {phone} 登录失败，已达到最大重试次数")
        return False
    
    async def _navigate_to_login(self) -> bool:
        """
        导航到登录页面
        
        Returns:
            bool: 导航是否成功
        """
        try:
            print(f"[登录] 导航到登录页面: {self.login_url}")
            await self.page.goto(self.login_url)
            await self.page.wait_for_load_state("networkidle")
            
            # 检查是否已经登录
            current_url = self.page.url
            if "/study/data" in current_url:
                print("[登录] 用户已登录")
                self.is_logged_in = True
                return True
            
            # 等待登录表单加载
            await self.page.wait_for_selector(self.selectors['phone_input'], timeout=10000)
            print("[登录] 登录页面加载完成")
            return True
            
        except Exception as e:
            print(f"[登录] 导航到登录页面失败: {e}")
            return False
    
    async def _fill_login_form(self, phone: str, password: str) -> bool:
        """
        填写登录表单
        
        Args:
            phone: 手机号
            password: 密码
            
        Returns:
            bool: 填写是否成功
        """
        try:
            print("[登录] 填写登录表单")
            
            # 清空并填写手机号
            await self.page.fill(self.selectors['phone_input'], "")
            await self.page.fill(self.selectors['phone_input'], phone)
            
            # 清空并填写密码
            await self.page.fill(self.selectors['password_input'], "")
            await self.page.fill(self.selectors['password_input'], password)
            
            print("[登录] 登录表单填写完成")
            return True
            
        except Exception as e:
            print(f"[登录] 填写登录表单失败: {e}")
            return False
    
    async def _handle_captcha(self, auto_captcha: bool = True) -> bool:
        """
        处理验证码
        
        Args:
            auto_captcha: 是否自动识别验证码
            
        Returns:
            bool: 验证码处理是否成功
        """
        try:
            # 检查是否有验证码
            captcha_input = await self.page.query_selector(self.selectors['captcha_input'])
            if not captcha_input:
                print("[登录] 无需验证码")
                return True
            
            print("[登录] 需要处理验证码")
            
            if auto_captcha:
                return await self._auto_recognize_captcha()
            else:
                return await self._manual_input_captcha()
                
        except Exception as e:
            print(f"[登录] 验证码处理失败: {e}")
            return False
    
    async def _auto_recognize_captcha(self) -> bool:
        """
        自动识别验证码
        
        Returns:
            bool: 识别是否成功
        """
        try:
            print("[登录] 开始自动识别验证码")
            
            # 获取验证码图片
            captcha_img = await self.page.query_selector(self.selectors['captcha_image'])
            if not captcha_img:
                print("[登录] 未找到验证码图片")
                return False
            
            # 截取验证码图片
            captcha_data = await captcha_img.screenshot()
            
            # 使用OCR识别验证码
            if not ocr_manager.is_initialized:
                await ocr_manager.initialize()
            
            result = ocr_manager.recognize_text(captcha_data)
            if not result.success:
                print(f"[登录] 验证码识别失败: {result.error_message}")
                return False
            
            captcha_text = result.text.strip()
            print(f"[登录] 验证码识别结果: {captcha_text}")
            
            # 输入验证码
            await self.page.fill(self.selectors['captcha_input'], captcha_text)
            print("[登录] 验证码已输入")
            return True
            
        except Exception as e:
            print(f"[登录] 自动识别验证码失败: {e}")
            return False
    
    async def _manual_input_captcha(self) -> bool:
        """
        手动输入验证码
        
        Returns:
            bool: 输入是否成功
        """
        try:
            print("[登录] 请手动输入验证码")
            
            # 这里可以实现一个简单的输入机制
            # 或者等待用户手动输入
            await asyncio.sleep(30)  # 给用户30秒时间输入验证码
            
            # 检查验证码是否已输入
            captcha_value = await self.page.input_value(self.selectors['captcha_input'])
            if captcha_value:
                print(f"[登录] 验证码已输入: {captcha_value}")
                return True
            else:
                print("[登录] 验证码未输入")
                return False
                
        except Exception as e:
            print(f"[登录] 手动输入验证码失败: {e}")
            return False
    
    async def _submit_login(self) -> bool:
        """
        提交登录
        
        Returns:
            bool: 提交是否成功
        """
        try:
            print("[登录] 提交登录")
            
            # 点击登录按钮
            await self.page.click(self.selectors['login_button'])
            
            # 等待页面响应
            await asyncio.sleep(2)
            
            print("[登录] 登录请求已提交")
            return True
            
        except Exception as e:
            print(f"[登录] 提交登录失败: {e}")
            return False
    
    async def _check_login_success(self) -> bool:
        """
        检查登录是否成功
        
        Returns:
            bool: 登录是否成功
        """
        try:
            print("[登录] 检查登录结果")
            
            # 等待页面跳转或错误消息
            await asyncio.sleep(3)
            
            # 检查是否跳转到学习页面
            current_url = self.page.url
            if "/study/data" in current_url:
                print("[登录] 登录成功，已跳转到学习页面")
                return True
            
            # 检查是否有错误消息
            error_msg = await self.page.query_selector(self.selectors['error_message'])
            if error_msg:
                error_text = await error_msg.text_content()
                print(f"[登录] 登录失败: {error_text}")
                return False
            
            # 检查URL变化
            if current_url != self.login_url:
                print(f"[登录] 页面已跳转: {current_url}")
                return True
            
            print("[登录] 登录状态不明确，可能失败")
            return False
            
        except Exception as e:
            print(f"[登录] 检查登录结果失败: {e}")
            return False
    
    async def logout(self) -> bool:
        """
        退出登录
        
        Returns:
            bool: 退出是否成功
        """
        try:
            if not self.is_logged_in:
                print("[登录] 用户未登录")
                return True
            
            print("[登录] 开始退出登录")
            
            # 这里可以实现退出登录的逻辑
            # 例如：点击退出按钮、清除cookies等
            
            self.is_logged_in = False
            self.current_user = {}
            print("[登录] 退出登录成功")
            return True
            
        except Exception as e:
            print(f"[登录] 退出登录失败: {e}")
            return False
    
    async def check_login_status(self) -> bool:
        """
        检查当前登录状态
        
        Returns:
            bool: 是否已登录
        """
        if not self.page:
            return False
        
        try:
            current_url = self.page.url
            
            # 如果在学习页面，说明已登录
            if "/study/data" in current_url:
                self.is_logged_in = True
                return True
            
            # 如果在登录页面，说明未登录
            if self.login_url in current_url:
                self.is_logged_in = False
                return False
            
            return self.is_logged_in
            
        except Exception as e:
            print(f"[登录] 检查登录状态失败: {e}")
            return False
    
    def get_current_user(self) -> Dict[str, Any]:
        """
        获取当前用户信息
        
        Returns:
            Dict[str, Any]: 用户信息
        """
        return self.current_user.copy()
    
    def get_status(self) -> Dict[str, Any]:
        """
        获取登录状态
        
        Returns:
            Dict[str, Any]: 状态信息
        """
        return {
            "is_logged_in": self.is_logged_in,
            "current_user": self.current_user,
            "login_url": self.login_url,
            "retry_count": self.retry_count,
            "has_page": self.page is not None
        }
