# coding:utf-8
"""
学习控制界面

该模块定义了学习控制界面类 StudyControlInterface，
提供学习任务的启动、暂停、停止等控制功能。

主要功能：
- 显示学习进度和状态
- 控制学习任务的执行
- 实时更新学习进度
- 显示当前学习用户列表

类说明：
- StudyControlInterface: 学习控制界面类
"""

from PySide6.QtCore import Qt, QTimer, Signal
from PySide6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QTableWidgetItem
from qfluentwidgets import (CardWidget, HeaderCardWidget, BodyLabel, CaptionLabel,
                            PrimaryPushButton, PushButton, ProgressRing, ProgressBar,
                            TableWidget, FluentIcon as FIF, InfoBar, InfoBarPosition)

from ..database.dao import DAOFactory
from ..common.constants import UserStatus


class StudyControlInterface(QWidget):
    """学习控制界面类"""
    
    # 定义信号
    startStudySignal = Signal()
    pauseStudySignal = Signal()
    stopStudySignal = Signal()
    
    def __init__(self, parent=None):
        """初始化学习控制界面"""
        super().__init__(parent)
        self.setObjectName("StudyControlInterface")
        
        # 状态变量
        self.is_studying = False
        self.is_paused = False
        self.total_users = 0
        self.completed_users = 0
        self.current_progress = 0
        
        # 初始化界面
        self.initWidget()
        self.initLayout()
        self.connectSignalToSlot()
        
        # 启动定时器更新状态
        self.updateTimer = QTimer()
        self.updateTimer.timeout.connect(self.updateStatus)
        self.updateTimer.start(2000)  # 每2秒更新一次

    def initWidget(self):
        """初始化界面组件"""
        # 创建主布局
        self.vBoxLayout = QVBoxLayout(self)
        
        # 创建控制面板卡片
        self.controlCard = HeaderCardWidget(self)
        self.controlCard.setTitle("学习控制面板")
        self.controlCard.headerLabel.setStyleSheet("font-size: 18px; font-weight: bold;")
        
        # 创建控制面板内容
        self.controlWidget = QWidget()
        self.controlLayout = QHBoxLayout(self.controlWidget)
        
        # 左侧进度区域
        self.progressWidget = QWidget()
        self.progressLayout = QVBoxLayout(self.progressWidget)
        
        # 进度环
        self.progressRing = ProgressRing()
        self.progressRing.setFixedSize(120, 120)
        self.progressRing.setValue(0)
        
        # 状态标签
        self.statusLabel = BodyLabel("就绪")
        self.statusLabel.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.statusLabel.setStyleSheet("font-size: 16px; font-weight: bold; color: #0078d4;")
        
        # 进度信息
        self.progressInfoLabel = CaptionLabel("0 / 0 用户")
        self.progressInfoLabel.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 添加到进度布局
        self.progressLayout.addWidget(self.progressRing, 0, Qt.AlignmentFlag.AlignCenter)
        self.progressLayout.addWidget(self.statusLabel)
        self.progressLayout.addWidget(self.progressInfoLabel)
        
        # 右侧控制按钮区域
        self.buttonWidget = QWidget()
        self.buttonLayout = QVBoxLayout(self.buttonWidget)
        
        # 控制按钮
        self.startBtn = PrimaryPushButton("启动学习", self)
        self.startBtn.setIcon(FIF.PLAY)
        self.startBtn.setFixedSize(120, 40)
        
        self.pauseBtn = PushButton("暂停学习", self)
        self.pauseBtn.setIcon(FIF.PAUSE)
        self.pauseBtn.setFixedSize(120, 40)
        self.pauseBtn.setEnabled(False)
        
        self.stopBtn = PushButton("停止学习", self)
        self.stopBtn.setIcon(FIF.CANCEL)
        self.stopBtn.setFixedSize(120, 40)
        self.stopBtn.setEnabled(False)
        
        # 添加到按钮布局
        self.buttonLayout.addWidget(self.startBtn)
        self.buttonLayout.addWidget(self.pauseBtn)
        self.buttonLayout.addWidget(self.stopBtn)
        self.buttonLayout.addStretch()
        
        # 添加到控制布局
        self.controlLayout.addWidget(self.progressWidget)
        self.controlLayout.addStretch()
        self.controlLayout.addWidget(self.buttonWidget)

        # 设置控制卡片内容
        self.controlCard.viewLayout.addWidget(self.controlWidget)
        
        # 创建统计信息卡片
        self.statsCard = HeaderCardWidget(self)
        self.statsCard.setTitle("学习统计")
        
        # 统计信息网格
        self.statsWidget = QWidget()
        self.statsLayout = QGridLayout(self.statsWidget)
        
        # 统计标签
        self.totalUsersLabel = BodyLabel("总用户数")
        self.totalUsersValue = BodyLabel("0")
        self.totalUsersValue.setStyleSheet("font-size: 24px; font-weight: bold; color: #0078d4;")
        
        self.waitingUsersLabel = BodyLabel("等待学习")
        self.waitingUsersValue = BodyLabel("0")
        self.waitingUsersValue.setStyleSheet("font-size: 24px; font-weight: bold; color: #ff8c00;")
        
        self.studyingUsersLabel = BodyLabel("学习中")
        self.studyingUsersValue = BodyLabel("0")
        self.studyingUsersValue.setStyleSheet("font-size: 24px; font-weight: bold; color: #32cd32;")
        
        self.completedUsersLabel = BodyLabel("已完成")
        self.completedUsersValue = BodyLabel("0")
        self.completedUsersValue.setStyleSheet("font-size: 24px; font-weight: bold; color: #228b22;")
        
        self.errorUsersLabel = BodyLabel("错误")
        self.errorUsersValue = BodyLabel("0")
        self.errorUsersValue.setStyleSheet("font-size: 24px; font-weight: bold; color: #dc143c;")
        
        # 添加到统计布局
        self.statsLayout.addWidget(self.totalUsersLabel, 0, 0)
        self.statsLayout.addWidget(self.totalUsersValue, 1, 0)
        self.statsLayout.addWidget(self.waitingUsersLabel, 0, 1)
        self.statsLayout.addWidget(self.waitingUsersValue, 1, 1)
        self.statsLayout.addWidget(self.studyingUsersLabel, 0, 2)
        self.statsLayout.addWidget(self.studyingUsersValue, 1, 2)
        self.statsLayout.addWidget(self.completedUsersLabel, 0, 3)
        self.statsLayout.addWidget(self.completedUsersValue, 1, 3)
        self.statsLayout.addWidget(self.errorUsersLabel, 0, 4)
        self.statsLayout.addWidget(self.errorUsersValue, 1, 4)

        # 设置统计卡片内容
        self.statsCard.viewLayout.addWidget(self.statsWidget)
        
        # 创建当前任务表格
        self.taskCard = HeaderCardWidget(self)
        self.taskCard.setTitle("当前任务")
        
        self.taskTable = TableWidget()
        self.taskTable.setColumnCount(5)
        self.taskTable.setHorizontalHeaderLabels(["手机号", "姓名", "状态", "进度", "错误信息"])
        # self.taskTable.setFixedHeight(300)

        self.taskCard.viewLayout.addWidget(self.taskTable)

    def initLayout(self):
        """初始化布局"""
        self.vBoxLayout.setContentsMargins(0, 0, 0, 0)
        self.vBoxLayout.setSpacing(20)
        
        self.vBoxLayout.addWidget(self.controlCard)
        self.vBoxLayout.addWidget(self.statsCard)
        self.vBoxLayout.addWidget(self.taskCard)

    def connectSignalToSlot(self):
        """连接信号到槽函数"""
        self.startBtn.clicked.connect(self.onStartStudy)
        self.pauseBtn.clicked.connect(self.onPauseStudy)
        self.stopBtn.clicked.connect(self.onStopStudy)

    def onStartStudy(self):
        """启动学习"""
        try:
            print("[学习控制] 用户点击启动学习按钮")

            if not self.is_studying:
                self.is_studying = True
                self.is_paused = False

                # 更新按钮状态
                self.startBtn.setEnabled(False)
                self.pauseBtn.setEnabled(True)
                self.stopBtn.setEnabled(True)

                # 更新状态
                self.statusLabel.setText("正在启动...")
                self.statusLabel.setStyleSheet("font-size: 16px; font-weight: bold; color: #ff8c00;")

                print("[学习控制] 发送启动学习信号")
                # 发送信号
                self.startStudySignal.emit()

                self.showInfoBar("正在启动学习任务...", "info")
            else:
                print("[学习控制] 学习任务已在运行中")
                self.showInfoBar("学习任务已在运行中", "warning")

        except Exception as e:
            print(f"[学习控制] 启动学习异常: {e}")
            self.showInfoBar(f"启动学习失败: {e}", "error")

    def onPauseStudy(self):
        """暂停/恢复学习"""
        try:
            print("[学习控制] 用户点击暂停/恢复学习按钮")

            if self.is_studying:
                if not self.is_paused:
                    # 暂停
                    print("[学习控制] 暂停学习")
                    self.is_paused = True
                    self.pauseBtn.setText("恢复学习")
                    self.pauseBtn.setIcon(FIF.PLAY)
                    self.statusLabel.setText("已暂停")
                    self.statusLabel.setStyleSheet("font-size: 16px; font-weight: bold; color: #ff8c00;")
                    self.showInfoBar("学习任务已暂停", "warning")
                else:
                    # 恢复
                    print("[学习控制] 恢复学习")
                    self.is_paused = False
                    self.pauseBtn.setText("暂停学习")
                    self.pauseBtn.setIcon(FIF.PAUSE)
                    self.statusLabel.setText("学习中")
                    self.statusLabel.setStyleSheet("font-size: 16px; font-weight: bold; color: #32cd32;")
                    self.showInfoBar("学习任务已恢复", "success")

                # 发送信号
                self.pauseStudySignal.emit()
            else:
                print("[学习控制] 学习任务未在运行")
                self.showInfoBar("学习任务未在运行", "warning")

        except Exception as e:
            print(f"[学习控制] 暂停/恢复学习异常: {e}")
            self.showInfoBar(f"暂停/恢复学习失败: {e}", "error")

    def onStopStudy(self):
        """停止学习"""
        try:
            print("[学习控制] 用户点击停止学习按钮")

            if self.is_studying:
                print("[学习控制] 停止学习任务")
                self.is_studying = False
                self.is_paused = False

                # 更新按钮状态
                self.startBtn.setEnabled(True)
                self.pauseBtn.setEnabled(False)
                self.stopBtn.setEnabled(False)
                self.pauseBtn.setText("暂停学习")
                self.pauseBtn.setIcon(FIF.PAUSE)

                # 更新状态
                self.statusLabel.setText("正在停止...")
                self.statusLabel.setStyleSheet("font-size: 16px; font-weight: bold; color: #ff8c00;")

                print("[学习控制] 发送停止学习信号")
                # 发送信号
                self.stopStudySignal.emit()

                self.showInfoBar("正在停止学习任务...", "info")
            else:
                print("[学习控制] 学习任务未在运行")
                self.showInfoBar("学习任务未在运行", "warning")

        except Exception as e:
            print(f"[学习控制] 停止学习异常: {e}")
            self.showInfoBar(f"停止学习失败: {e}", "error")

    def updateStatus(self):
        """更新状态信息"""
        try:
            # 获取用户统计信息
            user_dao = DAOFactory.get_user_dao()
            all_users = user_dao.get_all()
            
            # 统计各状态用户数
            total_count = len(all_users)
            waiting_count = len([u for u in all_users if u.status == UserStatus.NOT_STARTED])
            studying_count = len([u for u in all_users if u.status in [UserStatus.LOGGING_IN, UserStatus.STUDYING]])
            completed_count = len([u for u in all_users if u.status == UserStatus.COMPLETED])
            error_count = len([u for u in all_users if u.status == UserStatus.ERROR])
            
            # 更新统计标签
            self.totalUsersValue.setText(str(total_count))
            self.waitingUsersValue.setText(str(waiting_count))
            self.studyingUsersValue.setText(str(studying_count))
            self.completedUsersValue.setText(str(completed_count))
            self.errorUsersValue.setText(str(error_count))
            
            # 更新进度
            if total_count > 0:
                progress = int((completed_count / total_count) * 100)
                self.progressRing.setValue(progress)
                self.progressInfoLabel.setText(f"{completed_count} / {total_count} 用户")
            else:
                self.progressRing.setValue(0)
                self.progressInfoLabel.setText("0 / 0 用户")
            
            # 更新任务表格
            self.updateTaskTable(all_users)
            
        except Exception as e:
            print(f"[界面] 更新状态失败: {e}")

    def updateTaskTable(self, users):
        """更新任务表格"""
        try:
            # 只显示正在学习或有错误的用户
            active_users = [u for u in users if u.status in [
                UserStatus.LOGGING_IN, UserStatus.STUDYING, UserStatus.ERROR
            ]]

            self.taskTable.setRowCount(len(active_users))

            for i, user in enumerate(active_users):
                self.taskTable.setItem(i, 0, QTableWidgetItem(user.phone))
                self.taskTable.setItem(i, 1, QTableWidgetItem(user.name or ""))
                self.taskTable.setItem(i, 2, QTableWidgetItem(user.status))
                self.taskTable.setItem(i, 3, QTableWidgetItem(f"{user.progress:.1f}%"))
                self.taskTable.setItem(i, 4, QTableWidgetItem(user.error_message or ""))

        except Exception as e:
            print(f"[界面] 更新用户表格失败: {e}")

    def updateStudyStatus(self, status: str, message: str = ""):
        """更新学习状态"""
        try:
            print(f"[学习控制] 更新状态: {status} - {message}")

            if status == "starting":
                self.statusLabel.setText("正在启动...")
                self.statusLabel.setStyleSheet("font-size: 16px; font-weight: bold; color: #ff8c00;")
            elif status == "running":
                self.statusLabel.setText("学习中")
                self.statusLabel.setStyleSheet("font-size: 16px; font-weight: bold; color: #32cd32;")
                self.is_studying = True
            elif status == "stopped":
                self.statusLabel.setText("已停止")
                self.statusLabel.setStyleSheet("font-size: 16px; font-weight: bold; color: #dc143c;")
                self.is_studying = False
                self.is_paused = False
                # 重置按钮状态
                self.startBtn.setEnabled(True)
                self.pauseBtn.setEnabled(False)
                self.stopBtn.setEnabled(False)
            elif status == "error":
                self.statusLabel.setText("错误")
                self.statusLabel.setStyleSheet("font-size: 16px; font-weight: bold; color: #dc143c;")
                self.is_studying = False
                self.is_paused = False
                # 重置按钮状态
                self.startBtn.setEnabled(True)
                self.pauseBtn.setEnabled(False)
                self.stopBtn.setEnabled(False)

            if message:
                self.showInfoBar(message, "info" if status == "running" else "error" if status == "error" else "info")

        except Exception as e:
            print(f"[学习控制] 更新状态异常: {e}")

    def showInfoBar(self, message: str, type: str = "info"):
        """显示信息栏"""
        if type == "success":
            InfoBar.success("", message, duration=3000, parent=self)
        elif type == "warning":
            InfoBar.warning("", message, duration=5000, parent=self)
        elif type == "error":
            InfoBar.error("", message, duration=8000, parent=self)
        else:
            InfoBar.info("", message, duration=3000, parent=self)
