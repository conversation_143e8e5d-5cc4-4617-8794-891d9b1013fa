# coding: utf-8
"""
信号总线模块

该模块定义了应用程序的全局信号总线，用于在不同组件之间传递信号。
通过信号总线可以实现组件间的松耦合通信。

主要功能：
- 提供全局信号传递机制
- 支持云母效果状态变化信号
- 支持支持页面打开信号

类说明：
- SignalBus: 信号总线类，包含应用程序的全局信号
"""

from PySide6.QtCore import QObject, Signal


class SignalBus(QObject):
    """
    信号总线类

    提供应用程序级别的信号传递机制，用于在不同组件之间
    进行松耦合的通信。
    """

    # 云母效果启用状态变化信号
    micaEnableChanged = Signal(bool)

    # 支持页面打开信号
    supportSignal = Signal()

    # 页面切换信号，传递页面的routeKey
    switchToPageSignal = Signal(str)


# 全局信号总线实例
signalBus = SignalBus()