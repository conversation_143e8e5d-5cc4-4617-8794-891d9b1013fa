以下是优化后的 `开发要求.md` 文件内容：

# 学习工具开发规范文档

## 项目概述
本项目基于当前 PySide6-Fluent-Widgets 脚手架开发自动化学习辅助系统，旨在实现多账号批量学习操作，具备智能验证码识别、无痕浏览器自动化、并行学习等核心功能。

## 功能点介绍

### 独立模块开发
学习工具和 OCR 识别分别独立开发于 `/app/xueyuan` 和 `/app/ocr` 两个模块，彼此互不干扰，方便后续开发调用。所有开发工作均基于现有脚手架开展，统一以 `main.py` 作为唯一入口。

### 多账号管理
支持批量导入用户账号，账号录入格式为 `手机号码 密码`。

### 智能验证码识别
集成 Ddddocr 和百度 OCR API，支持主备引擎切换。默认以 Ddddocr 作为主引擎，百度 OCR 作为备选引擎。

### 自动化学习
对 Playwright 操作进行封装，以实现自动化学习流程。

### 多样化管理
将所有可配置项添加到设置页面，由用户自主决定是否启用。具体可参考 `app\view\setting_interface.py` 的组件和布局。

### 线程池
实现线程池，支持对并发数量、重试次数等参数进行配置。

### 并行学习
支持多账号同时进行学习，采用异步登录方式（即前一个账号登录成功后，再执行下一个账号的登录操作，避免 OCR 识别出错）。

### 数据提取
使用Sqlite3作为数据存储数据库，字段添加中文注释，具备读取和保存数据的功能，涉及用户信息、学习记录、课程信息等数据。

### 日志系统
记录操作日志和错误日志，支持实时查看、刷新、导出、筛选和清空日志，日志级别以中文显示。

### 界面组件
所有界面开发必须使用 qfluentwidgets 内置组件，详细参考官方 API 文档：[https://pyqt-fluent-widgets.readthedocs.io/zh-cn/latest/autoapi/index.html](https://pyqt-fluent-widgets.readthedocs.io/zh-cn/latest/autoapi/index.html)

### 学习流程
对学习流程进行封装，提供统一的学习接口。

### 异常处理
具备完善的异常处理和重试机制，确保系统的稳定性和可靠性。

### 学习配置
使用独立配置系统，遵循 PyQt-Fluent-Widgets 官方规范，保证配置的独立性和简洁性。

### 项目结构
```plaintext
- /app/xueyuan：学习工具模块
  - /app/xueyuan/common：公共模块
  - /app/xueyuan/components：界面组件
  - /app/xueyuan/view：界面模块
  - /app/xueyuan/config：配置文件
- /app/ocr：OCR 识别模块
- /data：数据目录
  - /data/config：配置文件目录
  - /data/：数据目录
```

## 界面设计

### 页面布局
参考 `app\view\blank_interface.py` 界面的布局方式，该空白页面界面能够完美支持 QFluentWidgets 明暗模式切换。关键代码如下：
```python
self.setObjectName('blankInterface')
self.view.setObjectName('view')
StyleSheet.BLANK_INTERFACE.apply(self)
```
其他界面必须保持一致。

### 主界面
顶部导航条使用 Pivot 组件，用于切换显示学习控制、用户管理、进度监控、学习设置等页面。

### 学习控制界面
- **左侧**：控制按钮区域，包含账号进度环、状态标签和控制按钮（启动学习、暂停学习、停止学习）。
- **右侧**：学习进度区域，以表格形式实时添加用户管理表格中状态非“已完成”的用户信息。表头包括：姓名、状态、总学时、必修学时、选修学时、进度条。

### 用户管理界面
- **顶部**：操作按钮栏，包含批量添加、导入用户、编辑用户、重置状态、删除选中按钮。
- **中间**：用户列表表格，使用 `TableWidget` 标准表格控件，支持圆角。表格列包括：手机号、姓名、状态、总学时、必修学时、选修学时、进度条。
- **底部**：状态统计栏，显示总用户、待学习、学习中、已完成、错误的用户数量。

### 日志界面
- **顶部**：操作按钮栏，包含刷新、导出、清空按钮。
- **底部**：日志统计栏，包含级别、搜索、自动滚动勾选框，以及日志查看器组件，支持日志级别颜色显示。

### 设置界面
包含个性化设置、浏览器设置、OCR 设置、日志设置等内容。将所有可配置项添加到设置页面，由用户自主决定是否启用（参考 `app\view\setting_interface.py`）。

## 开发规范

### 学习配置
继承 `QConfig`，将 `ConfigItem` 实例添加到 `StudyConfig` 的类属性中，创建全局唯一的 `StudyConfig` 单例 `cfg`，并通过 `qconfig.load("/data/config/study_config.json", cfg)` 加载配置文件。使用 `cfg.get(cfg.xxx)` 读取配置值，`cfg.set(cfg.xxx, value)` 写入配置值。

### API 请求
开发统一的 API 请求捕获方法。

### 数据结构

#### 用户数据
users
```json
{
    "users": [
        {
            "phone": "13800138000",
            "password": "password123",
            "name": "张三",
            "status": "未开始",
            "completeStatus": "1",
            "onlineTotalCredit": "157.5",
            "compulsoryCredit": 34.75,
            "electivesCredit": 122.75,
            "lastLoginTime": "2025-07-12 10:30:00",
            "errorMessage": "",
            "progress": 0.0
        }
    ],
    "created_at": "2025-07-12T10:00:00"
}
```

#### 课程数据
courses
```json
{
    "必修课": [
        {
            "name": "条例连着你我他 保守秘密靠大家",
            "completed": "0",
            "id": "1911718732312641537",
            "credit": 1,
            "percentage": "20.71",
            "coursewareId": "202501xxptFKJ2025040TO43a",
            "videoUrl": "https://study.jxgbwlxy.gov.cn/video?id=202501xxptFKJ2025040TO43a",
            "completed_date": ""
        }
    ],
    "选修课": [
        {
            "name": "统计法律法规知识",
            "completed": "0",
            "id": "1906616608113352706",
            "credit": 1,
            "percentage": "33.51",
            "coursewareId": "xxpt00282",
            "videoUrl": "https://study.jxgbwlxy.gov.cn/video?id=xxpt00282",
            "completed_date": ""
        }
    ],
    "updated_at": "2025-07-14T09:36:58.399568"
}
```

#### 日志数据
logs


#### 配置文件
文件路径：`data/config/study_config.json`
```json
{
    "System": {
        "AsyncLogin": true,
        "CompulsoryCourses": 20,
        "ElectiveCourses": 20,
        "AutoPlayVideo": false,
        "CheckCompleteStatus": false,
        "ConcurrentCount": 2,
        "DelayTime": 1,
        "RetryCount": 1
    },
    "Browser": {
        "BrowserMute": true,
        "BrowserType": "chrome",
        "DisableImages": false,
        "DisableJavaScript": false,
        "DisableWebSecurity": true,
        "Headless": false,
        "Proxy": "",
        "UserAgent": "",
        "VideoAutoplay": true
    },
    "Logging": {
        "LogLevel": "INFO",
        "MaxDays": 30,
        "MaxSizeMB": 100
    },
    "OCR": {
        "ApiKey": "8q3UoR04568WMCWszIMG43g0",
        "SecretKey": "Hn91Ahe4kqXnrWX5zXaAPVotjs64kI2G",
        "Timeout": 10
    }
}
```

## 学习流程

### 自动登录模块

#### 启动
程序运行后，自动打开登录页（`https://study.jxgbwlxy.gov.cn/index`），并进行自动登录操作。

#### 账号密码填充
模拟填充账号、密码和验证码。具体元素选择器如下：
- 账号输入框：`.el-input__inner[placeholder="您的手机号"]`
- 密码输入框：`.el-input__inner[placeholder="请输入密码"]`

#### 验证码识别
- 验证码图片元素：`.yzmImg`
- 验证码输入框元素：`.el-input__inner[placeholder="验证码"]`
使用 Ddddocr 或百度 OCR 识别验证码，验证码为 4 位，由数字和小写字母组成。

#### 登录操作
- 登录按钮元素：`.loginBtn.el-button`
点击登录按钮完成登录。

#### 登录出错处理
若登录失败，会弹出错误提示，错误提示元素为 `.el-notification__content p`。常见错误提示文本包括：`请输入验证码!`、`请输入账号!`、`请输入密码!`、`手机号格式错误`、`验证码错误`、`账号或密码错误`、`验证码长度为 4 位`、`密码已连续输错 3 次，剩余 2 次机会！` 等。

### 状态检查模块

#### 登录成功判断
检查是否跳转到学习档案页（`https://study.jxgbwlxy.gov.cn/study/data`），若跳转则表示登录成功。

#### 学习状态判断
使用 API 请求返回的 JSON 数据进行判断：
- 通过 `https://study.jxgbwlxy.gov.cn/api/report/myData/online` 接口获取用户的学习状态，返回数据结构如下：
```json
{
    "msg": "操作成功",
    "code": 0,
    "data": {
        "examineStatus": "1",
        "completeStatus": "0",
        "onlineTotalCredit": "157.5",
        "compulsoryCredit": 34.75,
        "shiftCredit": 0,
        "onlineAnswerCredits": 0,
        "compulsoryZwyCredit": 0,
        "electivesCredit": 122.75,
        "electivesZwyCredit": 0,
        "finishShiftNum": 0,
        "zwyCredit": 0,
        "zwyShiftNum": 0,
        "zwyColumnNum": 0
    }
}
```
- 通过 `https://study.jxgbwlxy.gov.cn/api/study/student/studentArchives/student` 接口获取用户姓名，返回数据结构如下：
```json
{
    "msg": "操作成功",
    "code": 0,
    "data": {
        "total": 0,
        "size": 0,
        "current": 0,
        "id": "358125",
        "stuName": "帅清明",
        "stuStatus": "0",
        "workRank": "3",
        "workCompany": {
            "id": "9415",
            "parentIds": "0,1,16,74,8143,171,",
            "name": "萍乡市上栗县桐木镇"
        }
    }
}
```
需要提取的字段包括：`completeStatus`（学习完成状态，1 表示未完成，0 表示已完成）、`onlineTotalCredit`（总学时）、`compulsoryCredit`（必修学时）、`electivesCredit`（选修学时）、`stuName`（用户姓名）。提取这些字段后，更新用户信息。若 `completeStatus` 为 1，则表示学习未完成，继续学习流程。

### 课程学习流程模块

#### 课程加载
判断 `{phone}` 的 `courses` 数据是否存在。若存在，检查必修课和选修课数量与设置中的必修课和选修课数量是否一致，若一致则开始学习；若少于设置数量，则进行对应的课程添加操作。

#### 进入“我的课程”
自动点击导航栏“我的课程”，跳转到（`https://study.jxgbwlxy.gov.cn/study/courseMine?id=0`）。

#### 处理课程“我的必修课”
- 到达“我的课程”页面后，点击“我的必修课”选项卡。
- 通过 `https://study.jxgbwlxy.gov.cn/api/study/years/yearsCourseware/annualPortalCourseListNew` 获取课程信息，返回数据结构如下：
```json
{
    "msg": "操作成功",
    "code": 0,
    "data": {
        "records": [
            {
                "total": 0,
                "size": 0,
                "current": 0,
                "percentage": "20.71",
                "completed": "0",
                "courseImage": "/jxgbwlxy/2025/条例连着你我他 保守秘密靠大家/jx2504002.png",
                "teachers": "——",
                "id": "1911718732312641537",
                "createDate": "2025-04-11",
                "updateDate": "2025-07-11",
                "name": "条例连着你我他 保守秘密靠大家",
                "courseware": {
                    "id": "202501xxptFKJ2025040TO43a",
                    "clicksNum": "3147"
                },
                "credit": 1,
                "creditsEarned": 0,
                "likeOrNot": false,
                "mainplatformparentcoursewareId": "FKJ2025040TO43",
                "examTransition": "1",
                "studentRank": ""
            }
        ],
        "total": 3,
        "size": 8,
        "current": 1,
        "orders": [],
        "optimizeCountSql": true,
        "hitCount": false,
        "searchCount": true,
        "pages": 1
    }
}
```
需要提取的字段包括：`name`（课程名称）、`completed`（课程完成状态，0 表示未完成，1 表示已完成）、`id`（课程 ID）、`credit`（课程学时）、`percentage`（课程完成进度）、`courseware.id`（课件 ID）、`total`（课程总数量）、`current`（当前页码）、`pages`（总页数）。根据 `total`、`current`、`pages` 三个字段判断是否还有下一页，若有则点击下一页按钮（元素选择器为 `.btn-next`），继续提取课程信息。提取到所有页面的课程信息后，生成课程字典，结构如下：
```python
{
    "必修课": [
        {
            "name": "条例连着你我他 保守秘密靠大家",
            "completed": "0",
            "id": "1911718732312641537",
            "credit": 1,
            "percentage": "20.71",
            "coursewareId": "202501xxptFKJ2025040TO43a",
            "videoUrl": "https://study.jxgbwlxy.gov.cn/video?id=202501xxptFKJ2025040TO43a"
        }
    ]
}
```

#### 处理课程“我的选修课”
- 处理完“我的必修课”后，点击“我的选修课”选项卡，切换到“我的选修课”页面。
- 通过 `https://study.jxgbwlxy.gov.cn/api/study/my/elective/myElectivesNew` 获取课程信息，API 返回的数据结构与“我的必修课”相同，处理逻辑也一致。
- 最后生成的课程字数据存到 `courses`，课程数据结构如下：
```python
{
    "必修课": [
        {
            "name": "条例连着你我他 保守秘密靠大家",
            "completed": "0",
            "id": "1911718732312641537",
            "credit": 1,
            "percentage": "20.71",
            "coursewareId": "202501xxptFKJ2025040TO43a",
            "videoUrl": "https://study.jxgbwlxy.gov.cn/video?id=202501xxptFKJ2025040TO43a",
            "completed_date": ""
        }
    ],
    "选修课": [
        {
            "name": "统计法律法规知识",
            "completed": "0",
            "id": "1906616608113352706",
            "credit": 1,
            "percentage": "33.51",
            "coursewareId": "xxpt00282",
            "videoUrl": "https://study.jxgbwlxy.gov.cn/video?id=xxpt00282",
            "completed_date": ""
        }
    ]
}
```

#### 添加选修课程
若处理“我的选修课”时发现列表为空，需要自动添加选修课程：
- 从导航跳转到“课程分类”页面（`https://study.jxgbwlxy.gov.cn/study/course-view`）。
- 等待页面加载完成，检查页面模式：
  - 若元素 `.el-button--danger.el-button--small.is-plain .el-icon-s-grid` 存在，表示当前是图片列表模式，点击图文切换按钮（元素选择器为 `.el-button--danger.el-button--small.is-plain`）切换到表格模式。
  - 若元素 `.el-button--danger.el-button--small.is-plain .el-icon-s-unfold` 存在，表示当前是表格列表模式。
- 在表格列表中，根据设置的“选课数量”，依次点击“添加课程”按钮（元素选择器为 `.el-icon-circle-plus-outline`）添加课程。若一页不足，翻页继续添加，直到满足数量要求。添加完成后，重新进行“处理课程‘我的选修课’”操作。

#### 进入视频播放页面处理
用户 `{phone}` 读取对应的 `courses` 数据，根据 `"completed":"0"` 的课程，打开对应的视频播放页面（`"videoUrl": "https://study.jxgbwlxy.gov.cn/video?id=xxpt00282"`）。页面加载完成后，执行一次“学习状态判断”并更新用户信息。

#### 处理温馨提示
因重复打开视频播放页面，可能会出现温馨提示页面（`https://study.jxgbwlxy.gov.cn/videoChoose?newId=...`），点击“此次打开的课件”后的标题（元素选择器为 `.choose-content`），进入视频播放页。

### 视频监听模块

#### 视频进度
仅在视频播放开始时，获取视频播放时间和视频总时长，并返回给用户学习监控模块。由学习监控模块记录播放时间和视频总时长，计算播放进度并显示，无需持续监听视频播放进度，避免线程阻塞。

#### 自动播放
通过浏览器参数 `--autoplay-policy=no-user-gesture-required` 控制视频播放，用户可在设置页面中设置是否自动播放。

#### 课件切换
每次监听到视频播放完成，判断当前课程的课件列表中是否存在 `未完成` 课件。判断代码如下：
```python
const elements = document.querySelectorAll('span[title]');
return Array.from(elements).some(el => el.textContent.includes('未完成'));
```
若存在未完成课件，则继续监听；若不存在，则表示当前课程的所有课件都已完成，将 `"completed":"0"` 更新为 `"completed":"1"`，并记录 `"completed_date": "YYYY-MM-DD"`。然后继续执行“进入视频播放页面处理”操作。

## API 捕获功能

### 实现示例
以下是 API 捕获功能的核心实现代码：
```python
import asyncio
from playwright.async_api import async_playwright
import json
import time

# 目标API列表 - 只捕获这两个API
TARGET_APIS = [
    "https://study.jxgbwlxy.gov.cn/api/report/myData/online",
    "https://study.jxgbwlxy.gov.cn/api/study/student/studentArchives/student"
]

def is_target_api(url: str) -> bool:
    """判断是否为目标API"""
    return url in TARGET_APIS

async def capture_simple_api_data():
    """简化的API数据捕获"""
    async with async_playwright() as p:
        # 启动浏览器
        browser = await p.chromium.launch(headless=False, slow_mo=500)
        context = await browser.new_context()
        page = await context.new_page()
        
        # 存储捕获的API数据
        api_data = {}
        
        # 网络监听器
        async def handle_response(response):
            url = response.url
            
            if is_target_api(url):
                print(f"[API捕获] 发现目标API: {url}")
                
                try:
                    if response.status == 200:
                        data = await response.json()
                        api_data[url] = {
                            'status': response.status,
                            'data': data,
                            'timestamp': time.time()
                        }
                        print(f"[API捕获] 成功捕获数据: {url}")
                        print(f"[API捕获] 数据大小: {len(str(data))} 字符")

                        # 输出捕获到的内容
                        print("=" * 60)
                        print(f"[API内容] URL: {url}")
                        print(f"[API内容] 状态码: {response.status}")
                        print(f"[API内容] 捕获时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime())}")
                        print("[API内容] 响应数据:")

                        # 格式化输出JSON数据
                        try:
                            formatted_data = json.dumps(data, ensure_ascii=False, indent=2)
                            # 如果数据太长，截断显示
                            if len(formatted_data) > 2000:
                                print(formatted_data[:2000] + "\n... [数据过长，已截断显示] ...")
                            else:
                                print(formatted_data)
                        except Exception as format_error:
                            print(f"数据格式化失败: {format_error}")
                            print(f"原始数据类型: {type(data)}")
                            print(f"原始数据: {str(data)[:500]}...")

                        print("=" * 60)

                        # 直接更新/保存数据到文件
                        save_api_data(api_data)
                        
                    else:
                        print(f"[API捕获] API响应状态异常: {url} (状态: {response.status})")
                        
                except Exception as e:
                    print(f"[API捕获] 处理API响应失败: {url} - {e}")
        
        # 设置网络监听器
        page.on("response", handle_response)
        print("[系统] 已设置网络监听器")
        
        # 1. 登录流程
        print("[登录] 开始登录...")
        await page.goto("https://study.jxgbwlxy.gov.cn/index")
        await page.wait_for_load_state("networkidle")
        
        # 填写登录信息
        await page.fill('.el-input__inner[placeholder="您的手机号"]', "18907995545")
        await page.fill('.el-input__inner[placeholder="请输入密码"]', "Shuai52018336")
        
        # 输入验证码
        captcha = input("请输入验证码: ")
        await page.fill('.el-input__inner[placeholder="验证码"]', captcha)
        
        # 登录
        await page.click('.loginBtn.el-button')
        await page.wait_for_url("**/study/data", timeout=30000)
        print("[登录] 登录成功")
        
        # 等待API请求
        await page.wait_for_load_state("networkidle")
        await asyncio.sleep(3)
        
        print(f"[系统] 登录阶段完成，已捕获 {len(api_data)} 个目标API")
        
        # 2. 访问课程播放页面
        print("[课程] 准备访问课程播放页面...")
        
        # 可以在这里修改为具体的课程页面URL
        video_url = "https://study.jxgbwlxy.gov.cn/video?id=202501xxptFKJ2025070TO12a"
        
        try:
            await page.goto(video_url)
            await page.wait_for_load_state("networkidle")
            print("[课程] 成功访问课程页面")
            
            # 等待可能的延迟API请求
            await asyncio.sleep(5)
            
            # 尝试触发更多API请求（滚动页面等）
            await page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
            await asyncio.sleep(2)
            await page.evaluate("window.scrollTo(0, 0)")
            await asyncio.sleep(2)
            
        except Exception as e:
            print(f"[课程] 访问课程页面失败: {e}")
        
        print(f"[系统] 课程页面访问完成，总共捕获 {len(api_data)} 个目标API")
        
        # 3. 最终保存
        if api_data:
            save_api_data(api_data)
            print(f"[系统] 数据捕获完成，共获取 {len(api_data)} 个API响应")
            
            # 显示捕获的API列表
            for url in api_data.keys():
                print(f"  - {url}")
        else:
            print("[系统] 未捕获到目标API数据")
        
        # 保持浏览器打开一段时间
        await asyncio.sleep(3)
        await browser.close()
        
        return api_data

def save_api_data(api_data: dict):
    """保存API数据到文件"""
    try:
        filename = "simple_api_data.json"
        with open(filename, "w", encoding="utf-8") as f:
            json.dump(api_data, f, ensure_ascii=False, indent=2)
        print(f"[保存] 数据已保存到 {filename}")
    except Exception as e:
        print(f"[保存] 保存数据失败: {e}")

def load_existing_data():
    """加载现有数据"""
    try:
        with open("simple_api_data.json", "r", encoding="utf-8") as f:
            return json.load(f)
    except FileNotFoundError:
        return {}
    except Exception as e:
        print(f"[加载] 加载现有数据失败: {e}")
        return {}

if __name__ == "__main__":
    print("=" * 50)
    print("简化API捕获工具")
    print("目标API:")
    for api in TARGET_APIS:
        print(f"  - {api}")
    print("=" * 50)
    
    # 运行捕获
    asyncio.run(capture_simple_api_data())

```

## 主要页面 URL
- 登录页面: https://study.jxgbwlxy.gov.cn/index
- 学习档案页面: https://study.jxgbwlxy.gov.cn/study/data
- 我的课程页面: https://study.jxgbwlxy.gov.cn/study/courseMine?id=0
- 课程分类页面: https://study.jxgbwlxy.gov.cn/study/course-view
- 温馨提示页面: https://study.jxgbwlxy.gov.cn/videoChoose?newId=2023xxpt000168&oldId=202501xxptFKJ2025070TO8a&courseStudyType=1
- 视频播放页面: https://study.jxgbwlxy.gov.cn/video?id=2023xxpt000168&platformcoursewaretypeId=xxfl1001&compulsoryElective=1&mainplatformparentcoursewareId=FKJ2025070TO8