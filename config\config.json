{"API": {"CaptureEnabled": true, "RetryCount": 3, "Timeout": 30}, "Concurrency": {"ApiRateLimit": 60, "DefaultTimeout": 300, "MaxConcurrentTasks": 10, "MaxConcurrentUsers": 5, "MaxQueueSize": 100, "MaxWorkers": 4, "SchedulerInterval": 1.0}, "System": {"AsyncLogin": true, "AutoMinimize": true, "AutoStart": false, "CheckUpdatesOnStart": true, "CompulsoryCourses": 20, "ConcurrentCount": 2, "DelayTime": 1, "ElectiveCourses": 20, "EnableNotifications": true, "RetryCount": 3, "SaveWindowState": true}, "StudyStrategy": {"AutoNextCourse": true, "EnableRandomDelay": true, "MaxStudyTimePerCourse": 1800, "MinStudyTimePerCourse": 300, "RandomDelayMax": 5, "RandomDelayMin": 1, "SkipCompletedCourses": true, "StudyMode": "sequential", "StudySpeedMultiplier": 1.0}, "UI": {"AutoRefreshInterval": 5, "EnableAnimations": true, "EnableSoundNotifications": false, "MaxLogDisplayLines": 1000, "ShowProgressDetails": true, "ThemeColor": "#ff0078d4"}, "Backup": {"BackupInterval": 24, "BackupPath": "backups", "CompressBackups": true, "EnableAutoBackup": true, "MaxBackupFiles": 10}, "OCR": {"BaiduApiKey": "", "BaiduSecretKey": "", "ConfidenceThreshold": 0.8, "EnablePreprocessing": true, "FallbackEngine": "baidu", "ImageEnhancement": true, "RetryCount": 3, "Timeout": 10, "PrimaryEngine": "ddddocr"}, "Website": {"BaseUrl": "https://study.jxgbwlxy.gov.cn", "CourseListUrl": "https://study.jxgbwlxy.gov.cn/courses", "LoginUrl": "https://study.jxgbwlxy.gov.cn/index", "StudyUrl": "https://study.jxgbwlxy.gov.cn/study"}, "Browser": {"BrowserType": "chrome", "DisableCSS": false, "DisableImages": false, "DisableJavaScript": false, "DownloadPath": "downloads", "ElementWaitTimeout": 10, "EnableDevTools": false, "Headless": false, "PageLoadTimeout": 30, "UserAgent": "", "WindowHeight": 1080, "WindowWidth": 1920}, "Logging": {"EnableConsoleLog": true, "EnableDatabaseLog": true, "EnableFileLog": true, "LogBackupCount": 5, "LogDir": "logs", "LogLevel": "INFO", "MaxDays": 30, "MaxLogFileSize": 10485760, "MaxSizeMB": 100}, "QFluentWidgets": {"ThemeMode": "Light"}}