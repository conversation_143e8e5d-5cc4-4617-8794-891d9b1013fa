# coding:utf-8
"""
OCR引擎基础接口

该模块定义了OCR引擎的基础接口类 BaseOCREngine，
所有OCR引擎实现都应该继承此类。

主要功能：
- 定义OCR引擎的标准接口
- 提供通用的错误处理
- 定义识别结果的数据结构

类说明：
- BaseOCREngine: OCR引擎基础接口类
- OCRResult: OCR识别结果数据类
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import Union, Optional, List, Dict, Any
from pathlib import Path
import io


@dataclass
class OCRResult:
    """OCR识别结果数据类"""
    
    # 识别是否成功
    success: bool = False
    
    # 识别的文本内容
    text: str = ""
    
    # 置信度 (0-1)
    confidence: float = 0.0
    
    # 错误信息
    error_message: str = ""
    
    # 引擎名称
    engine_name: str = ""
    
    # 处理时间(秒)
    processing_time: float = 0.0
    
    # 详细结果(引擎特定的原始数据)
    raw_result: Optional[Dict[str, Any]] = None
    
    # 文字位置信息(如果支持)
    positions: Optional[List[Dict[str, Any]]] = None


class BaseOCREngine(ABC):
    """
    OCR引擎基础接口类
    
    所有OCR引擎实现都应该继承此类，并实现抽象方法。
    提供统一的OCR识别接口和错误处理机制。
    """
    
    def __init__(self, name: str, **kwargs):
        """
        初始化OCR引擎
        
        Args:
            name: 引擎名称
            **kwargs: 引擎特定的配置参数
        """
        self.name = name
        self.config = kwargs
        self.is_initialized = False
        self.error_message = ""
    
    @abstractmethod
    def initialize(self) -> bool:
        """
        初始化OCR引擎
        
        Returns:
            bool: 初始化是否成功
        """
        pass
    
    @abstractmethod
    def recognize_text(self, image_data: Union[bytes, str, Path]) -> OCRResult:
        """
        识别图片中的文字
        
        Args:
            image_data: 图片数据，可以是：
                - bytes: 图片的二进制数据
                - str: 图片文件路径
                - Path: 图片文件路径对象
        
        Returns:
            OCRResult: 识别结果
        """
        pass
    
    @abstractmethod
    def is_available(self) -> bool:
        """
        检查引擎是否可用
        
        Returns:
            bool: 引擎是否可用
        """
        pass
    
    def cleanup(self):
        """
        清理资源
        
        子类可以重写此方法来清理特定的资源
        """
        pass
    
    def _load_image_data(self, image_data: Union[bytes, str, Path]) -> bytes:
        """
        加载图片数据为bytes格式
        
        Args:
            image_data: 图片数据
            
        Returns:
            bytes: 图片的二进制数据
            
        Raises:
            ValueError: 当图片数据格式不支持时
            FileNotFoundError: 当图片文件不存在时
        """
        if isinstance(image_data, bytes):
            return image_data
        elif isinstance(image_data, (str, Path)):
            file_path = Path(image_data)
            if not file_path.exists():
                raise FileNotFoundError(f"图片文件不存在: {file_path}")
            return file_path.read_bytes()
        else:
            raise ValueError(f"不支持的图片数据类型: {type(image_data)}")
    
    def _create_error_result(self, error_message: str) -> OCRResult:
        """
        创建错误结果
        
        Args:
            error_message: 错误信息
            
        Returns:
            OCRResult: 错误结果对象
        """
        return OCRResult(
            success=False,
            text="",
            confidence=0.0,
            error_message=error_message,
            engine_name=self.name,
            processing_time=0.0
        )
    
    def _create_success_result(self, text: str, confidence: float = 1.0, 
                             processing_time: float = 0.0, 
                             raw_result: Optional[Dict[str, Any]] = None,
                             positions: Optional[List[Dict[str, Any]]] = None) -> OCRResult:
        """
        创建成功结果
        
        Args:
            text: 识别的文本
            confidence: 置信度
            processing_time: 处理时间
            raw_result: 原始结果数据
            positions: 文字位置信息
            
        Returns:
            OCRResult: 成功结果对象
        """
        return OCRResult(
            success=True,
            text=text,
            confidence=confidence,
            error_message="",
            engine_name=self.name,
            processing_time=processing_time,
            raw_result=raw_result,
            positions=positions
        )
    
    def __str__(self) -> str:
        """返回引擎的字符串表示"""
        return f"{self.__class__.__name__}(name='{self.name}', initialized={self.is_initialized})"
    
    def __repr__(self) -> str:
        """返回引擎的详细字符串表示"""
        return self.__str__()
