# coding:utf-8
"""
日志系统模块

该模块提供日志记录和管理功能，包括多级别日志记录、
日志查看界面、日志导出等。

主要功能：
- 多级别日志记录
- 日志文件管理
- 日志查看和搜索
- 日志导出功能

模块组件：
- manager: 日志管理器
- viewer: 日志查看器
- exporter: 日志导出器

使用示例：
    from app.xueyuan.logging import log_manager
    
    # 记录日志
    log_manager.info("这是一条信息日志")
    log_manager.error("这是一条错误日志")
    
    # 查看日志
    logs = log_manager.get_logs(level="ERROR", limit=100)
"""

from .manager import LogManager, log_manager
from .viewer import LogViewer
from .exporter import LogExporter

__all__ = ['LogManager', 'log_manager', 'LogViewer', 'LogExporter']
