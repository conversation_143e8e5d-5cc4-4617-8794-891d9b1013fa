# coding:utf-8
"""
线程池管理器

该模块提供线程池管理功能，包括任务队列管理、
并发控制、异常处理和重试机制。

主要功能：
- 线程池创建和管理
- 任务队列管理
- 并发数量控制
- 异常处理和重试
- 任务状态监控

类说明：
- ThreadPoolManager: 线程池管理器类
- TaskQueue: 任务队列类
- TaskResult: 任务结果类
"""

import asyncio
import threading
import time
from concurrent.futures import ThreadPoolExecutor, Future, as_completed
from typing import Optional, Dict, Any, List, Callable, Union
from datetime import datetime
from dataclasses import dataclass
from enum import Enum
from queue import Queue, Empty

from .config_loader import get_study_config
from ..database.dao import DAOFactory


class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"      # 等待执行
    RUNNING = "running"      # 正在执行
    COMPLETED = "completed"  # 执行完成
    FAILED = "failed"        # 执行失败
    CANCELLED = "cancelled"  # 已取消
    RETRYING = "retrying"    # 重试中


@dataclass
class TaskResult:
    """任务结果数据类"""
    task_id: str
    status: TaskStatus
    result: Any = None
    error: Optional[Exception] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    retry_count: int = 0
    max_retries: int = 3


@dataclass
class TaskInfo:
    """任务信息数据类"""
    task_id: str
    func: Callable
    args: tuple
    kwargs: dict
    priority: int = 0
    max_retries: int = 3
    retry_delay: float = 1.0
    timeout: Optional[float] = None
    callback: Optional[Callable] = None
    created_at: datetime = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()


class TaskQueue:
    """
    任务队列类
    
    提供优先级队列和任务管理功能
    """
    
    def __init__(self, maxsize: int = 0):
        """
        初始化任务队列
        
        Args:
            maxsize: 队列最大大小，0表示无限制
        """
        self.queue = Queue(maxsize=maxsize)
        self.pending_tasks: Dict[str, TaskInfo] = {}
        self.running_tasks: Dict[str, TaskInfo] = {}
        self.completed_tasks: Dict[str, TaskResult] = {}
        self.lock = threading.Lock()
        
        print("[线程池] 任务队列初始化完成")
    
    def put(self, task_info: TaskInfo) -> bool:
        """
        添加任务到队列
        
        Args:
            task_info: 任务信息
            
        Returns:
            bool: 是否成功添加
        """
        try:
            with self.lock:
                if task_info.task_id in self.pending_tasks or task_info.task_id in self.running_tasks:
                    print(f"[任务队列] 任务已存在: {task_info.task_id}")
                    return False
                
                # 按优先级排序插入
                self.queue.put((task_info.priority, task_info.created_at, task_info))
                self.pending_tasks[task_info.task_id] = task_info
                
                print(f"[任务队列] 任务已添加: {task_info.task_id}")
                return True
                
        except Exception as e:
            print(f"[任务队列] 添加任务失败: {e}")
            return False
    
    def get(self, timeout: Optional[float] = None) -> Optional[TaskInfo]:
        """
        从队列获取任务
        
        Args:
            timeout: 超时时间
            
        Returns:
            Optional[TaskInfo]: 任务信息
        """
        try:
            priority, created_at, task_info = self.queue.get(timeout=timeout)
            
            with self.lock:
                if task_info.task_id in self.pending_tasks:
                    del self.pending_tasks[task_info.task_id]
                    self.running_tasks[task_info.task_id] = task_info
            
            return task_info
            
        except Empty:
            return None
        except Exception as e:
            print(f"[任务队列] 获取任务失败: {e}")
            return None
    
    def mark_completed(self, task_id: str, result: TaskResult):
        """
        标记任务完成
        
        Args:
            task_id: 任务ID
            result: 任务结果
        """
        with self.lock:
            if task_id in self.running_tasks:
                del self.running_tasks[task_id]
            
            self.completed_tasks[task_id] = result
    
    def cancel_task(self, task_id: str) -> bool:
        """
        取消任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            bool: 是否成功取消
        """
        with self.lock:
            if task_id in self.pending_tasks:
                del self.pending_tasks[task_id]
                
                # 创建取消结果
                result = TaskResult(
                    task_id=task_id,
                    status=TaskStatus.CANCELLED,
                    end_time=datetime.now()
                )
                self.completed_tasks[task_id] = result
                
                print(f"[任务队列] 任务已取消: {task_id}")
                return True
            
            return False
    
    def get_status(self) -> Dict[str, Any]:
        """
        获取队列状态
        
        Returns:
            Dict[str, Any]: 队列状态
        """
        with self.lock:
            return {
                'pending_count': len(self.pending_tasks),
                'running_count': len(self.running_tasks),
                'completed_count': len(self.completed_tasks),
                'queue_size': self.queue.qsize()
            }


class ThreadPoolManager:
    """
    线程池管理器类
    
    提供线程池管理和任务调度功能
    """
    
    def __init__(self, max_workers: Optional[int] = None):
        """
        初始化线程池管理器
        
        Args:
            max_workers: 最大工作线程数
        """
        # 配置
        cfg = get_study_config()
        self.max_workers = max_workers or cfg.maxWorkers.value
        self.max_queue_size = cfg.maxQueueSize.value
        self.default_timeout = cfg.defaultTimeout.value
        
        # 线程池
        self.executor: Optional[ThreadPoolExecutor] = None
        self.task_queue = TaskQueue(maxsize=self.max_queue_size)
        
        # 状态管理
        self.is_running = False
        self.worker_threads: List[threading.Thread] = []
        self.shutdown_event = threading.Event()
        
        # 统计信息
        self.stats = {
            'total_tasks': 0,
            'completed_tasks': 0,
            'failed_tasks': 0,
            'cancelled_tasks': 0,
            'start_time': None
        }
        
        # 数据库DAO
        self.log_dao = DAOFactory.get_log_dao()
        
        print(f"[线程池] 线程池管理器初始化完成，最大工作线程数: {self.max_workers}")
    
    def start(self) -> bool:
        """
        启动线程池
        
        Returns:
            bool: 是否成功启动
        """
        if self.is_running:
            print("[线程池] 线程池已在运行中")
            return True
        
        try:
            # 创建线程池
            self.executor = ThreadPoolExecutor(max_workers=self.max_workers)
            
            # 启动工作线程
            for i in range(self.max_workers):
                worker_thread = threading.Thread(
                    target=self._worker_loop,
                    name=f"ThreadPoolWorker-{i}",
                    daemon=True
                )
                worker_thread.start()
                self.worker_threads.append(worker_thread)
            
            self.is_running = True
            self.stats['start_time'] = datetime.now()
            self.shutdown_event.clear()
            
            print(f"[线程池] 线程池已启动，工作线程数: {len(self.worker_threads)}")
            return True
            
        except Exception as e:
            print(f"[线程池] 启动线程池失败: {e}")
            return False
    
    def shutdown(self, wait: bool = True, timeout: Optional[float] = None):
        """
        关闭线程池
        
        Args:
            wait: 是否等待任务完成
            timeout: 等待超时时间
        """
        if not self.is_running:
            print("[线程池] 线程池未在运行中")
            return
        
        try:
            print("[线程池] 正在关闭线程池...")
            
            # 设置关闭标志
            self.shutdown_event.set()
            self.is_running = False
            
            # 关闭线程池
            if self.executor:
                self.executor.shutdown(wait=wait, timeout=timeout)
                self.executor = None
            
            # 等待工作线程结束
            if wait:
                for thread in self.worker_threads:
                    thread.join(timeout=timeout)
            
            self.worker_threads.clear()
            
            print("[线程池] 线程池已关闭")
            
        except Exception as e:
            print(f"[线程池] 关闭线程池失败: {e}")
    
    def submit_task(self, func: Callable, *args, task_id: Optional[str] = None,
                   priority: int = 0, max_retries: int = 3, retry_delay: float = 1.0,
                   timeout: Optional[float] = None, callback: Optional[Callable] = None,
                   **kwargs) -> Optional[str]:
        """
        提交任务
        
        Args:
            func: 要执行的函数
            *args: 函数参数
            task_id: 任务ID（可选）
            priority: 优先级（数字越大优先级越高）
            max_retries: 最大重试次数
            retry_delay: 重试延迟（秒）
            timeout: 任务超时时间
            callback: 完成回调函数
            **kwargs: 函数关键字参数
            
        Returns:
            Optional[str]: 任务ID
        """
        if not self.is_running:
            print("[线程池] 线程池未启动")
            return None
        
        try:
            # 生成任务ID
            if task_id is None:
                task_id = f"task_{int(time.time() * 1000)}_{threading.get_ident()}"
            
            # 创建任务信息
            task_info = TaskInfo(
                task_id=task_id,
                func=func,
                args=args,
                kwargs=kwargs,
                priority=priority,
                max_retries=max_retries,
                retry_delay=retry_delay,
                timeout=timeout or self.default_timeout,
                callback=callback
            )
            
            # 添加到队列
            if self.task_queue.put(task_info):
                self.stats['total_tasks'] += 1
                print(f"[线程池] 任务已提交: {task_id}")
                return task_id
            else:
                print(f"[线程池] 任务提交失败: {task_id}")
                return None
                
        except Exception as e:
            print(f"[线程池] 提交任务失败: {e}")
            return None
    
    def _worker_loop(self):
        """工作线程循环"""
        thread_name = threading.current_thread().name
        print(f"[线程池] 工作线程启动: {thread_name}")
        
        while not self.shutdown_event.is_set():
            try:
                # 获取任务
                task_info = self.task_queue.get(timeout=1.0)
                if task_info is None:
                    continue
                
                # 执行任务
                self._execute_task(task_info)
                
            except Exception as e:
                print(f"[线程池] 工作线程异常: {thread_name} - {e}")
        
        print(f"[线程池] 工作线程结束: {thread_name}")
    
    def _execute_task(self, task_info: TaskInfo):
        """
        执行任务
        
        Args:
            task_info: 任务信息
        """
        task_id = task_info.task_id
        retry_count = 0
        
        while retry_count <= task_info.max_retries:
            try:
                print(f"[线程池] 开始执行任务: {task_id} (重试次数: {retry_count})")
                
                # 创建任务结果
                result = TaskResult(
                    task_id=task_id,
                    status=TaskStatus.RUNNING,
                    start_time=datetime.now(),
                    retry_count=retry_count,
                    max_retries=task_info.max_retries
                )
                
                # 执行任务
                if task_info.timeout:
                    # 使用超时执行
                    future = self.executor.submit(task_info.func, *task_info.args, **task_info.kwargs)
                    task_result = future.result(timeout=task_info.timeout)
                else:
                    # 直接执行
                    task_result = task_info.func(*task_info.args, **task_info.kwargs)
                
                # 任务成功完成
                result.status = TaskStatus.COMPLETED
                result.result = task_result
                result.end_time = datetime.now()
                
                # 标记完成
                self.task_queue.mark_completed(task_id, result)
                self.stats['completed_tasks'] += 1
                
                # 执行回调
                if task_info.callback:
                    try:
                        task_info.callback(result)
                    except Exception as e:
                        print(f"[线程池] 回调函数执行失败: {e}")
                
                print(f"[线程池] 任务执行成功: {task_id}")
                break
                
            except Exception as e:
                retry_count += 1
                
                if retry_count > task_info.max_retries:
                    # 重试次数用尽，任务失败
                    result = TaskResult(
                        task_id=task_id,
                        status=TaskStatus.FAILED,
                        error=e,
                        end_time=datetime.now(),
                        retry_count=retry_count - 1,
                        max_retries=task_info.max_retries
                    )
                    
                    self.task_queue.mark_completed(task_id, result)
                    self.stats['failed_tasks'] += 1
                    
                    print(f"[线程池] 任务执行失败: {task_id} - {e}")
                    
                    # 记录错误日志
                    self._log_task_error(task_id, e)
                    break
                else:
                    # 准备重试
                    print(f"[线程池] 任务执行失败，准备重试: {task_id} - {e}")
                    
                    if task_info.retry_delay > 0:
                        time.sleep(task_info.retry_delay)
    
    def cancel_task(self, task_id: str) -> bool:
        """
        取消任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            bool: 是否成功取消
        """
        if self.task_queue.cancel_task(task_id):
            self.stats['cancelled_tasks'] += 1
            return True
        return False
    
    def get_task_result(self, task_id: str) -> Optional[TaskResult]:
        """
        获取任务结果
        
        Args:
            task_id: 任务ID
            
        Returns:
            Optional[TaskResult]: 任务结果
        """
        return self.task_queue.completed_tasks.get(task_id)
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        queue_status = self.task_queue.get_status()
        
        return {
            'is_running': self.is_running,
            'max_workers': self.max_workers,
            'worker_count': len(self.worker_threads),
            'start_time': self.stats['start_time'],
            'total_tasks': self.stats['total_tasks'],
            'completed_tasks': self.stats['completed_tasks'],
            'failed_tasks': self.stats['failed_tasks'],
            'cancelled_tasks': self.stats['cancelled_tasks'],
            **queue_status
        }
    
    def _log_task_error(self, task_id: str, error: Exception):
        """
        记录任务错误日志
        
        Args:
            task_id: 任务ID
            error: 错误信息
        """
        try:
            log_data = {
                'user_phone': 'system',
                'level': 'ERROR',
                'message': f"[线程池] 任务执行失败: {task_id} - {str(error)}",
                'module': 'ThreadPoolManager',
                'timestamp': datetime.now()
            }
            
            self.log_dao.create(log_data)
            
        except Exception as e:
            print(f"[线程池] 记录错误日志失败: {e}")


# 全局线程池管理器实例
thread_pool_manager = ThreadPoolManager()
