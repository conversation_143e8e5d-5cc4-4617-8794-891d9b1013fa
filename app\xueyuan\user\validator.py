# coding:utf-8
"""
用户验证器

该模块提供用户数据验证功能，包括手机号验证、
密码强度检查、用户信息验证等。

主要功能：
- 手机号格式验证
- 密码强度检查
- 用户信息完整性验证
- 业务规则验证

类说明：
- UserValidator: 用户验证器类
"""

import re
from typing import Dict, List, Optional, Any
from datetime import datetime

from ..common.constants import UserStatus


class UserValidator:
    """
    用户验证器类
    
    提供用户数据验证功能
    """
    
    def __init__(self):
        """初始化用户验证器"""
        # 手机号正则表达式
        self.phone_pattern = re.compile(r'^1[3-9]\d{9}$')
        
        # 密码规则
        self.password_min_length = 6
        self.password_max_length = 20
        
        print("[用户验证] 用户验证器初始化完成")
    
    def validate_phone(self, phone: str) -> Dict[str, Any]:
        """
        验证手机号
        
        Args:
            phone: 手机号
            
        Returns:
            Dict[str, Any]: 验证结果
        """
        result = {
            'valid': False,
            'errors': []
        }
        
        if not phone:
            result['errors'].append('手机号不能为空')
            return result
        
        # 去除空格和特殊字符
        phone = phone.strip().replace(' ', '').replace('-', '')
        
        # 检查长度
        if len(phone) != 11:
            result['errors'].append('手机号必须为11位数字')
            return result
        
        # 检查是否全为数字
        if not phone.isdigit():
            result['errors'].append('手机号只能包含数字')
            return result
        
        # 检查格式
        if not self.phone_pattern.match(phone):
            result['errors'].append('手机号格式不正确')
            return result
        
        result['valid'] = True
        return result
    
    def validate_password(self, password: str) -> Dict[str, Any]:
        """
        验证密码
        
        Args:
            password: 密码
            
        Returns:
            Dict[str, Any]: 验证结果
        """
        result = {
            'valid': False,
            'errors': [],
            'strength': 'weak'
        }
        
        if not password:
            result['errors'].append('密码不能为空')
            return result
        
        # 检查长度
        if len(password) < self.password_min_length:
            result['errors'].append(f'密码长度不能少于{self.password_min_length}位')
        
        if len(password) > self.password_max_length:
            result['errors'].append(f'密码长度不能超过{self.password_max_length}位')
        
        if result['errors']:
            return result
        
        # 检查密码强度
        strength_score = 0
        
        # 包含小写字母
        if re.search(r'[a-z]', password):
            strength_score += 1
        
        # 包含大写字母
        if re.search(r'[A-Z]', password):
            strength_score += 1
        
        # 包含数字
        if re.search(r'\d', password):
            strength_score += 1
        
        # 包含特殊字符
        if re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            strength_score += 1
        
        # 长度加分
        if len(password) >= 8:
            strength_score += 1
        
        # 确定强度等级
        if strength_score >= 4:
            result['strength'] = 'strong'
        elif strength_score >= 2:
            result['strength'] = 'medium'
        else:
            result['strength'] = 'weak'
        
        result['valid'] = True
        return result
    
    def validate_name(self, name: str) -> Dict[str, Any]:
        """
        验证姓名
        
        Args:
            name: 姓名
            
        Returns:
            Dict[str, Any]: 验证结果
        """
        result = {
            'valid': False,
            'errors': []
        }
        
        if not name:
            result['errors'].append('姓名不能为空')
            return result
        
        name = name.strip()
        
        # 检查长度
        if len(name) < 2:
            result['errors'].append('姓名长度不能少于2位')
        
        if len(name) > 20:
            result['errors'].append('姓名长度不能超过20位')
        
        # 检查字符
        if not re.match(r'^[\u4e00-\u9fa5a-zA-Z\s]+$', name):
            result['errors'].append('姓名只能包含中文、英文字母和空格')
        
        if result['errors']:
            return result
        
        result['valid'] = True
        return result
    
    def validate_user_data(self, user_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        验证用户数据
        
        Args:
            user_data: 用户数据
            
        Returns:
            Dict[str, Any]: 验证结果
        """
        result = {
            'valid': False,
            'errors': [],
            'field_errors': {}
        }
        
        # 验证必填字段
        required_fields = ['phone', 'name']
        for field in required_fields:
            if field not in user_data or not user_data[field]:
                result['errors'].append(f'{field}是必填字段')
                result['field_errors'][field] = [f'{field}不能为空']
        
        if result['errors']:
            return result
        
        # 验证手机号
        phone_result = self.validate_phone(user_data['phone'])
        if not phone_result['valid']:
            result['field_errors']['phone'] = phone_result['errors']
            result['errors'].extend(phone_result['errors'])
        
        # 验证姓名
        name_result = self.validate_name(user_data['name'])
        if not name_result['valid']:
            result['field_errors']['name'] = name_result['errors']
            result['errors'].extend(name_result['errors'])
        
        # 验证密码（如果提供）
        if 'password' in user_data and user_data['password']:
            password_result = self.validate_password(user_data['password'])
            if not password_result['valid']:
                result['field_errors']['password'] = password_result['errors']
                result['errors'].extend(password_result['errors'])
        
        # 验证状态
        if 'status' in user_data:
            status = user_data['status']
            if status not in [s.value for s in UserStatus]:
                result['errors'].append('用户状态值无效')
                result['field_errors']['status'] = ['用户状态值无效']
        
        # 验证创建时间
        if 'created_at' in user_data:
            created_at = user_data['created_at']
            if isinstance(created_at, str):
                try:
                    datetime.fromisoformat(created_at)
                except ValueError:
                    result['errors'].append('创建时间格式无效')
                    result['field_errors']['created_at'] = ['时间格式无效']
        
        # 如果没有错误，验证通过
        if not result['errors']:
            result['valid'] = True
        
        return result
    
    def validate_login_data(self, login_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        验证登录数据
        
        Args:
            login_data: 登录数据
            
        Returns:
            Dict[str, Any]: 验证结果
        """
        result = {
            'valid': False,
            'errors': [],
            'field_errors': {}
        }
        
        # 验证必填字段
        required_fields = ['phone', 'password']
        for field in required_fields:
            if field not in login_data or not login_data[field]:
                result['errors'].append(f'{field}是必填字段')
                result['field_errors'][field] = [f'{field}不能为空']
        
        if result['errors']:
            return result
        
        # 验证手机号
        phone_result = self.validate_phone(login_data['phone'])
        if not phone_result['valid']:
            result['field_errors']['phone'] = phone_result['errors']
            result['errors'].extend(phone_result['errors'])
        
        # 验证密码（基本检查）
        password = login_data['password']
        if len(password) < self.password_min_length:
            result['errors'].append('密码长度不正确')
            result['field_errors']['password'] = ['密码长度不正确']
        
        # 如果没有错误，验证通过
        if not result['errors']:
            result['valid'] = True
        
        return result
    
    def validate_update_data(self, update_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        验证更新数据
        
        Args:
            update_data: 更新数据
            
        Returns:
            Dict[str, Any]: 验证结果
        """
        result = {
            'valid': False,
            'errors': [],
            'field_errors': {}
        }
        
        # 如果没有提供任何数据
        if not update_data:
            result['errors'].append('更新数据不能为空')
            return result
        
        # 验证姓名（如果提供）
        if 'name' in update_data:
            name_result = self.validate_name(update_data['name'])
            if not name_result['valid']:
                result['field_errors']['name'] = name_result['errors']
                result['errors'].extend(name_result['errors'])
        
        # 验证密码（如果提供）
        if 'password' in update_data:
            password_result = self.validate_password(update_data['password'])
            if not password_result['valid']:
                result['field_errors']['password'] = password_result['errors']
                result['errors'].extend(password_result['errors'])
        
        # 验证状态（如果提供）
        if 'status' in update_data:
            status = update_data['status']
            if status not in [s.value for s in UserStatus]:
                result['errors'].append('用户状态值无效')
                result['field_errors']['status'] = ['用户状态值无效']
        
        # 不允许更新手机号
        if 'phone' in update_data:
            result['errors'].append('不允许更新手机号')
            result['field_errors']['phone'] = ['不允许更新手机号']
        
        # 如果没有错误，验证通过
        if not result['errors']:
            result['valid'] = True
        
        return result
    
    def sanitize_phone(self, phone: str) -> str:
        """
        清理手机号
        
        Args:
            phone: 原始手机号
            
        Returns:
            str: 清理后的手机号
        """
        if not phone:
            return ''
        
        # 去除空格和特殊字符
        return phone.strip().replace(' ', '').replace('-', '')
    
    def sanitize_name(self, name: str) -> str:
        """
        清理姓名
        
        Args:
            name: 原始姓名
            
        Returns:
            str: 清理后的姓名
        """
        if not name:
            return ''
        
        # 去除首尾空格，保留中间空格
        return ' '.join(name.split())
    
    def get_validation_rules(self) -> Dict[str, Any]:
        """
        获取验证规则
        
        Returns:
            Dict[str, Any]: 验证规则
        """
        return {
            'phone': {
                'required': True,
                'pattern': self.phone_pattern.pattern,
                'length': 11,
                'description': '11位手机号码'
            },
            'password': {
                'required': True,
                'min_length': self.password_min_length,
                'max_length': self.password_max_length,
                'description': f'{self.password_min_length}-{self.password_max_length}位密码'
            },
            'name': {
                'required': True,
                'min_length': 2,
                'max_length': 20,
                'pattern': r'^[\u4e00-\u9fa5a-zA-Z\s]+$',
                'description': '2-20位中文或英文姓名'
            },
            'status': {
                'required': False,
                'options': [s.value for s in UserStatus],
                'description': '用户状态'
            }
        }
